/*
******************************************************************************
* @file     LibUartFifo.h
* <AUTHOR>
* @brief    This file include MSPM0G3519 UART FIFO Library Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __LIB_UART_FIFO_H__
#define	__LIB_UART_FIFO_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h" 

/* Global define ------------------------------------------------------------*/
/* Global typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t		*pu8FifoStartAddr;	     /* FIFO queue buffer start address */
	uint16_t	u16FifoSize;	         /* FIFO queue buffer size */
	uint16_t	u16FifoPushInPosi;		 /* FIFO queue buffer push in position */
	uint16_t	u16FifoPopOutPosi;		 /* FIFO queue buffer pop out position */
}tLibUartFifoStatus;

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
int8_t LibUartFifoPush(tLibUartFifoStatus* pmUartFifo, uint8_t u8Value);
int8_t LibUartFifoPop(tLibUartFifoStatus* pmUartFifo, uint8_t *pu8Value);
int8_t LibUartFifoGetUsedSize(tLibUartFifoStatus* pmUartFifo, uint16_t *pu16RemainSize);

#ifdef __cplusplus
}
#endif

#endif