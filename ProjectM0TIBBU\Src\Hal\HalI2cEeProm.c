/*
******************************************************************************
* @file     HalI2cEeProm.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "HalI2cEeProm.h"

/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/


/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
/* Local function prototypes ------------------------------------------------*/

static bool IsParmValid(tHalI2cEeProm *pmI2cEeProm)
{
    /* Check Pointer */
    if (pmI2cEeProm == 0 || pmI2cEeProm->pu8DataBuffer == 0)
    {
        return false;
    }

    /* Check Length */
    if (pmI2cEeProm->u16Length == 0)
    {
        return false;
    }

    return true;
}

/* Global function prototypes -----------------------------------------------*/
tFunRetunCode HalI2cEePromBlockWrite(tHalI2cEeProm *pmI2cEeProm)
{
    if (IsParmValid(pmI2cEeProm) == false)
    {
        return (RES_ERROR_INVALID_PARAM);
    }
    
    uint32_t u32ProgramAddr = pmI2cEeProm->u32StartAddress;
    uint32_t u32DeviceAddress = pmI2cEeProm->u32DeviceAddress;
    uint16_t u16Len = pmI2cEeProm->u16Length;
    tHalI2cMasterConfig* mHalI2c2Config =  pmI2cEeProm->pmHalI2cConfig;

    uint8_t u8TxBuf[2 + u16Len];

    u8TxBuf[0] = (uint8_t)((u32ProgramAddr >> 8) & 0xFF); // High Byte
    u8TxBuf[1] = (uint8_t)(u32ProgramAddr & 0xFF);       // Low Byte
    
    for (uint16_t i = 0; i < u16Len; i++)
    {
        u8TxBuf[2 + i] = pmI2cEeProm->pu8DataBuffer[i];
    }

    if (!HalI2cMasterSend(mHalI2c2Config, u32DeviceAddress, u8TxBuf, u16Len + 2))
    {
        return (RES_ERROR_FAIL);
    }

    return (RES_SUCCESS);
}


tFunRetunCode HalI2cEePromBlockRead(tHalI2cEeProm *pmI2cEeProm)
{
    if (IsParmValid(pmI2cEeProm) == false)
    {
        return RES_ERROR_INVALID_PARAM;
    }

    uint32_t u32ReadAddr = pmI2cEeProm->u32StartAddress;
    uint32_t u32DeviceAddress = pmI2cEeProm->u32DeviceAddress;
    uint16_t u16RxLen = pmI2cEeProm->u16Length;
    tHalI2cMasterConfig* mHalI2c2Config =  pmI2cEeProm->pmHalI2cConfig;
    uint8_t u8TxBuf[2] = {0};
    uint8_t u8RxBuf[u16RxLen];

    u8TxBuf[0] = (uint8_t)((u32ReadAddr >> 8) & 0xFF); // High Byte
    u8TxBuf[1] = (uint8_t)(u32ReadAddr & 0xFF);       // Low Byte

   HalI2cMasterSend(mHalI2c2Config, u32DeviceAddress, u8TxBuf, 2);

    if (HalI2cMasterRead(mHalI2c2Config, u32DeviceAddress, u8RxBuf, u16RxLen))
    {
        return (RES_ERROR_FAIL);
    }

    for (uint16_t i = 0; i < u16RxLen; i++)
    {
        pmI2cEeProm->pu8DataBuffer[i] = u8RxBuf[i];
    }
  
    return RES_SUCCESS;
}


#define HAL_I2C_EEPROM_TEST_PROGRAM_ADDR (0x0010)
static tHalI2cEeProm gmHalI2cEePromTestWrite = {0};
static tHalI2cEeProm gmHalI2cEePromTestRead = {0};
static tHalI2cMasterConfig gmHalI2c2Config ;

static void HalI2cCb(eTypeHalI2cMasterEvent eI2cEvtt , uint8_t *u8RxData)
{

}

void HalI2cEePromExample(void)
{
    gmHalI2c2Config.eChannel = kHAL_I2C2;
    gmHalI2c2Config.eFreq = kHAL_I2C_FREQUENCY_400k;
    gmHalI2c2Config.fpI2cEvent = HalI2cCb;
    HalI2cMasterInit(&gmHalI2c2Config); 

    uint8_t u8TxBuf[10] = {0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB};
    uint8_t u8RxBuf[64] = {0};

    gmHalI2cEePromTestWrite.pmHalI2cConfig = &gmHalI2c2Config;
    gmHalI2cEePromTestWrite.u32DeviceAddress = 0x50;
    gmHalI2cEePromTestWrite.u32StartAddress = HAL_I2C_EEPROM_TEST_PROGRAM_ADDR;
    gmHalI2cEePromTestWrite.u16Length = 10;
    gmHalI2cEePromTestWrite.pu8DataBuffer = u8TxBuf;

    gmHalI2cEePromTestRead.pmHalI2cConfig = &gmHalI2c2Config;
    gmHalI2cEePromTestRead.u32DeviceAddress = 0x50;
    gmHalI2cEePromTestRead.u32StartAddress = HAL_I2C_EEPROM_TEST_PROGRAM_ADDR;
    gmHalI2cEePromTestRead.u16Length = 64;
    gmHalI2cEePromTestRead.pu8DataBuffer = u8RxBuf;

    HalI2cEePromBlockWrite(&gmHalI2cEePromTestWrite);
    delay_cycles(1600000 * 10);
    HalI2cEePromBlockRead(&gmHalI2cEePromTestRead);
    
}

