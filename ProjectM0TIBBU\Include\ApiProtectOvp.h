/*
******************************************************************************
* @file     ApiProtectOvp.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

#ifndef _API_PROTECT_OVP_H_
#define _API_PROTECT_OVP_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "ApiProtect.h"
#include "LibFunctionPointerRegister.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Public define ------------------------------------------------------------*/
/* Public typedef -----------------------------------------------------------*/

/* Public macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
void ApiProtectOvpOpen(tfpApiProtectEvtHandler fpEvtHandler);
void ApiProtectOvpPfHandler(void);
uint8_t ApiProtectOvpHandler(uint8_t u8ProtectLevel);
uint8_t	ApiProtectOvpGetFlag(uint16_t u16CellIndex);
uint8_t	ApiProtectOvpPfGetFlag(void);
void ApiProtectOvpPfClean(void);

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */


#ifdef __cplusplus
}
#endif


#endif /* _API_PROTECT_OVP_H_ */


/************************ (C) COPYRIGHT *****END OF FILE****/    


