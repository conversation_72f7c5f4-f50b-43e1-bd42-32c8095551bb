/*
******************************************************************************
* @file     ApiProtectDocp.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "HalAfe.h"
#include "ApiProtect.h"
#include "LibSysPar.h"

void AppSerialUartSendMessage(char *str);

/* Private define ------------------------------------------------------------*/
#define API_PROTECT_DOCP_GET_PAR(u8ProtectLevel, mProtectPar)   LibSysParGetDocpPar(u8ProtectLevel, mProtectPar)
#define API_PROTECT_DOCP_GET_LEVEL(u8ProtectLevel, mProtectFlagValue)   ApiProtectGetLevelMask(u8ProtectLevel, mProtectFlagValue)
#define API_PROTECT_DOCP_GET_CURRENT_VALUE(u8Index)     HalAfeGetCurrentValue(u8Index)

/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t	u8Flag;
	uint8_t	u8SetCount[API_PROTECT_LEVEL];
	uint8_t	u8ReleaseCount[API_PROTECT_LEVEL];
	tfpApiProtectEvtHandler  fpEvtHandler;
}tDocpProtect;

static tDocpProtect	gmDocpProtect={0};
static bool gbDotpEnable = 0;
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
uint8_t	ApiProtectDocpGetFlag(void)
{
	return gmDocpProtect.u8Flag;
}

uint8_t ApiProtectDocpHandler(uint8_t u8ProtectLevel)
{
	uint16_t		u16Value;
	int32_t		    i32CurrentValue;
	tProtectFlagValue	mProtectFlagValue;
	tScuProtectPar		mProtectPar;
	
	if (gbDotpEnable == 0)
	{
		return 1;
	}
	
	API_PROTECT_DOCP_GET_PAR(u8ProtectLevel, &mProtectPar);
	API_PROTECT_DOCP_GET_LEVEL(u8ProtectLevel, &mProtectFlagValue);

	i32CurrentValue = abs(API_PROTECT_DOCP_GET_CURRENT_VALUE(0)) / 1000;
	//i32CurrentValue = 0;//abs(API_PROTECT_DOCP_GET_CURRENT_VALUE(P_CURRENT)) / 1000;

	//if(appGaugeGetCurrentMode() != APP_SCU_GAUGE_DISCHARGE_MODE)
	//	i32CurrentValue = 0;

	if(i32CurrentValue > mProtectPar.mSetValue.u32Value && mProtectPar.mSTime.u32Value != 0)
	{
		if((gmDocpProtect.u8Flag & mProtectFlagValue.u8Mask) == 0)
		{
			gmDocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
			gmDocpProtect.u8Flag |= mProtectFlagValue.u8Setting;
			gmDocpProtect.u8SetCount[u8ProtectLevel] = 1;			
		}
		else if((gmDocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
		{
			gmDocpProtect.u8SetCount[u8ProtectLevel]++;
			if(gmDocpProtect.u8SetCount[u8ProtectLevel] >= mProtectPar.mSTime.u32Value)
			{
				if(gmDocpProtect.fpEvtHandler)
				{
					u16Value = i32CurrentValue;
					gmDocpProtect.fpEvtHandler(0, kAPI_PROTECT_DOCP_L1_SET + u8ProtectLevel, &u16Value);
				}
				gmDocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
				gmDocpProtect.u8Flag |= mProtectFlagValue.u8Setted;
				gmDocpProtect.u8SetCount[u8ProtectLevel] = 0;
			}
		}
	}
	else if((gmDocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
	{
		gmDocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
	}	
	//-----------------------------------------
	//	Level1	Release
	if(i32CurrentValue < mProtectPar.mRelValue.u32Value && mProtectPar.mRTime.u32Value)
	{
		if((gmDocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setted)
		{
			gmDocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
			gmDocpProtect.u8Flag |= mProtectFlagValue.u8Releasing;
			gmDocpProtect.u8ReleaseCount[u8ProtectLevel] = 1;		
		}
		else if((gmDocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
		{
			gmDocpProtect.u8ReleaseCount[u8ProtectLevel] ++;
			if(gmDocpProtect.u8ReleaseCount[u8ProtectLevel]  >=  mProtectPar.mRTime.u32Value)
			{
				if(gmDocpProtect.fpEvtHandler)
				{
					u16Value = i32CurrentValue;
					gmDocpProtect.fpEvtHandler(0, kAPI_PROTECT_DOCP_L1_RELEASE + u8ProtectLevel, &u16Value);	
				}	
				gmDocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
				gmDocpProtect.u8ReleaseCount[u8ProtectLevel] = 0;
			}		
		}
	}
	else if((gmDocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
	{
		gmDocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
		gmDocpProtect.u8Flag |= mProtectFlagValue.u8Setted;
 	}	
	
	return 1;
}

void ApiProtectDocpOpen(tfpApiProtectEvtHandler fpEvtHandler)
{
	gbDotpEnable = 1;
	
	gmDocpProtect.fpEvtHandler = fpEvtHandler;
}

/************************ (C) COPYRIGHT *****END OF FILE****/    

