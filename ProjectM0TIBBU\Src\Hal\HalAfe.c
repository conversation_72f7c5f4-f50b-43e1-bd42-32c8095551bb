/*
******************************************************************************
* @file     <PERSON><PERSON>fe.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "HalMcuPeripheralConfig.h"
#include "LibSoftwareTimerHandler.h"
#include "Define.h"
#include "HalAfeInternal.h"
#include "HalAfe.h"
#include "LibSysPar.h"
#include "LibNtc.h"

/* Private define ------------------------------------------------------------*/
#define HAL_AFE_IS_IN_SIMU_MODE	(0)			//appProjectIsInSimuMode()
#define HAL_AFE_IS_PRE_RELAY_ON()  (1)   //halBspIsPreDischargeRelayOn

#define HAL_AFE_MAX_MIN_CHECK_NUM_PER_TIME   (20)

/* Private macro -------------------------------------------------------------*/

/* Private typedef -----------------------------------------------------------*/
typedef void (* tfpHalAfeCheckFunTable)(void);

typedef struct
{
	uint16_t u16CellVoltage[MAX_CELL_NUMBER + 4];
	uint16_t u16NtcVoltage[MAX_NTC_NUMBER + 4];
	uint16_t u16CellBusbarVoltage[MAX_CELL_BUSBAR_NUMBER];
	uint16_t u16NtcBusbarVoltage[MAX_NTC_BUSBAR_NUMBER];
	uint16_t u16NtcAmbientVoltage[MAX_NTC_AMBIENT_NUMBER];
	uint16_t u16NtcOtherVoltage[MAX_NTC_OTHER_NUMBER];
	int32_t  i32CurrentValue[2];
	int32_t  i32CurrentAdcValue[2];
	uint32_t u32VBat[3]; // 0 Vb sum of cell 1:Vb Exit 2:Vb Cal
	int32_t  i32VbatAdcValue[2];
	uint16_t u16MaxCellVoltage;
	uint16_t u16MinCellVoltage;	
	uint16_t u16CheckingIndex;
	
	uint16_t u16TmpMaxPosition;
	uint16_t u16TmpMinPosition;
	uint16_t u16TmpMaxValue;
	uint16_t u16TmpMinValue;
	uint16_t u16MaxTempNtcVoltage;
	uint16_t u16MinTempNtcVoltage;
	uint16_t u16MaxNtcTemp;
	uint16_t u16MinNtcTemp;
	struct
	{
		uint8_t u8Bmu;
		uint8_t u8Channel;
		uint16_t u16Posi;
	} mMaxVPosition;
	struct
	{
		uint8_t u8Bmu;
		uint8_t u8Channel;
		uint16_t u16Posi;
	} mMinVPosition;
	struct
	{
		uint8_t u8Bmu;
		uint8_t u8Channel;
		uint16_t u16Posi;
	} mMaxTPosition;
	struct
	{
		uint8_t u8Bmu;
		uint8_t u8Channel;
		uint16_t u16Posi;
	} mMinTPosition;
	struct
	{
		uint32_t u32BalanceData[64];
		uint8_t u8Flag_BalanceOn;
	} mBalance;
} tAfeBuffer;

/* Public variables ---------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/
static uint8_t  gu8HalAfeCheckTableIndex;
static tAfeBuffer gmAfeBuffer;
/* Private function prototypes -----------------------------------------------*/
static void HalAfeStartCheckProcess(void);
static void HalAfeStartCheckCellVoltageProcess(void);
static void HalAfeUpdateMinMaxCellVoltage(void);
static void HalAfeConverterMaxCellVoltagePosition(void);
static void HalAfeConverterMinCellVoltagePosition(void);
static void HalAfeStartCheckTempProcess(void);
static void HalAfeUpdateMinMaxNtcTempVoltage(void);
static void HalAfeConverterMaxNtcPosition(void);
static void HalAfeConverterMinNtcPosition(void);

static void HalAfeEndCheckProcess(void);


const tfpHalAfeCheckFunTable mfpHalAfeCheckFunTable[]={
    HalAfeStartCheckProcess,
    HalAfeStartCheckCellVoltageProcess,
    HalAfeUpdateMinMaxCellVoltage,
    HalAfeConverterMaxCellVoltagePosition,
    HalAfeConverterMinCellVoltagePosition,
    HalAfeStartCheckTempProcess,
    HalAfeUpdateMinMaxNtcTempVoltage,
    HalAfeConverterMaxNtcPosition,
    HalAfeConverterMinNtcPosition,
    HalAfeEndCheckProcess    	    	
};

static uint8_t HalAfeIsAdcValid(uint16_t u16LogicIndex)
{
    return 1;
}

static void HalAfeGetCellPhyPosition(uint16_t u16Posi, uint8_t *pu8RetBmu, uint8_t *pu8RetCh)
{
	uint16_t u16Index = 0;
	uint8_t u8Bmu;
	uint32_t u32Flag;
	uint8_t i;
	for (u8Bmu = 0; u8Bmu < HAL_AFE_GET_AFE_NUM(); u8Bmu++)
	{
		u32Flag = HAL_AFE_GET_CELL_FLAG(u8Bmu);
		for (i = 0; i < 32; i++)
		{
			if (u32Flag & 0x01)
			{
				if (u16Index == u16Posi)
				{
					*pu8RetBmu = u8Bmu + 1;
					*pu8RetCh = i + 1;
					return;
				}
				u16Index++;
			}
			u32Flag >>= 1;
		}
	}
}

static void HalAfeGetNtcPhyPosition(uint16_t u16Posi, uint8_t *pu8RetBmu, uint8_t *pu8RetCh)
{
	uint16_t u16Index = 0;
	uint8_t u8Bmu;
	uint32_t u32Flag;
	uint8_t i;
	for (u8Bmu = 0; u8Bmu < HAL_AFE_GET_AFE_NUM(); u8Bmu++)
	{
		u32Flag = HAL_AFE_GET_NTC_FLAG(u8Bmu);
		for (i = 0; i < 32; i++)
		{
			if (u32Flag & 0x01)
			{
				if (u16Index == u16Posi)
				{
					*pu8RetBmu = u8Bmu + 1;
					*pu8RetCh = i + 1;
					return;
				}
				u16Index++;
			}
			u32Flag >>= 1;
		}
	}
}
static void HalAfeGotoNextCheckIndex(void)
{
    gu8HalAfeCheckTableIndex++;   
}
static void HalAfeStartCheckProcess(void)
{
    gu8HalAfeCheckTableIndex = 1;    
}

static void HalAfeEndCheckProcess(void)
{
    gu8HalAfeCheckTableIndex = 0;    
}

static void HalAfeStartCheckCellVoltageProcess(void)
{
   	gmAfeBuffer.u16TmpMaxPosition = 0;
	gmAfeBuffer.u16TmpMinPosition = 0;
	gmAfeBuffer.u16TmpMaxValue = 0;
	gmAfeBuffer.u16TmpMinValue = 0xffff;
	gmAfeBuffer.u16CheckingIndex = 0;
	HalAfeGotoNextCheckIndex();
}
static void HalAfeUpdateMinMaxCellVoltage(void)
{
	uint16_t    u16CellVoltage;
	uint8_t     u8CheckCount = 0;

	while(1)
	{
		if(HalAfeIsAdcValid(gmAfeBuffer.u16CheckingIndex) != 0)
		{
    		u16CellVoltage = HalAfeGetCellVoltage(gmAfeBuffer.u16CheckingIndex);
	    	if (u16CellVoltage > gmAfeBuffer.u16TmpMaxValue)
		    {
    			gmAfeBuffer.u16TmpMaxValue = u16CellVoltage;
	    		gmAfeBuffer.u16TmpMaxPosition = gmAfeBuffer.u16CheckingIndex;
		    }
    		if (u16CellVoltage < gmAfeBuffer.u16TmpMinValue)
	    	{
		    	gmAfeBuffer.u16TmpMinValue = u16CellVoltage;
			    gmAfeBuffer.u16TmpMinPosition = gmAfeBuffer.u16CheckingIndex;
		    }
        }
		gmAfeBuffer.u16CheckingIndex++;
		if(gmAfeBuffer.u16CheckingIndex >= HAL_AFE_GET_CELL_NUM())
		{
		    gmAfeBuffer.u16MaxCellVoltage = gmAfeBuffer.u16TmpMaxValue;
	        gmAfeBuffer.u16MinCellVoltage = gmAfeBuffer.u16TmpMinValue;
		   	gmAfeBuffer.mMaxVPosition.u16Posi = gmAfeBuffer.u16TmpMaxPosition;
	        gmAfeBuffer.mMinVPosition.u16Posi = gmAfeBuffer.u16TmpMinPosition;
		    HalAfeGotoNextCheckIndex();
		    break;
        }		  
		u8CheckCount++;
		if(u8CheckCount >= HAL_AFE_MAX_MIN_CHECK_NUM_PER_TIME)
		    break;		
	}
}

static void HalAfeConverterMaxCellVoltagePosition(void)
{
	HalAfeGetCellPhyPosition(gmAfeBuffer.mMaxVPosition.u16Posi,
							 &gmAfeBuffer.mMaxVPosition.u8Bmu,
							 &gmAfeBuffer.mMaxVPosition.u8Channel);
    HalAfeGotoNextCheckIndex();							
}
static void HalAfeConverterMinCellVoltagePosition(void)
{
   	HalAfeGetCellPhyPosition(gmAfeBuffer.mMinVPosition.u16Posi,
							 &gmAfeBuffer.mMinVPosition.u8Bmu,
							 &gmAfeBuffer.mMinVPosition.u8Channel);
    HalAfeGotoNextCheckIndex();
}

static void HalAfeStartCheckTempProcess(void)
{
   	gmAfeBuffer.u16TmpMaxPosition = 0;
	gmAfeBuffer.u16TmpMinPosition = 0;
	gmAfeBuffer.u16TmpMaxValue = 0xffff;
	gmAfeBuffer.u16TmpMinValue = 0;
	gmAfeBuffer.u16CheckingIndex = 0;
	HalAfeGotoNextCheckIndex();
}
static void HalAfeUpdateMinMaxNtcTempVoltage(void)
{
	uint16_t    u16NtcVoltage;
	uint8_t     u8CheckCount = 0;

	while(1)
	{
		u16NtcVoltage = HalAfeGetNtcVoltage(gmAfeBuffer.u16CheckingIndex);

		if (u16NtcVoltage <= NTC_ADC_UPPER_BOUND)
		{
    		if (u16NtcVoltage < gmAfeBuffer.u16TmpMaxValue)
	    	{
		    	gmAfeBuffer.u16TmpMaxValue = u16NtcVoltage;
			    gmAfeBuffer.u16TmpMaxPosition = gmAfeBuffer.u16CheckingIndex;
		    }
    		if (u16NtcVoltage > gmAfeBuffer.u16TmpMinValue)
	    	{
		    	gmAfeBuffer.u16TmpMinValue = u16NtcVoltage;
			    gmAfeBuffer.u16TmpMinPosition = gmAfeBuffer.u16CheckingIndex;
		    }
        }
        gmAfeBuffer.u16CheckingIndex++;
        if(gmAfeBuffer.u16CheckingIndex >= HAL_AFE_GET_NTC_NUM())
        {
   		    gmAfeBuffer.u16MaxTempNtcVoltage = gmAfeBuffer.u16TmpMaxValue;
	        gmAfeBuffer.u16MinTempNtcVoltage = gmAfeBuffer.u16TmpMinValue;
		   	gmAfeBuffer.mMaxTPosition.u16Posi = gmAfeBuffer.u16TmpMaxPosition;
	        gmAfeBuffer.mMinTPosition.u16Posi = gmAfeBuffer.u16TmpMinPosition;
           	gmAfeBuffer.u16MaxNtcTemp = HAL_AFE_CVT_VOL_TO_TEMP(gmAfeBuffer.u16MaxTempNtcVoltage);
        	gmAfeBuffer.u16MinNtcTemp = HAL_AFE_CVT_VOL_TO_TEMP(gmAfeBuffer.u16MinTempNtcVoltage);

            HalAfeGotoNextCheckIndex();
            return;
        }
        u8CheckCount++;
        if(u8CheckCount >= HAL_AFE_MAX_MIN_CHECK_NUM_PER_TIME)
            break;
	}
}

static void HalAfeConverterMaxNtcPosition(void)
{
   	HalAfeGetNtcPhyPosition(gmAfeBuffer.mMaxTPosition.u16Posi,
						    &gmAfeBuffer.mMaxTPosition.u8Bmu,
						    &gmAfeBuffer.mMaxTPosition.u8Channel);
    HalAfeGotoNextCheckIndex();						    
}
static void HalAfeConverterMinNtcPosition(void)
{
   	HalAfeGetNtcPhyPosition(gmAfeBuffer.mMinTPosition.u16Posi,
						    &gmAfeBuffer.mMinTPosition.u8Bmu,
						    &gmAfeBuffer.mMinTPosition.u8Channel);
    HalAfeGotoNextCheckIndex();						    
}


static void HalAfeSwTimerHandler(__far void *pvDest, uint16_t u16Event, void *pvData)
{
    if (u16Event & kLIB_SW_TIMER_EVT_1_MS)
    {       
        if(gu8HalAfeCheckTableIndex != 0)
        {
            mfpHalAfeCheckFunTable[gu8HalAfeCheckTableIndex]();
            DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_8);
        }
    }
    if (u16Event & kLIB_SW_TIMER_EVT_500_MS)
    {
        if(gu8HalAfeCheckTableIndex == 0)
            HalAfeStartCheckProcess();
    }
}
/* Public function prototypes -----------------------------------------------*/
void HalAfeSetNtcOtherFunctionVoltage(uint8_t u8Fun, uint16_t u16Ntcs, uint16_t u16Voltage)
{
	switch (u8Fun)
	{
	case kGPIO_BUSBAR:
		if (u16Ntcs >= MAX_NTC_BUSBAR_NUMBER)
			break;
		gmAfeBuffer.u16NtcBusbarVoltage[u16Ntcs] = u16Voltage;
		break;
	case kGPIO_AMBIENT:
		if (u16Ntcs >= MAX_NTC_AMBIENT_NUMBER)
			break;
		gmAfeBuffer.u16NtcAmbientVoltage[u16Ntcs] = u16Voltage;
		break;
	case kGPIO_OTHER:
		if (u16Ntcs >= MAX_NTC_OTHER_NUMBER)
			break;
		gmAfeBuffer.u16NtcOtherVoltage[u16Ntcs] = u16Voltage;
		break;
	}
}

uint32_t HalAfeGetPhysicalBalancePosition(uint8_t u8Afeindex)
{
	return gmAfeBuffer.mBalance.u32BalanceData[u8Afeindex];
}

void HalAfeSetPhysicalBalancePosition(uint8_t u8Afeindex, uint32_t u32Position)
{
	gmAfeBuffer.mBalance.u32BalanceData[u8Afeindex] = u32Position;
}

uint8_t HalAfeGetBalanceOnFlag(void)
{
	return gmAfeBuffer.mBalance.u8Flag_BalanceOn;
}

void HalAfeSetBalanceOnFlag(uint8_t u8Onflag)
{
	gmAfeBuffer.mBalance.u8Flag_BalanceOn = u8Onflag;
}

void HalAfeCalVbatFromCellVoltage(void)
{
	uint16_t u16Cell;
	uint32_t u32Vbat = 0;
	if (HAL_AFE_IS_IN_SIMU_MODE == 0)
		return;

	for (u16Cell = 0; u16Cell < HAL_AFE_GET_CELL_NUM(); u16Cell++)
	{
		u32Vbat += HalAfeGetCellVoltage(u16Cell);
	}
	HalAfeSetVBatVoltage(kAFE_VBAT_INDEX, u32Vbat / 10);
}
int32_t HalAfeGetCurrentAdcValue(uint8_t u8CurrentIndex)
{
	if (u8CurrentIndex >= 2)
		return 0;
	return gmAfeBuffer.i32CurrentAdcValue[u8CurrentIndex];
}

void HalAfeSetCurrentAdcValue(uint8_t u8CurrentIndex, int32_t _i32AdcValue)
{
#define _I_ADC_AVG_TIMES 4
	static int32_t _i32AdcAvgBuf[2][_I_ADC_AVG_TIMES] = {0};
	static uint8_t u8Index[2] = {0xff, 0xff};
	uint8_t i;
	int32_t _i32Avg;

	if (u8CurrentIndex >= 2)
		return;

	if (u8Index[u8CurrentIndex] == 0xff)
	{
		for (i = 0; i < _I_ADC_AVG_TIMES; i++)
			_i32AdcAvgBuf[u8CurrentIndex][i] = _i32AdcValue;
		u8Index[u8CurrentIndex] = 0;
	}
	if (u8Index[u8CurrentIndex] >= _I_ADC_AVG_TIMES)
		u8Index[u8CurrentIndex] = 0;
	_i32AdcAvgBuf[u8CurrentIndex][u8Index[u8CurrentIndex]] = _i32AdcValue;
	u8Index[u8CurrentIndex]++;

	_i32Avg = 0;
	for (i = 0; i < _I_ADC_AVG_TIMES; i++)
		_i32Avg += _i32AdcAvgBuf[u8CurrentIndex][i];
	_i32Avg /= _I_ADC_AVG_TIMES;
	_i32AdcValue = _i32Avg;

	gmAfeBuffer.i32CurrentAdcValue[u8CurrentIndex] = _i32AdcValue;
}
int32_t HalAfeGetVBatAdcValue(uint8_t u8VbIndex)
{
	if (u8VbIndex >= 2)
		return 0;
	return gmAfeBuffer.i32VbatAdcValue[u8VbIndex];
}
void HalAfeSetVBatAdcValue(uint8_t u8VbIndex, int32_t _i32AdcValue)
{
	if (u8VbIndex >= 2)
		return;
	gmAfeBuffer.i32VbatAdcValue[u8VbIndex] = _i32AdcValue;
}


void HalAfeSetCellVoltage(uint16_t u16Cells, uint16_t u16Voltage)
{
	gmAfeBuffer.u16CellVoltage[u16Cells] = u16Voltage;
}
void HalAfeSetNtcVoltage(uint16_t u16Ntcs, uint16_t u16Voltage)
{
    if(u16Ntcs >= MAX_NTC_NUMBER)
        return;
	gmAfeBuffer.u16NtcVoltage[u16Ntcs] = u16Voltage;
}

uint16_t HalAfeGetCellVoltage(uint16_t u16CellIndex)
{
	return gmAfeBuffer.u16CellVoltage[u16CellIndex];
}

uint16_t HalAfeGetNtcVoltage(uint16_t u16NtcIndex)
{
    if(u16NtcIndex >= MAX_NTC_NUMBER)
        return 0;
    return gmAfeBuffer.u16NtcVoltage[u16NtcIndex];
}

int32_t HalAfeGetCurrentValue(uint8_t u8Index)
{
	return gmAfeBuffer.i32CurrentValue[u8Index];
}
void HalAfeSetCurrentValue(uint8_t u8Index, int32_t i32Current)
{
	//if (HAL_AFE_IS_PRE_RELAY_ON() != 0)
	//	i32Current = 0;
	gmAfeBuffer.i32CurrentValue[u8Index] = i32Current;
}

uint32_t HalAfeGetVBatVoltage(uint8_t u8Index)
{
	if (u8Index > kAFE_VBCAL_INDEX)
		return 0;
	return gmAfeBuffer.u32VBat[u8Index];
}
void HalAfeSetVBatVoltage(uint8_t u8Index, uint32_t u32Voltage)
{
	if (u8Index > kAFE_VBCAL_INDEX)
		return;
	gmAfeBuffer.u32VBat[u8Index] = u32Voltage;
}

uint16_t HalAfeGetMaxCellVoltage(uint8_t *pu8Bmu, uint8_t *pu8Channel, uint16_t *pu16Posi)
{
	if (pu8Bmu != 0)
		*pu8Bmu = gmAfeBuffer.mMaxVPosition.u8Bmu;
	if (pu8Channel != 0)
		*pu8Channel = gmAfeBuffer.mMaxVPosition.u8Channel;
	if (pu16Posi != 0)
		*pu16Posi = gmAfeBuffer.mMaxVPosition.u16Posi;

	return gmAfeBuffer.u16MaxCellVoltage;
}

uint16_t HalAfeGetMinCellVoltage(uint8_t *pu8Bmu, uint8_t *pu8Channel, uint16_t *pu16Posi)
{
	if (pu8Bmu != 0)
		*pu8Bmu = gmAfeBuffer.mMinVPosition.u8Bmu;
	if (pu8Channel != 0)
		*pu8Channel = gmAfeBuffer.mMinVPosition.u8Channel;
	if (pu16Posi != 0)
		*pu16Posi = gmAfeBuffer.mMinVPosition.u16Posi;
	return gmAfeBuffer.u16MinCellVoltage;
}

uint16_t HalAfeGetMinNtcTemp(uint8_t *pu8Bmu, uint8_t *pu8Posi)
{
	if (pu8Bmu != 0)
		*pu8Bmu = gmAfeBuffer.mMinTPosition.u8Bmu;
	if (pu8Posi != 0)
		*pu8Posi = gmAfeBuffer.mMinTPosition.u8Channel;

	return gmAfeBuffer.u16MinNtcTemp;
}
uint16_t HalAfeGetMaxNtcTemp(uint8_t *pu8Bmu, uint8_t *pu8Posi)
{
	if (pu8Bmu != 0)
		*pu8Bmu = gmAfeBuffer.mMaxTPosition.u8Bmu;
	if (pu8Posi != 0)
		*pu8Posi = gmAfeBuffer.mMaxTPosition.u8Channel;

	return gmAfeBuffer.u16MaxNtcTemp;
}

void HalAfeSetCellBusbarVoltage(uint8_t u8Index, uint16_t u16Voltage)
{
	if (u8Index >= MAX_CELL_BUSBAR_NUMBER)
		return;
	gmAfeBuffer.u16CellBusbarVoltage[u8Index] = u16Voltage;
}
uint16_t HalAfeGetCellBusbarVoltage(uint8_t u8Index)
{
	if (u8Index >= MAX_CELL_BUSBAR_NUMBER)
		return 0;
	return gmAfeBuffer.u16CellBusbarVoltage[u8Index];
}

void HalAfeSetNtcBusbarVoltage(uint8_t u8Index, uint16_t u16Voltage)
{
	if(u8Index >= MAX_NTC_BUSBAR_NUMBER)
		return;
	gmAfeBuffer.u16NtcBusbarVoltage[u8Index] = u16Voltage;
}

uint16_t HalAfeGetNtcBusbarVoltage(uint8_t u8Index)
{
	if(u8Index >= MAX_NTC_BUSBAR_NUMBER)
		return 0;
	return gmAfeBuffer.u16NtcBusbarVoltage[u8Index];
}

void HalAfeSetNtcAmbientVoltage(uint8_t u8Index, uint16_t u16Voltage)
{
	if(u8Index >= MAX_NTC_AMBIENT_NUMBER)
		return;
	gmAfeBuffer.u16NtcAmbientVoltage[u8Index] = u16Voltage;
}

uint16_t HalAfeGetNtcAmbientVoltage(uint8_t u8Index)
{
	if(u8Index >= MAX_NTC_AMBIENT_NUMBER)
		return 0;
	return gmAfeBuffer.u16NtcAmbientVoltage[u8Index];
}

void HalAfeSetNtcOtherVoltage(uint8_t u8Index, uint16_t u16Voltage)
{
	if(u8Index >= MAX_NTC_OTHER_NUMBER)
		return;
	gmAfeBuffer.u16NtcOtherVoltage[u8Index] = u16Voltage;
}
uint16_t HalAfeGetNtcOtherVoltage(uint8_t u8Index)
{
	if(u8Index >= MAX_NTC_OTHER_NUMBER)
		return 0;
	return gmAfeBuffer.u16NtcOtherVoltage[u8Index];
}
void HalAfeHandlerOpen(void)
{
	LibSoftwareTimerHandlerOpen(HalAfeSwTimerHandler, 0);
}

/************************ (C) COPYRIGHT *****END OF FILE****/    


