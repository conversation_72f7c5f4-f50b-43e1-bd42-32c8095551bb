/*
******************************************************************************
* @file     ApiProtectUvp.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

#ifndef _API_PROTECT_UVP_H_
#define _API_PROTECT_UVP_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "ApiProtect.h"
#include "LibFunctionPointerRegister.h"
#ifdef __cplusplus
extern "C" {
#endif

/* Public define ------------------------------------------------------------*/
/* Public typedef -----------------------------------------------------------*/
/* Public macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
void ApiProtectUvpOpen(tfpApiProtectEvtHandler fpEvtHandler);
uint8_t ApiProtectUvpHandler(uint8_t u8ProtectLevel);
void ApiProtectUvpPfHandler(void);
uint8_t	ApiProtectUvpGetFlag(uint16_t u16CellIndex);
uint8_t	ApiProtectUvpPfGetFlag(void);
void ApiProtectUvpPfClean(void);

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */


#ifdef __cplusplus
}
#endif


#endif /* _API_PROTECT_UVP_H_ */


/************************ (C) COPYRIGHT *****END OF FILE****/    


