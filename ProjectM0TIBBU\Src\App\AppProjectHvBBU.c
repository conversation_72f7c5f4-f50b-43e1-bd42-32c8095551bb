/*
******************************************************************************
* @file     AppProjectHvBBU.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdio.h>
#include "Main.h"
#include "LibSoftwareTimerHandler.h"
#include "LibHardwareTimerHandler.h"
#include "HalMcuPeripheralConfig.h"
#include "HalGeneralTimer.h"
#include "LibSysPar.h"
#include "HalAfe.h"
#include "HalEeProm.h"
#include "HalCan.h"
#include "ApiProtect.h"
#include "ApiProtectEvent.h"
#include "AppSerialUartHvBBU.h"
#include "ApiSystemFlag.h"
#include "HalAfeAdbms6832.h"

void AppSerialUartSendMessage(uint8_t *pu8Str);
/* Private define ------------------------------------------------------------*/
#if 1
#define SIM_MODE
#endif

#define	APP_PROJECT_DEBUG_MSG(pc8Msg)		AppSerialUartSendMessage((uint8_t *)pc8Msg)
#define APP_HARDWARE_TIMER_TASK_PERIOD      (1000)


/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
static void AppTemporaryHalTimerHandler(void *pvDest, uint16_t u16Event, void *pvData)
{
    LibSwTimerHardwareTimerCounter(kLIB_SW_TIMER_EVT_HW_1MS, 0);
    LibHardwareTimerHandler();
}

static void AppProjectHwTimerHandler(__far void *pvDest, uint16_t u16Event, void *pvData)
{
    if (u16Event == kLIB_HW_TIMER_EVT_1_MS)
    {
      //  DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_8);
    }
}

static void AppProjectSwTimerHandler(__far void *pvDest, uint16_t u16Event, void *pvData)
{
    DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_14);
    if (u16Event & kLIB_SW_TIMER_EVT_1_MS)
    {
      //  DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_14);
        //DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_8);
        //DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_9);
    }
    if (u16Event & kLIB_SW_TIMER_EVT_10_0_MS)
    {
        //DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_9);
    } 
    if (u16Event & kLIB_SW_TIMER_EVT_10_1_MS)
    {
        //DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_8);
    }
    if (u16Event & kLIB_SW_TIMER_EVT_100_MS)
    {
        DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_9);   
    }
    if (u16Event & kLIB_SW_TIMER_EVT_500_MS)
    {
        DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_8);
    }
    if (u16Event & kLIB_SW_TIMER_EVT_1_S)
    {
        //DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_14);
    }
}
static void AppProjectProtectEventHandler(void *pDest, uint16_t _u16Evt, void *pData)
{
    char    c8Str[100];
    
	switch (_u16Evt)
	{
	case kAPI_PROTECT_OVP_L1_SET:
		APP_PROJECT_DEBUG_MSG("OVP L1 Set");
		break;
	case kAPI_PROTECT_OVP_L2_SET:
		APP_PROJECT_DEBUG_MSG("OVP L2 Set");
		break;
	case kAPI_PROTECT_OVP_L3_SET:
		APP_PROJECT_DEBUG_MSG("OVP L3 Set");
		break;
	case kAPI_PROTECT_OVP_L1_RELEASE:
		APP_PROJECT_DEBUG_MSG("OVP L1 Release");
		break;
	case kAPI_PROTECT_OVP_L2_RELEASE:
		APP_PROJECT_DEBUG_MSG("OVP L2 Release");
		break;
	case kAPI_PROTECT_OVP_L3_RELEASE:
		APP_PROJECT_DEBUG_MSG("OVP L3 Release");
		break;
	case kAPI_PROTECT_OVP_PF:
		APP_PROJECT_DEBUG_MSG("OVP PF Set");
		break;		
		
	case kAPI_PROTECT_UVP_L1_SET:
	    APP_PROJECT_DEBUG_MSG("UVP L1 Set");
		break;
	case kAPI_PROTECT_UVP_L2_SET:
	    APP_PROJECT_DEBUG_MSG("UVP L2 Set");
		break;
	case kAPI_PROTECT_UVP_L3_SET:
	    APP_PROJECT_DEBUG_MSG("UVP L3 Set");
		break;
	case kAPI_PROTECT_UVP_L1_RELEASE:
	    APP_PROJECT_DEBUG_MSG("UVP L1 Release");
		break;
	case kAPI_PROTECT_UVP_L2_RELEASE:
	    APP_PROJECT_DEBUG_MSG("UVP L2 Release");
		break;
	case kAPI_PROTECT_UVP_L3_RELEASE:
	    APP_PROJECT_DEBUG_MSG("UVP L3 Release");
		break;
	case kAPI_PROTECT_UVP_PF:
	    APP_PROJECT_DEBUG_MSG("UVP PF Set");
		break;
		
	case kAPI_PROTECT_CDVP_L1_SET:
		break;
	case kAPI_PROTECT_CDVP_L2_SET:
		break;
	case kAPI_PROTECT_CDVP_L3_SET:
		break;
	case kAPI_PROTECT_CDVP_PF:
		break;
	case kAPI_PROTECT_CDVP_L1_RELEASE:
		break;
	case kAPI_PROTECT_CDVP_L2_RELEASE:
		break;
	case kAPI_PROTECT_CDVP_L3_RELEASE:
		break;
	case kAPI_PROTECT_MDVP_L1_SET:
		break;
	case kAPI_PROTECT_MDVP_L2_SET:
		break;
	case kAPI_PROTECT_MDVP_L3_SET:
		break;
	case kAPI_PROTECT_MDVP_L1_RELEASE:
		break;
	case kAPI_PROTECT_MDVP_L2_RELEASE:
		break;
	case kAPI_PROTECT_MDVP_L3_RELEASE:
		break;
	case kAPI_PROTECT_MDVP_PF:
		break;
	case kAPI_PROTECT_CDTP_L1_SET:
		break;
	case kAPI_PROTECT_CDTP_L2_SET:
		break;
	case kAPI_PROTECT_CDTP_L3_SET:
		break;
	case kAPI_PROTECT_CDTP_L1_RELEASE:
		break;
	case kAPI_PROTECT_CDTP_L2_RELEASE:
		break;
	case kAPI_PROTECT_CDTP_L3_RELEASE:
		break;
	case kAPI_PROTECT_MDTP_L1_SET:
		break;
	case kAPI_PROTECT_MDTP_L2_SET:
		break;
	case kAPI_PROTECT_MDTP_L3_SET:
		break;
	case kAPI_PROTECT_MDTP_L1_RELEASE:
		break;
	case kAPI_PROTECT_MDTP_L2_RELEASE:
		break;
	case kAPI_PROTECT_MDTP_L3_RELEASE:
		break;
	case kAPI_PROTECT_COTP_L1_SET:
	    sprintf(c8Str, "COTP L1 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_COTP_L2_SET:
	    sprintf(c8Str, "COTP L2 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_COTP_L3_SET:
	    sprintf(c8Str, "COTP L3 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_COTP_L4_SET:
	    APP_PROJECT_DEBUG_MSG("COTP L4 Set");
		break;
	case kAPI_PROTECT_COTP_L1_RELEASE:
	    sprintf(c8Str, "COTP L1 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_COTP_L2_RELEASE:
	    sprintf(c8Str, "COTP L2 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_COTP_L3_RELEASE:
	    sprintf(c8Str, "COTP L3 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_COTP_L4_RELEASE:
		break;
	case kAPI_PROTECT_CUTP_L1_SET:
	    sprintf(c8Str, "CUTP L1 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_CUTP_L2_SET:
	    sprintf(c8Str, "CUTP L2 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_CUTP_L3_SET:
	    sprintf(c8Str, "CUTP L3 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_CUTP_L4_SET:
		break;
	case kAPI_PROTECT_CUTP_L1_RELEASE:
	    sprintf(c8Str, "CUTP L1 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_CUTP_L2_RELEASE:
	    sprintf(c8Str, "CUTP L2 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_CUTP_L3_RELEASE:
	    sprintf(c8Str, "CUTP L3 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_CUTP_L4_RELEASE:
		break;
	case kAPI_PROTECT_DOTP_L1_SET:
	    sprintf(c8Str, "DOTP L1 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DOTP_L2_SET:
	    sprintf(c8Str, "DOTP L2 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DOTP_L3_SET:
	    sprintf(c8Str, "DOTP L3 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DOTP_L1_RELEASE:
	    sprintf(c8Str, "DOTP L1 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DOTP_L2_RELEASE:
	    sprintf(c8Str, "DOTP L2 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DOTP_L3_RELEASE:
	    sprintf(c8Str, "DOTP L3 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DUTP_L1_SET:
	    sprintf(c8Str, "DUTP L1 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DUTP_L2_SET:
	    sprintf(c8Str, "DUTP L2 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DUTP_L3_SET:
	    sprintf(c8Str, "DUTP L3 Set %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DUTP_L1_RELEASE:
	    sprintf(c8Str, "DUTP L1 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DUTP_L2_RELEASE:
	    sprintf(c8Str, "DUTP L2 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DUTP_L3_RELEASE:
	    sprintf(c8Str, "DUTP L3 Release %d", *((uint16_t *)pData));
	    APP_PROJECT_DEBUG_MSG(c8Str);
		break;
	case kAPI_PROTECT_DOCP_L1_SET:
		break;
	case kAPI_PROTECT_DOCP_L2_SET:
		break;
	case kAPI_PROTECT_DOCP_L3_SET:
		break;
	case kAPI_PROTECT_DOCP_L4_SET:
		break;
	case kAPI_PROTECT_DOCP_L1_RELEASE:
		break;
	case kAPI_PROTECT_DOCP_L2_RELEASE:
		break;
	case kAPI_PROTECT_DOCP_L3_RELEASE:
		break;
	case kAPI_PROTECT_DOCP_L4_RELEASE:
		break;
	case kAPI_PROTECT_COCP_L1_SET:
		break;
	case kAPI_PROTECT_COCP_L2_SET:
		break;
	case kAPI_PROTECT_COCP_L3_SET:
		break;
	case kAPI_PROTECT_COCP_L4_SET:
		break;
	case kAPI_PROTECT_COCP_L1_RELEASE:
		break;
	case kAPI_PROTECT_COCP_L2_RELEASE:
		break;
	case kAPI_PROTECT_COCP_L3_RELEASE:
		break;
	case kAPI_PROTECT_COCP_L4_RELEASE:
		break;
	
	
	case kAPI_PROTECT_RLY1_OT_L1_SET:
		break;
	case kAPI_PROTECT_RLY1_OT_L2_SET:
		break;
	case kAPI_PROTECT_RLY1_OT_L3_SET:
		break;
	case kAPI_PROTECT_RLY1_OT_L1_RELEASE:
		break;
	case kAPI_PROTECT_RLY1_OT_L2_RELEASE:
		break;
	case kAPI_PROTECT_RLY1_OT_L3_RELEASE:
		break;
	case kAPI_PROTECT_RLY2_OT_L1_SET:
		break;
	case kAPI_PROTECT_RLY2_OT_L2_SET:
		break;
	case kAPI_PROTECT_RLY2_OT_L3_SET:
		break;
	case kAPI_PROTECT_RLY2_OT_L1_RELEASE:
		break;
	case kAPI_PROTECT_RLY2_OT_L2_RELEASE:
		break;
	case kAPI_PROTECT_RLY2_OT_L3_RELEASE:
		break;
	case kAPI_PROTECT_AMBI_OT_L1_SET:
		break;
	case kAPI_PROTECT_AMBI_OT_L2_SET:
		break;
	case kAPI_PROTECT_AMBI_OT_L3_SET:
		break;
	case kAPI_PROTECT_AMBI_OT_L1_RELEASE:
		break;
	case kAPI_PROTECT_AMBI_OT_L2_RELEASE:
		break;
	case kAPI_PROTECT_AMBI_OT_L3_RELEASE:
		break;
	case kAPI_PROTECT_BUSBAR_P_OT_L1_SET:
		break;
	case kAPI_PROTECT_BUSBAR_P_OT_L2_SET:
		break;
	case kAPI_PROTECT_BUSBAR_P_OT_L3_SET:
		break;
	case kAPI_PROTECT_BUSBAR_P_OT_L1_RELEASE:
		break;
	case kAPI_PROTECT_BUSBAR_P_OT_L2_RELEASE:
		break;
	case kAPI_PROTECT_BUSBAR_P_OT_L3_RELEASE:
		break;
	case kAPI_PROTECT_BUSBAR_N_OT_L1_SET:
		break;
	case kAPI_PROTECT_BUSBAR_N_OT_L2_SET:
		break;
	case kAPI_PROTECT_BUSBAR_N_OT_L3_SET:
		break;
	case kAPI_PROTECT_BUSBAR_N_OT_L1_RELEASE:
		break;
	case kAPI_PROTECT_BUSBAR_N_OT_L2_RELEASE:
		break;
	case kAPI_PROTECT_BUSBAR_N_OT_L3_RELEASE:
		break;
	case kAPI_PROTECT_RP_URP_L1_SET:
		break;
	case kAPI_PROTECT_RP_URP_L2_SET:
		break;
	case kAPI_PROTECT_RP_URP_L3_SET:
		break;
	case kAPI_PROTECT_RP_URP_L1_RELEASE:
		break;
	case kAPI_PROTECT_RP_URP_L2_RELEASE:
		break;
	case kAPI_PROTECT_RP_URP_L3_RELEASE:
		break;
	case kAPI_PROTECT_RN_URP_L1_SET:
		break;
	case kAPI_PROTECT_RN_URP_L2_SET:
		break;
	case kAPI_PROTECT_RN_URP_L3_SET:
		break;
	case kAPI_PROTECT_RN_URP_L1_RELEASE:
		break;
	case kAPI_PROTECT_RN_URP_L2_RELEASE:
		break;
	case kAPI_PROTECT_RN_URP_L3_RELEASE:
		break;
	case kAPI_PROTECT_DIP_L1_SET:
		break;
	case kAPI_PROTECT_DIP_L2_SET:
		break;
	case kAPI_PROTECT_DIP_L3_SET:
		break;
	case kAPI_PROTECT_DIP_L1_RELEASE:
		break;
	case kAPI_PROTECT_DIP_L2_RELEASE:
		break;
	case kAPI_PROTECT_DIP_L3_RELEASE:
		break;
	case kAPI_PROTECT_VB_DVP_L1_SET:
		break;
	case kAPI_PROTECT_VB_DVP_L2_SET:
		break;
	case kAPI_PROTECT_VB_DVP_L3_SET:
		break;
	case kAPI_PROTECT_VB_DVP_L1_RELEASE:
		break;
	case kAPI_PROTECT_VB_DVP_L2_RELEASE:
		break;
	case kAPI_PROTECT_VB_DVP_L3_RELEASE:
		break;
	case kAPI_PROTECT_CELL_OWP_SET:
		break;
	case kAPI_PROTECT_CELL_OWP_RELEASE:
		break;
	}
}

static void afeEventHandler(__far void *pvdest, uint16_t u16Evt, void *pvDataPtr)
{
}

#ifdef SIM_MODE
static void SimuModeIni(void)
{
	uint16_t u16Cells;
	uint16_t u16Voltage;

	APP_PROJECT_DEBUG_MSG("SimuModeIni !!");

	u16Voltage = 30000;
	for (u16Cells = 0; u16Cells < MAX_CELL_NUMBER; u16Cells++)
	{
		HalAfeSetCellVoltage(u16Cells, u16Voltage);
		u16Voltage += 1;
	}
	u16Voltage = 2000;
	for (u16Cells = 0; u16Cells < MAX_NTC_NUMBER; u16Cells++)
	{
		HalAfeSetNtcVoltage(u16Cells, u16Voltage);
		u16Voltage++;
	}
	
	HalAfeSetCellVoltage(2, 35810);
	HalAfeSetCellVoltage(5, 36010);
	HalAfeSetCellVoltage(8, 36210);
	
	HalAfeSetCellVoltage(1, 27400);
	HalAfeSetCellVoltage(6, 26400);
	HalAfeSetCellVoltage(9, 25510);
	/*
	
	mSystemParemater.mRomPar.mOvp[0].u16SetValue = 35800;
	mSystemParemater.mRomPar.mOvp[0].u8SetTime = 10;
	mSystemParemater.mRomPar.mOvp[0].u16ReleaseValue = 34500;
	mSystemParemater.mRomPar.mOvp[0].u8ReleaseTime = 20;
         
	mSystemParemater.mRomPar.mOvp[1].u16SetValue = 36000;
	mSystemParemater.mRomPar.mOvp[1].u8SetTime = 10;
	mSystemParemater.mRomPar.mOvp[1].u16ReleaseValue = 34500;
	mSystemParemater.mRomPar.mOvp[1].u8ReleaseTime = 20;
         
	mSystemParemater.mRomPar.mOvp[2].u16SetValue = 36200;
	mSystemParemater.mRomPar.mOvp[2].u8SetTime = 6;
	mSystemParemater.mRomPar.mOvp[2].u16ReleaseValue = 34500;
	mSystemParemater.mRomPar.mOvp[2].u8ReleaseTime = 6;
	*/
	
//	halAfeSetVBatVoltage(AFE_VBAT_INDEX, 100000);
//	halAfeSetVBatVoltage(AFE_VPACK_INDEX, 100000);
//	halAfeSetVBatVoltage(AFE_VBCAL_INDEX, 100000);
	//appProjcetSetIrValue(10000, 11000);
//	simu_status |= 0x02;
}
#endif
/* Public function prototypes -----------------------------------------------*/
void AppProjectOpen(void)
{
    DL_GPIO_reset(GPIOA);
    DL_GPIO_reset(GPIOB);
    DL_GPIO_reset(GPIOC);
    
    HalMcuPeripheralInit(); 
	AppSerialUartHvBbuOpen();
	
    HalGeneralTimerLowPowerOpenAndStart(APP_HARDWARE_TIMER_TASK_PERIOD, AppTemporaryHalTimerHandler);
    LibHardwareTimerHandlerOpen(AppProjectHwTimerHandler, 0);
    LibSoftwareTimerHandlerOpen(AppProjectSwTimerHandler, 0);
    
    LibSysParOpen();
    ApiProtectOpen(AppProjectProtectEventHandler);   

    HalAfeHandlerOpen();
    ApiSystemFlagOpen();
	HalAfeAdbms6832Open(afeEventHandler);

#ifdef SIM_MODE
	SimuModeIni();
#endif

#ifdef NORMAL_MODE_BUILD
    APP_PROJECT_DEBUG_MSG("Normal Mode");
#endif

#ifdef BOOT_MODE_BUILD
    APP_PROJECT_DEBUG_MSG("Boot Mode");
#endif

}

/************************ (C) COPYRIGHT *****END OF FILE****/
