/*
******************************************************************************
* @file     HalWatchDog.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "<PERSON><PERSON>atchDog.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/


/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
/* Local function prototypes ------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void HalWdtFeedDog(tHalWWDTHandle *pWdt) 
{
    DL_WWDT_restart(pWdt);
}

bool HalIsWdtReset(tHalWWDTHandle *pWdt)
{
    bool bWDTStatus = false;
    bWDTStatus = DL_WWDT_isReset(pWdt);
    return bWDTStatus;
}

void HalWdtStop(tHalWWDTHandle *pWdt)
{
    DL_WWDT_disablePower(pWdt);
}

void HalWdtStart(tHalWWDTHandle *pWdt)
{
    DL_WWDT_enablePower(pWdt);
}

void HalWdtInit(tHalWWDTHandle *pWdt, eTypeHalWdtTimeout mTimeout)
{
    DL_WWDT_reset(pWdt);
    HalWdtStart(pWdt);
    switch (mTimeout)
    {       
        case kHAL_WDT_TIMEOUT_500MS:
            DL_WWDT_initWatchdogMode(
                pWdt,
                DL_WWDT_CLOCK_DIVIDE_4,
                DL_WWDT_TIMER_PERIOD_12_BITS,
                DL_WWDT_RUN_IN_SLEEP,
                DL_WWDT_WINDOW_PERIOD_0,
                DL_WWDT_WINDOW_PERIOD_0
            );
            break;
        case kHAL_WDT_TIMEOUT_1000MS:
            DL_WWDT_initWatchdogMode(
                pWdt,
                DL_WWDT_CLOCK_DIVIDE_8,
                DL_WWDT_TIMER_PERIOD_12_BITS,
                DL_WWDT_RUN_IN_SLEEP,
                DL_WWDT_WINDOW_PERIOD_0,
                DL_WWDT_WINDOW_PERIOD_0
            );

            break;
        default:
            DL_WWDT_initWatchdogMode(
                pWdt,
                DL_WWDT_CLOCK_DIVIDE_4,
                DL_WWDT_TIMER_PERIOD_12_BITS,
                DL_WWDT_RUN_IN_SLEEP,
                DL_WWDT_WINDOW_PERIOD_0,
                DL_WWDT_WINDOW_PERIOD_0
            );
            break;
    }  
    DL_WWDT_setActiveWindow(WWDT0, DL_WWDT_WINDOW0);
}