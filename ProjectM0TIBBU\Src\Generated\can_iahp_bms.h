/*
 * CAN_IAHP_BMS - CAN Database Header File
 * Generated from CAN_IAHP_BMS.dbc using cantools
 *
 * Total Messages: 46
 * Generated on: 2025-07-23 14:13:29
 */

#ifndef CAN_IAHP_BMS_H
#define CAN_IAHP_BMS_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Message IDs */
#define CAN_IAHP_BMS_BMS_PACKINFO1_ID 0x00000100U
#define CAN_IAHP_BMS_BMS_PACKINFO2_ID 0x00000101U
#define CAN_IAHP_BMS_BMS_PACKINFO3_ID 0x00000102U
#define CAN_IAHP_BMS_BMS_PACKINFO4_ID 0x00000103U
#define CAN_IAHP_BMS_BMS_PACKINFO5_ID 0x00000104U
#define CAN_IAHP_BMS_BMS_PACKINFO6_ID 0x00000105U
#define CAN_IAHP_BMS_BMS_PACKINFO7_ID 0x00000106U
#define CAN_IAHP_BMS_BMS_PACKINFO8_ID 0x00000107U
#define CAN_IAHP_BMS_BMS_STATUS1_ID 0x00000108U
#define CAN_IAHP_BMS_BMS_STATUS2_ID 0x00000109U
#define CAN_IAHP_BMS_BMS_STATUS3_ID 0x0000010AU
#define CAN_IAHP_BMS_BMS_STATUS4_ID 0x0000010BU
#define CAN_IAHP_BMS_BMS_STATUS5_ID 0x0000010CU
#define CAN_IAHP_BMS_BMS_TEMP01_04_ID 0x0000010DU
#define CAN_IAHP_BMS_BMS_TEMP05_08_ID 0x0000010EU
#define CAN_IAHP_BMS_BMS_TEMP09_12_ID 0x0000010FU
#define CAN_IAHP_BMS_BMS_TEMP13_16_ID 0x00000110U
#define CAN_IAHP_BMS_BMS_TEMP17_20_ID 0x00000111U
#define CAN_IAHP_BMS_BMS_TEMP21_24_ID 0x00000112U
#define CAN_IAHP_BMS_BMS_TEMP25_28_ID 0x00000113U
#define CAN_IAHP_BMS_BMS_TEMP29_32_ID 0x00000114U
#define CAN_IAHP_BMS_BMS_TEMP33_36_ID 0x00000115U
#define CAN_IAHP_BMS_BMS_TEMP37_40_ID 0x00000116U
#define CAN_IAHP_BMS_BMS_TEMP41_44_ID 0x00000117U
#define CAN_IAHP_BMS_BMS_TEMP45_48_ID 0x00000118U
#define CAN_IAHP_BMS_BMS_CELL01_04_ID 0x00000119U
#define CAN_IAHP_BMS_BMS_CELL05_08_ID 0x0000011AU
#define CAN_IAHP_BMS_BMS_CELL09_12_ID 0x0000011BU
#define CAN_IAHP_BMS_BMS_CELL13_16_ID 0x0000011CU
#define CAN_IAHP_BMS_BMS_CELL17_20_ID 0x0000011DU
#define CAN_IAHP_BMS_BMS_CELL21_24_ID 0x0000011EU
#define CAN_IAHP_BMS_BMS_CELL25_28_ID 0x0000011FU
#define CAN_IAHP_BMS_BMS_CELL29_32_ID 0x00000120U
#define CAN_IAHP_BMS_BMS_CELL33_36_ID 0x00000121U
#define CAN_IAHP_BMS_BMS_CELL37_40_ID 0x00000122U
#define CAN_IAHP_BMS_BMS_CELL41_44_ID 0x00000123U
#define CAN_IAHP_BMS_BMS_CELL45_48_ID 0x00000124U
#define CAN_IAHP_BMS_BMS_CELL49_52_ID 0x00000125U
#define CAN_IAHP_BMS_BMS_CELL53_56_ID 0x00000126U
#define CAN_IAHP_BMS_BMS_CELL57_60_ID 0x00000127U
#define CAN_IAHP_BMS_BMS_CELL61_64_ID 0x00000128U
#define CAN_IAHP_BMS_BMS_CELL65_68_ID 0x00000129U
#define CAN_IAHP_BMS_BMS_CELL69_72_ID 0x0000012AU
#define CAN_IAHP_BMS_BMS_OTHERVOLT1_ID 0x0000012BU
#define CAN_IAHP_BMS_BMS_OTHERVOLT2_ID 0x0000012CU
#define CAN_IAHP_BMS_BMS_VERSION_ID 0x0000012DU

/* Message Structures */

typedef struct {
    uint32_t bms_vpack;
    uint32_t bms_packvoltage;
} can_iahp_bms_bms_packinfo1_t;

typedef struct {
    int32_t bms_packcurrent;
    int32_t bms_avgcurrent;
} can_iahp_bms_bms_packinfo2_t;

typedef struct {
    uint16_t bms_rsoc;
    uint16_t bms_asoc;
    uint32_t bms_rc;
} can_iahp_bms_bms_packinfo3_t;

typedef struct {
    uint32_t bms_fcc;
    uint16_t bms_cyclecount;
    uint16_t bms_learncycle;
} can_iahp_bms_bms_packinfo4_t;

typedef struct {
    uint32_t bms_userrc;
    uint32_t bms_dcr;
} can_iahp_bms_bms_packinfo5_t;

typedef struct {
    uint32_t bms_fdcr;
    uint16_t bms_userrsoc;
    uint16_t bms_fccmin;
} can_iahp_bms_bms_packinfo6_t;

typedef struct {
    uint32_t bms_deltarc;
    uint16_t bms_srartrsoc;
    uint16_t bms_startfdcr;
} can_iahp_bms_bms_packinfo7_t;

typedef struct {
    uint32_t bms_rcmincutoff;
} can_iahp_bms_bms_packinfo8_t;

typedef struct {
    uint16_t bms_batterystatuslow;
    uint16_t bms_batterystatushigh;
    uint16_t bms_packstatuslow;
    uint16_t bms_packstatushigh;
} can_iahp_bms_bms_status1_t;

typedef struct {
    uint16_t bms_safetystatuslow;
    uint16_t bms_safetystatushigh;
    uint16_t bms_warnstatus;
    uint16_t bms_ststatus;
} can_iahp_bms_bms_status2_t;

typedef struct {
    uint16_t bms_pfstatuslow;
    uint16_t bms_pfstatushigh;
    uint16_t bms_cbs0_15;
    uint16_t bms_startrsocmin;
} can_iahp_bms_bms_status3_t;

typedef struct {
    uint32_t bms_usagecapacity;
    uint32_t bms_succchacap;
} can_iahp_bms_bms_status4_t;

typedef struct {
    uint32_t bms_systemtime;
    uint8_t bms_engmode;
} can_iahp_bms_bms_status5_t;

typedef struct {
    int16_t bms_temp1;
    int16_t bms_temp2;
    int16_t bms_temp3;
    int16_t bms_temp4;
} can_iahp_bms_bms_temp01_04_t;

typedef struct {
    int16_t bms_temp5;
    int16_t bms_temp6;
    int16_t bms_temp7;
    int16_t bms_temp8;
} can_iahp_bms_bms_temp05_08_t;

typedef struct {
    int16_t bms_temp9;
    int16_t bms_temp10;
    int16_t bms_temp11;
    int16_t bms_temp12;
} can_iahp_bms_bms_temp09_12_t;

typedef struct {
    int16_t bms_temp13;
    int16_t bms_temp14;
    int16_t bms_temp15;
    int16_t bms_temp16;
} can_iahp_bms_bms_temp13_16_t;

typedef struct {
    int16_t bms_temp17;
    int16_t bms_temp18;
    int16_t bms_temp19;
    int16_t bms_temp20;
} can_iahp_bms_bms_temp17_20_t;

typedef struct {
    int16_t bms_temp21;
    int16_t bms_temp22;
    int16_t bms_temp23;
    int16_t bms_temp24;
} can_iahp_bms_bms_temp21_24_t;

typedef struct {
    int16_t bms_temp25;
    int16_t bms_temp26;
    int16_t bms_temp27;
    int16_t bms_temp28;
} can_iahp_bms_bms_temp25_28_t;

typedef struct {
    int16_t bms_temp29;
    int16_t bms_temp30;
    int16_t bms_temp31;
    int16_t bms_temp32;
} can_iahp_bms_bms_temp29_32_t;

typedef struct {
    int16_t bms_temp33;
    int16_t bms_temp34;
    int16_t bms_temp35;
    int16_t bms_temp36;
} can_iahp_bms_bms_temp33_36_t;

typedef struct {
    int16_t bms_temp37;
    int16_t bms_temp38;
    int16_t bms_temp39;
    int16_t bms_temp40;
} can_iahp_bms_bms_temp37_40_t;

typedef struct {
    int16_t bms_temp41;
    int16_t bms_temp42;
    int16_t bms_temp43;
    int16_t bms_temp44;
} can_iahp_bms_bms_temp41_44_t;

typedef struct {
    int16_t bms_temp45;
    int16_t bms_temp46;
    int16_t bms_temp47;
    int16_t bms_temp48;
} can_iahp_bms_bms_temp45_48_t;

typedef struct {
    uint16_t bms_cellvolt1;
    uint16_t bms_cellvolt2;
    uint16_t bms_cellvolt3;
    uint16_t bms_cellvolt4;
} can_iahp_bms_bms_cell01_04_t;

typedef struct {
    uint16_t bms_cellvolt5;
    uint16_t bms_cellvolt6;
    uint16_t bms_cellvolt7;
    uint16_t bms_cellvolt8;
} can_iahp_bms_bms_cell05_08_t;

typedef struct {
    uint16_t bms_cellvolt9;
    uint16_t bms_cellvolt10;
    uint16_t bms_cellvolt11;
    uint16_t bms_cellvolt12;
} can_iahp_bms_bms_cell09_12_t;

typedef struct {
    uint16_t bms_cellvolt13;
    uint16_t bms_cellvolt14;
    uint16_t bms_cellvolt15;
    uint16_t bms_cellvolt16;
} can_iahp_bms_bms_cell13_16_t;

typedef struct {
    uint16_t bms_cellvolt17;
    uint16_t bms_cellvolt18;
    uint16_t bms_cellvolt19;
    uint16_t bms_cellvolt20;
} can_iahp_bms_bms_cell17_20_t;

typedef struct {
    uint16_t bms_cellvolt21;
    uint16_t bms_cellvolt22;
    uint16_t bms_cellvolt23;
    uint16_t bms_cellvolt24;
} can_iahp_bms_bms_cell21_24_t;

typedef struct {
    uint16_t bms_cellvolt25;
    uint16_t bms_cellvolt26;
    uint16_t bms_cellvolt27;
    uint16_t bms_cellvolt28;
} can_iahp_bms_bms_cell25_28_t;

typedef struct {
    uint16_t bms_cellvolt29;
    uint16_t bms_cellvolt30;
    uint16_t bms_cellvolt31;
    uint16_t bms_cellvolt32;
} can_iahp_bms_bms_cell29_32_t;

typedef struct {
    uint16_t bms_cellvolt33;
    uint16_t bms_cellvolt34;
    uint16_t bms_cellvolt35;
    uint16_t bms_cellvolt36;
} can_iahp_bms_bms_cell33_36_t;

typedef struct {
    uint16_t bms_cellvolt37;
    uint16_t bms_cellvolt38;
    uint16_t bms_cellvolt39;
    uint16_t bms_cellvolt40;
} can_iahp_bms_bms_cell37_40_t;

typedef struct {
    uint16_t bms_cellvolt41;
    uint16_t bms_cellvolt42;
    uint16_t bms_cellvolt43;
    uint16_t bms_cellvolt44;
} can_iahp_bms_bms_cell41_44_t;

typedef struct {
    uint16_t bms_cellvolt45;
    uint16_t bms_cellvolt46;
    uint16_t bms_cellvolt47;
    uint16_t bms_cellvolt48;
} can_iahp_bms_bms_cell45_48_t;

typedef struct {
    uint16_t bms_cellvolt49;
    uint16_t bms_cellvolt50;
    uint16_t bms_cellvolt51;
    uint16_t bms_cellvolt52;
} can_iahp_bms_bms_cell49_52_t;

typedef struct {
    uint16_t bms_cellvolt53;
    uint16_t bms_cellvolt54;
    uint16_t bms_cellvolt55;
    uint16_t bms_cellvolt56;
} can_iahp_bms_bms_cell53_56_t;

typedef struct {
    uint16_t bms_cellvolt57;
    uint16_t bms_cellvolt58;
    uint16_t bms_cellvolt59;
    uint16_t bms_cellvolt60;
} can_iahp_bms_bms_cell57_60_t;

typedef struct {
    uint16_t bms_cellvolt61;
    uint16_t bms_cellvolt62;
    uint16_t bms_cellvolt63;
    uint16_t bms_cellvolt64;
} can_iahp_bms_bms_cell61_64_t;

typedef struct {
    uint16_t bms_cellvolt65;
    uint16_t bms_cellvolt66;
    uint16_t bms_cellvolt67;
    uint16_t bms_cellvolt68;
} can_iahp_bms_bms_cell65_68_t;

typedef struct {
    uint16_t bms_cellvolt69;
    uint16_t bms_cellvolt70;
    uint16_t bms_cellvolt71;
    uint16_t bms_cellvolt72;
} can_iahp_bms_bms_cell69_72_t;

typedef struct {
    uint32_t bms_vdfuse;
    uint32_t bms_vchgplus;
} can_iahp_bms_bms_othervolt1_t;

typedef struct {
    uint32_t bms_vpackplus;
    uint32_t bms_vcfuse;
} can_iahp_bms_bms_othervolt2_t;

typedef struct {
    uint32_t bms_chksum;
    uint32_t bms_publicver;
} can_iahp_bms_bms_version_t;

/* Function Prototypes */
int can_iahp_bms_bms_packinfo1_pack(uint8_t *data, const can_iahp_bms_bms_packinfo1_t *msg, uint8_t size);
int can_iahp_bms_bms_packinfo2_pack(uint8_t *data, const can_iahp_bms_bms_packinfo2_t *msg, uint8_t size);
int can_iahp_bms_bms_packinfo3_pack(uint8_t *data, const can_iahp_bms_bms_packinfo3_t *msg, uint8_t size);
int can_iahp_bms_bms_packinfo4_pack(uint8_t *data, const can_iahp_bms_bms_packinfo4_t *msg, uint8_t size);
int can_iahp_bms_bms_packinfo5_pack(uint8_t *data, const can_iahp_bms_bms_packinfo5_t *msg, uint8_t size);
int can_iahp_bms_bms_packinfo6_pack(uint8_t *data, const can_iahp_bms_bms_packinfo6_t *msg, uint8_t size);
int can_iahp_bms_bms_packinfo7_pack(uint8_t *data, const can_iahp_bms_bms_packinfo7_t *msg, uint8_t size);
int can_iahp_bms_bms_packinfo8_pack(uint8_t *data, const can_iahp_bms_bms_packinfo8_t *msg, uint8_t size);
int can_iahp_bms_bms_status1_pack(uint8_t *data, const can_iahp_bms_bms_status1_t *msg, uint8_t size);
int can_iahp_bms_bms_status2_pack(uint8_t *data, const can_iahp_bms_bms_status2_t *msg, uint8_t size);
int can_iahp_bms_bms_status3_pack(uint8_t *data, const can_iahp_bms_bms_status3_t *msg, uint8_t size);
int can_iahp_bms_bms_status4_pack(uint8_t *data, const can_iahp_bms_bms_status4_t *msg, uint8_t size);
int can_iahp_bms_bms_status5_pack(uint8_t *data, const can_iahp_bms_bms_status5_t *msg, uint8_t size);
int can_iahp_bms_bms_temp01_04_pack(uint8_t *data, const can_iahp_bms_bms_temp01_04_t *msg, uint8_t size);
int can_iahp_bms_bms_temp05_08_pack(uint8_t *data, const can_iahp_bms_bms_temp05_08_t *msg, uint8_t size);
int can_iahp_bms_bms_temp09_12_pack(uint8_t *data, const can_iahp_bms_bms_temp09_12_t *msg, uint8_t size);
int can_iahp_bms_bms_temp13_16_pack(uint8_t *data, const can_iahp_bms_bms_temp13_16_t *msg, uint8_t size);
int can_iahp_bms_bms_temp17_20_pack(uint8_t *data, const can_iahp_bms_bms_temp17_20_t *msg, uint8_t size);
int can_iahp_bms_bms_temp21_24_pack(uint8_t *data, const can_iahp_bms_bms_temp21_24_t *msg, uint8_t size);
int can_iahp_bms_bms_temp25_28_pack(uint8_t *data, const can_iahp_bms_bms_temp25_28_t *msg, uint8_t size);
int can_iahp_bms_bms_temp29_32_pack(uint8_t *data, const can_iahp_bms_bms_temp29_32_t *msg, uint8_t size);
int can_iahp_bms_bms_temp33_36_pack(uint8_t *data, const can_iahp_bms_bms_temp33_36_t *msg, uint8_t size);
int can_iahp_bms_bms_temp37_40_pack(uint8_t *data, const can_iahp_bms_bms_temp37_40_t *msg, uint8_t size);
int can_iahp_bms_bms_temp41_44_pack(uint8_t *data, const can_iahp_bms_bms_temp41_44_t *msg, uint8_t size);
int can_iahp_bms_bms_temp45_48_pack(uint8_t *data, const can_iahp_bms_bms_temp45_48_t *msg, uint8_t size);
int can_iahp_bms_bms_cell01_04_pack(uint8_t *data, const can_iahp_bms_bms_cell01_04_t *msg, uint8_t size);
int can_iahp_bms_bms_cell05_08_pack(uint8_t *data, const can_iahp_bms_bms_cell05_08_t *msg, uint8_t size);
int can_iahp_bms_bms_cell09_12_pack(uint8_t *data, const can_iahp_bms_bms_cell09_12_t *msg, uint8_t size);
int can_iahp_bms_bms_cell13_16_pack(uint8_t *data, const can_iahp_bms_bms_cell13_16_t *msg, uint8_t size);
int can_iahp_bms_bms_cell17_20_pack(uint8_t *data, const can_iahp_bms_bms_cell17_20_t *msg, uint8_t size);
int can_iahp_bms_bms_cell21_24_pack(uint8_t *data, const can_iahp_bms_bms_cell21_24_t *msg, uint8_t size);
int can_iahp_bms_bms_cell25_28_pack(uint8_t *data, const can_iahp_bms_bms_cell25_28_t *msg, uint8_t size);
int can_iahp_bms_bms_cell29_32_pack(uint8_t *data, const can_iahp_bms_bms_cell29_32_t *msg, uint8_t size);
int can_iahp_bms_bms_cell33_36_pack(uint8_t *data, const can_iahp_bms_bms_cell33_36_t *msg, uint8_t size);
int can_iahp_bms_bms_cell37_40_pack(uint8_t *data, const can_iahp_bms_bms_cell37_40_t *msg, uint8_t size);
int can_iahp_bms_bms_cell41_44_pack(uint8_t *data, const can_iahp_bms_bms_cell41_44_t *msg, uint8_t size);
int can_iahp_bms_bms_cell45_48_pack(uint8_t *data, const can_iahp_bms_bms_cell45_48_t *msg, uint8_t size);
int can_iahp_bms_bms_cell49_52_pack(uint8_t *data, const can_iahp_bms_bms_cell49_52_t *msg, uint8_t size);
int can_iahp_bms_bms_cell53_56_pack(uint8_t *data, const can_iahp_bms_bms_cell53_56_t *msg, uint8_t size);
int can_iahp_bms_bms_cell57_60_pack(uint8_t *data, const can_iahp_bms_bms_cell57_60_t *msg, uint8_t size);
int can_iahp_bms_bms_cell61_64_pack(uint8_t *data, const can_iahp_bms_bms_cell61_64_t *msg, uint8_t size);
int can_iahp_bms_bms_cell65_68_pack(uint8_t *data, const can_iahp_bms_bms_cell65_68_t *msg, uint8_t size);
int can_iahp_bms_bms_cell69_72_pack(uint8_t *data, const can_iahp_bms_bms_cell69_72_t *msg, uint8_t size);
int can_iahp_bms_bms_othervolt1_pack(uint8_t *data, const can_iahp_bms_bms_othervolt1_t *msg, uint8_t size);
int can_iahp_bms_bms_othervolt2_pack(uint8_t *data, const can_iahp_bms_bms_othervolt2_t *msg, uint8_t size);
int can_iahp_bms_bms_version_pack(uint8_t *data, const can_iahp_bms_bms_version_t *msg, uint8_t size);

/* Timer Management Functions */
void can_iahp_bms_timer_init(void);
void can_iahp_bms_timer_1ms_handler(void);
void can_iahp_bms_broadcast_all_messages(void);

/* Message timing control */
typedef struct {
    uint8_t delay_time;     // GenMsgDelayTime (0-5ms)
    uint8_t cycle_time;     // GenMsgCycleTime (50ms)
    uint8_t delay_counter;  // 当前延迟计数器
    uint8_t cycle_counter;  // 当前周期计数器
    uint8_t enabled;        // 消息使能标志
} can_iahp_bms_msg_timing_t;

extern can_iahp_bms_msg_timing_t can_iahp_bms_msg_timings[46];

#ifdef __cplusplus
}
#endif

#endif /* CAN_IAHP_BMS_H */
