﻿/*
******************************************************************************
* @file     HalMcuPeripheralConfig.c
* <AUTHOR> <PERSON>
* @brief    This file include MCU peripheral configuration.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

/* Includes ------------------------------------------------------------------*/
#include "HalMcuPeripheralConfig.h"
/* Local typedef -----------------------------------------------------------*/
/* Local define ------------------------------------------------------------*/

#if 1
#define HAL_MCU_SYS_CLOCK_SOURE_USE_HFXT
#endif

#if 0
#define HAL_MCU_SYS_CLOCK_OUT_SYSPLLOUT1
#endif

#define HAL_MCU_SYSPLL_QDIV                        (9)
#define HAL_MCU_SYSPLL_CLK1OUT_DEVIDE              (1)
#define HAL_MCU_SYSPLL_REF_HFCLK_80MHZ_QDIV        (7)

#define GPIO_LFXOUT_PIN                            (BSP_LFXT_OUT_PIN)
#define GPIO_LFXOUT_IOMUX                          (BSP_LFXT_OUT_IOMUX)
#define GPIO_LFXIN_PIN                             (BSP_LFXT_IN_PIN)
#define GPIO_LFXIN_IOMUX                           (BSP_LFXT_IN_IOMUX)

#define GPIO_HFXOUT_PIN                            (BSP_HFXT_OUT_PIN)
#define GPIO_HFXOUT_IOMUX                          (BSP_HFXT_OUT_IOMUX)
#define GPIO_HFXIN_PIN                             (BSP_HFXT_IN_PIN)
#define GPIO_HFXIN_IOMUX                           (BSP_HFXT_IN_IOMUX)

#define GPIO_CLKOUT_PORT                           (GPIOA)
#define GPIO_CLKOUT_PIN                            (DL_GPIO_PIN_14)
#define GPIO_CLKOUT_IOMUX                          (IOMUX_PINCM36)
#define GPIO_CLKOUT_IOMUX_FUNC                     (IOMUX_PINCM36_PF_SYSCTL_CLK_OUT)

/* Local macro -------------------------------------------------------------*/
/* Local function declare --------------------------------------------------*/
static void PeripheralReset(void);
static void SystemClockConfigure80MhzFromSysOsc(void);
static void SystemClockConfigure80MhzFromHFCLK(void);
static void DebugGpioInit(void);
static void ProjectBspGpioInit(void);

/* Local variables ---------------------------------------------------------*/
/*
	Systemp PLL Frequencr Formula
	pDiv: Range(0x00~0x03 : /1, /2, /4)
	qDiv: Range(0x01~0x7E : /2, /3, .../127)

	Freq(VCO) = Freq(Reference) * (qDiv+1) / 2^pDiv 
	32 * (9+1) / 2 = 160 MHz

	PLL output Devider 
	SYSPLLCLK2XDIV = RDIVCLK2X + 1
	fSYSPLLCLK2X = 2 × fVCO / SYSPLLCLK2XDIV
	Freq(rDivClk2x) --> 2*160 / 2 = 160 MHz

	SYSPLLCLK0DIV = 2 × (RDIVCLK0+1) 
	SYSPLLCLK1DIV = 2 × (RDIVCLK1+1) 

	Set as Main Clock
	fSYSPLLCLK0 = fVCO / SYSPLLCLK0DIV
				= 160  / (2) = 80MHz

	fSYSPLLCLK1 = fVCO / SYSPLLCLK1DIV
				= 160  / (4) = 40MHz
*/
static const DL_SYSCTL_SYSPLLConfig gSYSPLLConfig80MHzFromSysOsc = 
{
	.inputFreq             = DL_SYSCTL_SYSPLL_INPUT_FREQ_16_32_MHZ, // Set Factory Flash Address For Loading SYSPLL Lookup Parameter 
	.rDivClk2x              = 1,
	.rDivClk1               = 1,
	.rDivClk0               = 0,
	.enableCLK2x            = DL_SYSCTL_SYSPLL_CLK2X_DISABLE,
	.enableCLK1             = DL_SYSCTL_SYSPLL_CLK1_ENABLE,
	.enableCLK0             = DL_SYSCTL_SYSPLL_CLK0_ENABLE,
	.sysPLLMCLK             = DL_SYSCTL_SYSPLL_MCLK_CLK0,
	.sysPLLRef              = DL_SYSCTL_SYSPLL_REF_SYSOSC,
	.qDiv                   = HAL_MCU_SYSPLL_QDIV,
	.pDiv                   = DL_SYSCTL_SYSPLL_PDIV_2               // 1 --> Frequencey Devide 2
};


/*
	Systemp PLL Frequencr Formula
	pDiv: Range(0x00~0x03 : /1, /2, /4)
	qDiv: Range(0x01~0x7E : /2, /3, .../127)

	Freq(VCO) = Freq(Reference) * (qDiv+1) / 2^pDiv 
	40 * (7+1) / 2 = 160 MHz (FreqVco)

	PLL output Devider 
	SYSPLLCLK2XDIV = RDIVCLK2X + 1
	fSYSPLLCLK2X = 2 × fVCO / SYSPLLCLK2XDIV
	Freq(rDivClk2x) --> 2*160 / 2 = 160 MHz

	SYSPLLCLK0DIV = 2 × (RDIVCLK0 + 1)  = 2 x (0 + 1)  = 2
	SYSPLLCLK1DIV = 2 × (RDIVCLK1 + 1)  = 2 x (0 + 1)  = 2

	Set as Main Clock
	fSYSPLLCLK0 = fVCO / SYSPLLCLK0DIV
				= 160  / (2) = 80MHz

	fSYSPLLCLK1 = fVCO / SYSPLLCLK1DIV
				= 160  / (2) = 80MHz
*/
static const DL_SYSCTL_SYSPLLConfig gSYSPLLConfig80MHzFromHFCLK = 
{
	.inputFreq              = DL_SYSCTL_SYSPLL_INPUT_FREQ_32_48_MHZ, // Set Factory Flash Address For Loading SYSPLL Lookup Parameter 
	.rDivClk2x              = 1,                                     // 0= div1, 1= div2, 2= div3, 3= div4, 4= div5
	.rDivClk1               = 0,                                     // 0= div2, 1= div4, 2= div6, 3= div8, 4= div10
	.rDivClk0               = 0,                                     // 0= div2, 1= div4, 2= div6, 3= div8, 4= div10
	.enableCLK2x            = DL_SYSCTL_SYSPLL_CLK2X_DISABLE,
	.enableCLK1             = DL_SYSCTL_SYSPLL_CLK1_ENABLE,
	.enableCLK0             = DL_SYSCTL_SYSPLL_CLK0_ENABLE,
	.sysPLLMCLK             = DL_SYSCTL_SYSPLL_MCLK_CLK0,            //Set MCLK source from PLL CLK0
	.sysPLLRef              = DL_SYSCTL_SYSPLL_REF_HFCLK,            //Set PLL Ref clock source from HFCLK 
	.qDiv                   = HAL_MCU_SYSPLL_REF_HFCLK_80MHZ_QDIV,             
	.pDiv                   = DL_SYSCTL_SYSPLL_PDIV_2,               // 1 --> Frequencey Devide 2
};

static const DL_SYSCTL_LFCLKConfig gLFCLKConfig = 
{
    .lowCap   = false,
    .monitor  = false,
    .xt1Drive = DL_SYSCTL_LFXT_DRIVE_STRENGTH_HIGHEST,
};

/* Local function prototypes -----------------------------------------------*/
static void PeripheralReset(void)
{
    DL_GPIO_enablePower(GPIOA);
    DL_GPIO_enablePower(GPIOB);
	DL_GPIO_enablePower(GPIOC);

    delay_cycles(HAL_MCU_POWER_STARTUP_DELAY_CYCLE_CNT);
}

static void WaitSysClockConfigDone(void)
{
	while ((DL_SYSCTL_getClockStatus() & (DL_SYSCTL_CLK_STATUS_SYSPLL_GOOD | DL_SYSCTL_CLK_STATUS_HSCLK_GOOD 
										| DL_SYSCTL_CLK_STATUS_LFXT_GOOD | DL_SYSCTL_CLK_STATUS_HFCLK_GOOD))
			!= (DL_SYSCTL_CLK_STATUS_SYSPLL_GOOD | DL_SYSCTL_CLK_STATUS_HSCLK_GOOD 
				| DL_SYSCTL_CLK_STATUS_LFXT_GOOD | DL_SYSCTL_CLK_STATUS_HFCLK_GOOD))
	{
	/* Ensure that clocks are in default POR configuration before initialization.
	* Additionally once LFXT is enabled, the internal LFOSC is disabled, and cannot
	* be re-enabled other than by executing a BOOTRST. */
	;
	}
}

static void SystemClockConfigure80MhzFromSysOsc(void)
{
	DL_SYSCTL_setBORThreshold(DL_SYSCTL_BOR_THRESHOLD_LEVEL_0);
	DL_SYSCTL_setFlashWaitState(DL_SYSCTL_FLASH_WAIT_STATE_2);

	// Set Internal SYSOSC to 32Mhz
	DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
	DL_SYSCTL_disableHFXT();
	DL_SYSCTL_disableSYSPLL();

	// Set SYSPLL Clock To 80Mhz, Default value set as HSCLK source. 
	DL_SYSCTL_configSYSPLL((DL_SYSCTL_SYSPLLConfig *) &gSYSPLLConfig80MHzFromSysOsc);
	// For Low Power Mode
	DL_SYSCTL_setULPCLKDivider(DL_SYSCTL_ULPCLK_DIV_2); 
	// Switch Clock Source From SYSOSC to HSCLK 
	DL_SYSCTL_setMCLKSource(SYSOSC, HSCLK, DL_SYSCTL_HSCLK_SOURCE_SYSPLL);
	// Open 40MHz HFX On Ev Board 
	DL_SYSCTL_setHFCLKSourceHFXTParams(DL_SYSCTL_HFXT_RANGE_32_48_MHZ, 10, true);
	
	//LFXT Clk  config
	//-------------------------------------
    DL_GPIO_initPeripheralAnalogFunction(GPIO_LFXIN_IOMUX);
    DL_GPIO_initPeripheralAnalogFunction(GPIO_LFXOUT_IOMUX);
    DL_SYSCTL_setLFCLKSourceLFXT((DL_SYSCTL_LFCLKConfig *) &gLFCLKConfig);
    
}

static void SystemClockConfigure80MhzFromHFCLK(void)
{
	//Low Power Mode is configured to be SLEEP0
    DL_SYSCTL_setBORThreshold(DL_SYSCTL_BOR_THRESHOLD_LEVEL_0);
    DL_SYSCTL_setFlashWaitState(DL_SYSCTL_FLASH_WAIT_STATE_2);

    DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
    /* Set default configuration */
    DL_SYSCTL_disableHFXT();
	DL_SYSCTL_disableSYSPLL();

	//HFXT Clk Config
	//-------------------------------------	
    DL_GPIO_initPeripheralAnalogFunction(GPIO_HFXIN_IOMUX);
    DL_GPIO_initPeripheralAnalogFunction(GPIO_HFXOUT_IOMUX);
    DL_SYSCTL_setHFCLKSourceHFXTParams(DL_SYSCTL_HFXT_RANGE_32_48_MHZ,10, true);

	//LFXT Clk  config
	//-------------------------------------
    DL_GPIO_initPeripheralAnalogFunction(GPIO_LFXIN_IOMUX);
    DL_GPIO_initPeripheralAnalogFunction(GPIO_LFXOUT_IOMUX);
    DL_SYSCTL_setLFCLKSourceLFXT((DL_SYSCTL_LFCLKConfig *) &gLFCLKConfig);

	// Set SYSPLL Clock To 80MHz, Default value set as HSCLK source. 
	DL_SYSCTL_configSYSPLL((DL_SYSCTL_SYSPLLConfig *) &gSYSPLLConfig80MHzFromHFCLK);
    
	// Clock For Low Power Mode 
	DL_SYSCTL_setULPCLKDivider(DL_SYSCTL_ULPCLK_DIV_2);

	// Switch Clock Source From SYSOSC to HSCLK(80MHz from SYSPLL Clock)
	DL_SYSCTL_setMCLKSource(SYSOSC, HSCLK, DL_SYSCTL_HSCLK_SOURCE_SYSPLL);
	
	#ifdef  HAL_MCU_SYS_CLOCK_OUT_SYSPLLOUT1
	//Setting SYSPLL out pin
	DL_GPIO_initPeripheralOutputFunction(GPIO_CLKOUT_IOMUX, GPIO_CLKOUT_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_CLKOUT_PORT, GPIO_CLKOUT_PIN);
	DL_SYSCTL_enableExternalClock(DL_SYSCTL_CLK_OUT_SOURCE_SYSPLLOUT1, DL_SYSCTL_CLK_OUT_DIVIDE_16);  //80MHz/16 = 5MHz (Output pin)
    #endif
}

static void DebugGpioInit(void)
{
	#ifdef BSP_MCU_EVB3519_LED_FUNCTION
    /* Red LED On 3519 EVB Board */
	DL_GPIO_initDigitalOutput(BSP_EVB_RED_LED_IOMUX);
	DL_GPIO_enableOutput(BSP_EVB_RED_LED_PORT, BSP_EVB_RED_LED_PIN);

	/* Green LED On 3519 EVB Board */
	DL_GPIO_initDigitalOutput(BSP_EVB_GREEN_LED_IOMUX);
	DL_GPIO_enableOutput(BSP_EVB_GREEN_LED_PORT, BSP_EVB_GREEN_LED_PIN);

	/* Blue LED On 3519 EVB Board */
	DL_GPIO_initDigitalOutput(BSP_EVB_BLUE_LED_IOMUX);
	DL_GPIO_enableOutput(BSP_EVB_BLUE_LED_PORT, BSP_EVB_BLUE_LED_PIN);
	#endif

	/* Debug Pin For LA */
	DL_GPIO_initDigitalOutput(BSP_DEBUG0_IOMUX);
	DL_GPIO_enableOutput(BSP_DEBUG0_PORT, BSP_DEBUG0_PIN);

	DL_GPIO_initDigitalOutput(BSP_DEBUG1_IOMUX);
	DL_GPIO_enableOutput(BSP_DEBUG1_PORT, BSP_DEBUG1_PIN);

	DL_GPIO_initDigitalOutput(BSP_DEBUG2_IOMUX);
	DL_GPIO_enableOutput(BSP_DEBUG2_PORT, BSP_DEBUG2_PIN);
}

/* Global function prototypes ------------------------------------------------*/
void HalMcuPeripheralInit(void)
{
	PeripheralReset();	
	DebugGpioInit();

	#ifdef HAL_MCU_SYS_CLOCK_SOURE_USE_HFXT
	SystemClockConfigure80MhzFromHFCLK();
    #else
	SystemClockConfigure80MhzFromSysOsc();
	#endif
	
	WaitSysClockConfigDone();
}