#include "AppCanTemp.h"/**
  ******************************************************************************
  * @file        AppSerialCanDavinciDetail.h
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2022/2/10
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2022 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

#ifndef _APP_SERIAL_CAN_DAVINCI_DETAIL_H_
#define _APP_SERIAL_CAN_DAVINCI_DETAIL_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "AppCanTemp.h"
#include "SmpCanBusProtocol.h"
#include "SmpParameterId.h"

#ifdef __cplusplus
extern "C" {
#endif
/* Public typedef -----------------------------------------------------------*/
/* Public define ------------------------------------------------------------*/
/* Public macro -------------------------------------------------------------*/

/* Public function prototypes -----------------------------------------------*/
void DavinciCanFunDetailTx(tHalCanFrame *pCanPkg);


#ifdef __cplusplus
}
#endif


	

#endif /* _APP_SERIAL_CAN_DAVINCI_DETAIL_H_ */

/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    




