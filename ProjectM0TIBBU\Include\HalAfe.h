/*
******************************************************************************
* @file     HalAfe.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef HAL_AFE_H_
#define HAL_AFE_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "LibFunctionPointerRegister.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum
{
    kAFE_STATE_NORMAL = 0,
	kAFE_STATE_INI
}eTypeAfeState;

typedef enum
{
	kAFE_EVT_COMM_L1_SET = 1,
	kAFE_EVT_COMM_L1_RELEASE,
	kAFE_EVT_COMM_L2_SET,
	kAFE_EVT_COMM_L2_RELEASE,
	kAFE_EVT_RE_INI,
	kAFE_EVT_END
}eTypeAfeEvent;

typedef enum
{
	kAFE_VBAT_INDEX = 0,
	kAFE_VPACK_INDEX,
	kAFE_VBCAL_INDEX
}eTypeAfeVbatIndex;

typedef enum{
	kCELLV_USE = 1,
	kCELLV_BUSBAR,
	kCELLV_NONE_USE	
}eTypeAfeWhichCellUse;

typedef enum{
	kGPIO_NTC = 1,
	kGPIO_AMBIENT,
	kGPIO_BUSBAR,
	kGPIO_OTHER,
	kGPIO_NONE_USE	
}eTypeAfeWhichNtcUse;



#define NTC_ADC_UPPER_BOUND 4990 // mV

/* Public define ------------------------------------------------------------*/
#define tfpAfeEvtHandler tLibFuncPtrRegEvtHandler
typedef void(*tfpAfeLineLossCallBack)(uint8_t BmuIndex,uint8_t CellChannel, uint16_t *CellVoltage, int16_t BBVoltage);
//

int32_t	halAfeGetCurrentAdcValue(uint8_t u8CurrentIndex);
void HalAfeSetCurrentAdcValue(uint8_t u8CurrentIndex,int32_t i32AdcValue);
int32_t	halAfeGetVBatAdcValue(uint8_t VbIndex);
void HalAfeSetVBatAdcValue(uint8_t u8VbIndex,int32_t i32AdcValue);

uint16_t HalAfeGetCellVoltage(uint16_t u16CellIndex);
int32_t  HalAfeGetCurrentValue(uint8_t u8Index);
uint16_t HalAfeGetNtcVoltage(uint16_t u16NtcIndex);
void HalAfeSetCellVoltage(uint16_t u16Cell, uint16_t u16Voltage);
void HalAfeSetNtcVoltage(uint16_t u16Ntcs, uint16_t u16Voltage);
void HalAfeSetNtcOtherFunctionVoltage(uint8_t u8Fun, uint16_t u16Ntcs, uint16_t u16Voltage);
uint16_t HalAfeGetNtcOtherFunctionVoltage(uint8_t u8Fun, uint16_t u16Ntcs);

void HalAfeSetCurrentValue(uint8_t u8Index, int32_t i32Current);
uint32_t HalAfeGetVBatVoltage(uint8_t u8Index);
void HalAfeSetVBatVoltage(uint8_t u8Index, uint32_t u32Voltage);

uint16_t HalAfeGetMaxCellVoltage(uint8_t *pu8Bmu, uint8_t *pu8Channel, uint16_t *pu16Posi);
uint16_t HalAfeGetMinCellVoltage(uint8_t *pu8Bmu, uint8_t *pu8Channel, uint16_t *pu16Posi);
uint16_t HalAfeGetMinNtcTemp(uint8_t *pu8Bmu, uint8_t *pu8Posi);
uint16_t HalAfeGetMaxNtcTemp(uint8_t *pu8Bmu, uint8_t *pu8Posi);

void HalafeOpen(tfpAfeEvtHandler evtHandler, tfpAfeLineLossCallBack lineLossCb);
uint32_t HalAfeGetPhysicalBalancePosition(uint8_t u8AfeIndex);
void HalAfeSetPhysicalBalancePosition(uint8_t u8AfeIndex, uint32_t u32Position);
uint8_t HalAfeGetBalanceOnFlag(void);
void HalAfeSetBalanceOnFlag(uint8_t u8OnFlag);
uint8_t HalAfeGetState(void);
uint8_t HalAfeIsL1Protect(void);
uint8_t HalAfeIsL2Protect(void);
void HalAfeCalVbatFromCellVoltage(void);
uint8_t HalAfeUpdateModuleVoltage(void);
void HalAfeStartOpenWireTest(void);

uint8_t	IsAfeBridgeFail(void);
void HalAfeSetCellBusbarVoltage(uint8_t u8Index, uint16_t u16Voltage);
uint16_t HalAfeGetCellBusbarVoltage(uint8_t u8Index);

void HalAfeSetNtcBusbarVoltage(uint8_t u8Index, uint16_t u16Voltage);
uint16_t HalAfeGetNtcBusbarVoltage(uint8_t u8Index);

void HalAfeSetNtcAmbientVoltage(uint8_t u8Index, uint16_t u16Voltage);
uint16_t HalAfeGetNtcAmbientVoltage(uint8_t u8Index);

void HalAfeSetNtcOtherVoltage(uint8_t u8Index, uint16_t u16Voltage);
uint16_t HalAfeGetNtcOtherVoltage(uint8_t u8Index);

void logicalCellPositionToPhysicalPosition(uint16_t u16Posi,uint8_t *pu8RetBmu, uint8_t *pu8RetCh);
void logicalNtcPositionToPhysicalPosition(uint16_t u16Posi,uint8_t *pu8RetBmu, uint8_t *pu8RetCh);

void HalAfeHandlerOpen(void);

#ifdef __cplusplus
}
#endif


	

#endif /* HAL_AFE_H_ */

/************************ (C) COPYRIGHT *****END OF FILE****/    
