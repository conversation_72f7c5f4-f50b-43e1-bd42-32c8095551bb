/*
******************************************************************************
* @file     HalMcuPeripheralConfig.h
* <AUTHOR>
* @brief    This file include MCU peripheral configuration.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_MCUPERIPHERALCONFIG_H__
#define __HAL_MCUPERIPHERALCONFIG_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "Bsp.h"
#include "HalCan.h"
/* Global define ------------------------------------------------------------*/
/* Global typedef -----------------------------------------------------------*/
/* Global macro -------------------------------------------------------------*/
#define HAL_MCU_MAIN_CLOCK_FREQUENCY (80) // Unit MHz
#define HAL_MCU_ULPCLK_CLOCK_FREQUENCY (40) // Unit MHz
#define HAL_MCU_HFXCLK_FREQUENCY (40) // Unit MHz
#define HAL_MCU_POWER_STARTUP_DELAY_CYCLE_CNT (16)
/* Global function prototypes -----------------------------------------------*/
void HalMcuPeripheralInit(void);

#ifdef __cplusplus
}
#endif

#endif