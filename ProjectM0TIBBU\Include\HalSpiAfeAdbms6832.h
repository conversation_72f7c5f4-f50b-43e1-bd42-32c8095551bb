/*
******************************************************************************
* @file     HalSpiAfeAdbms6832.h
* <AUTHOR>
* @brief    XXXXXXXXXX

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_SPI_AFE_ADBMS6832_H__
#define	__HAL_SPI_AFE_ADBMS6832_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "HalAfeAdbms6832.h"

/* Global define ------------------------------------------------------------*/
#define HAL_ADBMS6832_CMD_WRCFGA        (0x0001)
#define HAL_ADBMS6832_CMD_WRCFGB        (0x0024)
#define HAL_ADBMS6832_CMD_RDCFGA        (0x0002)
#define HAL_ADBMS6832_CMD_RDCFGB        (0x0026)

#define HAL_ADBMS6832_CMD_RDCVA         (0x0004)
#define HAL_ADBMS6832_CMD_RDCVB         (0x0006)
#define HAL_ADBMS6832_CMD_RDCVC         (0x0008)
#define HAL_ADBMS6832_CMD_RDCVD         (0x000A)
#define HAL_ADBMS6832_CMD_RDCVE         (0x0009)
#define HAL_ADBMS6832_CMD_RDCVF         (0x000B)
#define HAL_ADBMS6832_CMD_RDCVALL       (0x000C)

#define HAL_ADBMS6832_CMD_RDACA         (0x0044)
#define HAL_ADBMS6832_CMD_RDACB         (0x0046)
#define HAL_ADBMS6832_CMD_RDACC         (0x0048)
#define HAL_ADBMS6832_CMD_RDACD         (0x004A)
#define HAL_ADBMS6832_CMD_RDACE         (0x0049)
#define HAL_ADBMS6832_CMD_RDACF         (0x004B)
#define HAL_ADBMS6832_CMD_RDACALL       (0x004C)

#define HAL_ADBMS6832_CMD_RDSVA         (0x0003)
#define HAL_ADBMS6832_CMD_RDSVB         (0x0005)
#define HAL_ADBMS6832_CMD_RDSVC         (0x0007)
#define HAL_ADBMS6832_CMD_RDSVD         (0x000D)
#define HAL_ADBMS6832_CMD_RDSVE         (0x000E)
#define HAL_ADBMS6832_CMD_RDSVF         (0x000F)
#define HAL_ADBMS6832_CMD_RDSALL        (0x0010)

#define HAL_ADBSM6832_CMD_RDCSALL       (0x0011)
#define HAL_ADBSM6832_CMD_RDACSALL      (0x0051)

#define HAL_ADBMS6832_CMD_RDFCA         (0x0012)
#define HAL_ADBMS6832_CMD_RDFCB         (0x0013)
#define HAL_ADBMS6832_CMD_RDFCC         (0x0014)
#define HAL_ADBMS6832_CMD_RDFCD         (0x0015)
#define HAL_ADBMS6832_CMD_RDFCE         (0x0016)
#define HAL_ADBMS6832_CMD_RDFCF         (0x0017)
#define HAL_ADBMS6832_CMD_RDFCALL       (0x0018)

#define HAL_ADBMS6832_CMD_RDAUXA        (0x0019)
#define HAL_ADBMS6832_CMD_RDAUXB        (0x001A)
#define HAL_ADBMS6832_CMD_RDAUXC        (0x001B)
#define HAL_ADBMS6832_CMD_RDAUXD        (0x001F)
#define HAL_ADBMS6832_CMD_RDAUXE        (0x0036)

#define HAL_ADBMS6832_CMD_RDRAXA        (0x001C)
#define HAL_ADBMS6832_CMD_RDRAXB        (0x001D)
#define HAL_ADBMS6832_CMD_RDRAXC        (0x001E)
#define HAL_ADBMS6832_CMD_RDRAXD        (0x0025)

#define HAL_ADBMS6832_CMD_RDSTATA       (0x0030)
#define HAL_ADBMS6832_CMD_RDSTATB       (0x0031)
#define HAL_ADBMS6832_CMD_RDSTATC       (0x0032)    //Without ERR injection for latent fault detection
#define HAL_ADBMS6832_CMD_RDSTATD       (0x0033)
#define HAL_ADBMS6832_CMD_RDSTATE       (0x0034)
#define HAL_ADBMS6832_CMD_RDASALL       (0x0035)

#define HAL_ADBMS6832_CMD_WRPWMA        (0x0020)
#define HAL_ADBMS6832_CMD_RDPWMA        (0x0022)
#define HAL_ADBMS6832_CMD_WRPWMB        (0x0021)
#define HAL_ADBMS6832_CMD_RDPWMB        (0x0023)

#define HAL_ADBMS6832_CMD_CMDIS         (0x0040)
#define HAL_ADBMS6832_CMD_CMEN          (0x0041)
#define HAL_ADBMS6832_CMD_CMHB          (0x0043)

#define HAL_ADBMS6832_CMD_WRCMCFG       (0x0058)
#define HAL_ADBMS6832_CMD_RDCMCFG       (0x0059)
#define HAL_ADBMS6832_CMD_WRCMCELLT     (0x005A)
#define HAL_ADBMS6832_CMD_RDCMCELLT     (0x005B)
#define HAL_ADBMS6832_CMD_WRCMGPIOT     (0x005C)
#define HAL_ADBMS6832_CMD_RDCMGPIOT     (0x005D)
#define HAL_ADBMS6832_CMD_CLRCMFLAG     (0x005E)
#define HAL_ADBMS6832_CMD_RDCMFLAG      (0x005F)

#define HAL_ADBMS6832_CMD_ADCV_BASE     (0x0260)
#define HAL_ADBMS6832_CMD_ADSV_BASE     (0x0168)
#define HAL_ADBMS6832_CMD_ADAX_BASE     (0x0410)
#define HAL_ADBMS6832_CMD_ADAX2_BASE    (0x0400)

#define HAL_ADBMS6832_CMD_CLRCELL       (0x0711)
#define HAL_ADBMS6832_CMD_CLRFC         (0x0714)
#define HAL_ADBMS6832_CMD_CLRAUX        (0x0712)
#define HAL_ADBMS6832_CMD_CLRSPIN       (0x0716)
#define HAL_ADBMS6832_CMD_CLRFLAG       (0x0717)
#define HAL_ADBMS6832_CMD_CLROVUV       (0x0715)

#define HAL_ADBMS6832_CMD_PLADC         (0x0718)
#define HAL_ADBMS6832_CMD_PLCADC        (0x071C)
#define HAL_ADBMS6832_CMD_PLSADC        (0x071D)
#define HAL_ADBMS6832_CMD_PLAUX         (0x071E)
#define HAL_ADBMS6832_CMD_PLAUX2        (0x071F)

#define HAL_ADBMS6832_CMD_WRCOMM        (0x0721)
#define HAL_ADBMS6832_CMD_RDCOMM        (0x0722)
#define HAL_ADBMS6832_CMD_STCOMM        (0x0723)

#define HAL_ADBMS6832_CMD_MUTEBAL       (0x0028)
#define HAL_ADBMS6832_CMD_UNMUTEBAL     (0x0029)

#define HAL_ADBMS6832_CMD_RDSID         (0x002C)
#define HAL_ADBMS6832_CMD_RSTCC         (0x002E)

#define HAL_ADBMS6832_CMD_SNAP          (0x002D)
#define HAL_ADBMS6832_CMD_UNSNAP        (0x002F)

#define HAL_ADBMS6832_CMD_SRST          (0x0027)

#define ADAX_OW                         (0)
#define ADAX_PUP                        (0)
#define ADAX_CH                         (0)

#define ADCV_RD                         (0)
#define ADCV_CONT                       (1)
#define ADCV_DCP                        (0)
#define ADCV_RSTF                       (0)
#define ADCV_OW                         (0)

/// [CH] : (DATA = 6 Bytes + PEC = 2 Bytes)  
#define HAL_ADBMS6832_DATA_BYTE_NUM              (8)                       
#define HAL_ADBMS6832_SPI_RX_BUFFER_SIZE         (HAL_ADBMS6832_MAX_AFE_IC_NUM * HAL_ADBMS6832_DATA_BYTE_NUM)

/// [CH] : 3 Cell in every Cell Register, 2 Byte in every Cell, Total 6 Byte	
#define HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG        (6)
/// [CH] : 3 Cell in every Cell Register	
#define HAL_ADBMS6832_CELL_NUM_IN_AFE_REG        (3)
#define HAL_ADBMS6832_DECODE_BUFFER_SIZE         (HAL_ADBMS6832_CELL_NUM_IN_AFE_REG * HAL_ADBMS6832_MAX_AFE_IC_NUM)  

/* Global typedef -----------------------------------------------------------*/
typedef void(*tfpHalAfeAdbms6832CbFun)(void);

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void HalSpiAfeAdbms6832Init(void);
void HalAfeAdbms6832StartWakeupIdle(void);
void HalAfeAdbms6832SetCommDir(eTypeHalAfeAdbms6832CommDirection eAfeCommDir);
void HalAfeAdbms6832StartCellAdc(void);
void HalAfeAdbms6832StartGpioAdc(void);
void HalAfeAdbms6832ReadCellV(uint16_t u16AfeCmd);
void HalAfeAdbms6832ReadAuXi(uint16_t u16AfeCmd);
void HalAfeAdbms6832EventRegisterCb(tfpHalAfeAdbms6832CbFun fpCbFun);
uint8_t HalAfeAdbms6832GetAfeOnlineNum(void);
uint16_t* HalAfeAdbms6832GetDecodeAfeRxDataBuf(void);
void HalAfeAdbms6832ReadCfga(eTypeHalAfeAdbms6832State eAfeState);
void HalAfeAdbms6832ReadCfgb(void);
void HalAfeAdbms6832WriteCfgaRefon(void);
void HalAfeAdbms6832StartCellBalance(void);
void HalAfeAdbms6832StopCellBalance(void);
void HalAfeAdbms6832SetCfgaCfgbDccDcto(uint8_t u8AfeIdx, bool* pbDccCell);
void HalAfeAdbms6832WriteCfgaDccDcto(void);
void HalAfeAdbms6832WriteCfgbDccDcto(void);

#ifdef __cplusplus
}
#endif

#endif