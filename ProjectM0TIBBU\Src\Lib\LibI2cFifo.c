/*
******************************************************************************
* @file     LibI2cFifo.c
* <AUTHOR>
* @brief    This file include MSPM0G3519 I2c FIFO Library Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "LibI2cFifo.h"
#include "LibFunctionReturnValueDefine.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
/* Local function prototypes ------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
int8_t LibI2cFifoPush(tLibI2cFifoStatus* pmI2cFifo, uint8_t u8Value)
{
	if ( ((pmI2cFifo->u16FifoPushInPosi + 1) % pmI2cFifo->u16FifoSize) == pmI2cFifo->u16FifoPopOutPosi )
    {
		return RES_ERROR_NULL; //Buf full
	}
    else
    {
		pmI2cFifo->pu8FifoStartAddr[pmI2cFifo->u16FifoPushInPosi] = u8Value;
		pmI2cFifo->u16FifoPushInPosi = ( (pmI2cFifo->u16FifoPushInPosi + 1) % pmI2cFifo->u16FifoSize );
	}
	return RES_SUCCESS;
}

int8_t LibI2cFifoPop(tLibI2cFifoStatus* pmI2cFifo, uint8_t* pu8Value)
{
	if (pmI2cFifo->u16FifoPushInPosi == pmI2cFifo->u16FifoPopOutPosi)
    {
		return RES_ERROR_RESOURCES; //Buf empty
	}
    else
    {
		*pu8Value = pmI2cFifo->pu8FifoStartAddr[pmI2cFifo->u16FifoPopOutPosi];
		pmI2cFifo->u16FifoPopOutPosi = ( (pmI2cFifo->u16FifoPopOutPosi + 1) % pmI2cFifo->u16FifoSize );
	}		
	return RES_SUCCESS;
}

int8_t LibI2cFifoGetUsedSize(tLibI2cFifoStatus* pmI2cFifo, uint16_t *pu16UsedSize)
{
    if (pmI2cFifo->u16FifoPushInPosi == pmI2cFifo->u16FifoPopOutPosi)
    {
		return RES_ERROR_RESOURCES; //Buf empty
	}
    else if (pmI2cFifo->u16FifoPushInPosi > pmI2cFifo->u16FifoPopOutPosi)
    {
		*pu16UsedSize = (pmI2cFifo->u16FifoPushInPosi - pmI2cFifo->u16FifoPopOutPosi);
	}
    else
    {
		*pu16UsedSize = ( (pmI2cFifo->u16FifoSize - pmI2cFifo->u16FifoPopOutPosi) + (pmI2cFifo->u16FifoPushInPosi) );
	}	
	return RES_SUCCESS;
}