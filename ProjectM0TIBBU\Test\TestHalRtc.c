/*
******************************************************************************
* @file     TestHalRtc.c
* <AUTHOR>
* @brief    This file is Test HalRtc function

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "LibSoftwareTimerHandler.h"
#include "LibHardwareTimerHandler.h"
#include "TestHalRtc.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/

#if 1
#define TEST_HALRTC_DEBUG_IO
#endif

#if 1
#define TEST_HALRTC_EVB_LED_IO
#endif

/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
static void TestHalRtcExecutionFun(void);
static void TestHalRtcCallFun(void);
static void TestHalRtcCallFun(void);
static void TestHalRtcAppSwTimerHandler(__far void *pvDest, uint16_t u16Event, void *pvData);
static void TestHalRtcAppOpen(void);
/* Global variables ---------------------------------------------------------*/
/* Local function prototypes ------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/

//Test RTC
//---------------------------------------------------------
static void TestHalRtcExecutionFun(void)
{
    tHalRtcDateTime mDateTime  ={0};
    tHalRtcDateTime mDateTime2 ={0};
    uint32_t u32UnixTime =0;
    bool bRtcValid = false;

    HalRtcGetDateTime(&mDateTime); 

    bRtcValid = HalRtcIsRtcValid();
    if(bRtcValid == true)
    {
        HalRtcGetDateTime(&mDateTime);    
        u32UnixTime = HalRtcGetSelfUnixTime();    
        HalRtcSelfUnixTimeToDateTime(u32UnixTime, &mDateTime2);
        HalRtcSelfUnixTimeToDateTime(u32UnixTime, &mDateTime2);

        #ifdef TEST_HALRTC_EVB_LED_IO
        HalGpioTogglePin(BSP_EVB_BLUE_LED_PORT, BSP_EVB_BLUE_LED_PIN);
        #endif
    }
}

static void TestHalRtcCallFun(void)
{
    HalGpioTogglePin(BSP_DEBUG0_PORT, BSP_DEBUG0_PIN);
    /*Test and turn off the RTC function after N seconds */
    if(HalRtcGetPrescaler1Cnt() >= TEST_HAL_RTC_ON_AFTER_OFF_TIME_CNT)
    {
         //Close RTC
        HalRtcClose();

        #ifdef TEST_HALRTC_DEBUG_IO
        HalGpioTogglePin(BSP_DEBUG2_PORT, BSP_DEBUG2_PIN);
        #endif
    }
}

static void TestHalRtcInit(void)
{
    tHalRtcDateTime mDateTime  ={0};
    tFunRetunCode mFunRetcode = RES_ERROR_INIT;

    /* Test HalRtcClose */
    //-------------------------------------
    #ifdef TEST_HAL_RTC_HALRTCCLOSE_FUN
    HalRtcRegisterPrescaler1Callback(TestHalRtcCallFun);
    #endif 
    //-------------------------------------

    //Open RTC
    mFunRetcode = HalRtcOpen();

    if(mFunRetcode == RES_ERROR_INIT)
    {
        //3519 EVB RED LED 
        #ifdef TEST_HALRTC_EVB_LED_IO
        HalGpioSetPin(BSP_EVB_RED_LED_PORT, BSP_EVB_RED_LED_PIN);    
        #endif
    }

    HalRtcGetDateTime(&mDateTime); 
    HalRtcGetDateTime(&mDateTime); 

    HalRtcSetPrescaler1Cnt(0);

    //Setup Date and Time for Test
    //--------------------------------------
    HalRtcSetupDate(TEST_HAL_RTC_SETUP_YEAR, TEST_HAL_RTC_SETUP_MONTH, TEST_HAL_RTC_SETUP_DAY);
    HalRtcSetupTime(TEST_HAL_RTC_SETUP_HOUR, TEST_HAL_RTC_SETUP_MINUTE, TEST_HAL_RTC_SETUP_SECOND);
    //--------------------------------------
}

static void TestHalRtcAppSwTimerHandler(__far void *pvDest, uint16_t u16Event, void *pvData)
{
    if (u16Event & kLIB_SW_TIMER_EVT_1_S)
    {
        TestHalRtcExecutionFun();
    }
}

static void TestHalRtcAppOpen(void)
{
    //Can be used if the previous action is not enabled
    //--------------------------------------------------------- 
    #if 0
    HalMcuPeripheralInit();
    HalHardwareTimerOpen(APP_HARDWARE_TIMER_TASK_PERIOD, AppTemporaryHalTimerHandler);
    LibHardwareTimerHandlerOpen(AppProjectHwTimerHandler, 0);
    #endif
    //--------------------------------------------------------- 

    LibSoftwareTimerHandlerOpen(TestHalRtcAppSwTimerHandler, 0);
}

void TestHalRtcApp(void){
    
   TestHalRtcAppOpen();   
   TestHalRtcInit();
}
