/*
******************************************************************************
* @file     ApiStackMemCalc.h
* <AUTHOR>
* @brief    This file is Stack memory size estimation.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __API_STACK_MEM_CALC_H__
#define __API_STACK_MEM_CALC_H__

#ifdef __cplusplus
extern "C" {
#endif
/* Includes -----------------------------------------------------------------*/
#include <stdint.h>
#include "LibFunctionReturnValueDefine.h"
/* Global define ------------------------------------------------------------*/
#define API_STACK_MEM_CALC_SKIP_CNT                (240000 * 20)   //About 20secs
#define API_STACK_MEM_FILL_PATTERN                 (0xAAAAAAAA)

/* Global typedef -----------------------------------------------------------*/
void ApiStackMemFillStack(void);
uint32_t ApiStackMemGetUsedSize(void);
uint32_t ApiStackMemGetFreeSize(void);
tFunRetunCode ApiStackMemCalcExe(uint32_t* pu32StacUseSize);

#ifdef __cplusplus
}
#endif
#endif