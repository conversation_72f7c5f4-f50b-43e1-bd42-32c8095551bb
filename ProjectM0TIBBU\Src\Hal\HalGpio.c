/*
******************************************************************************
* @file     Hal<PERSON><PERSON>.c
* <AUTHOR>
* @brief    This file include MSPM0G3519 GPIO Hardwarre Abstraction Layer Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "HalGpio.h"
#include "LibFunctionReturnValueDefine.h"
#include "Bsp.h" 

/* Local function declare ---------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void HalGpioPowerInit(void)
{
    //DL_GPIO_reset(GPIOA);
    //DL_GPIO_reset(GPIOB);
    //DL_GPIO_reset(GPIOC);

    DL_GPIO_enablePower(GPIOA);
    DL_GPIO_enablePower(GPIOB);
    DL_GPIO_enablePower(GPIOC);

    delay_cycles(POWER_STARTUP_DELAY);
}

static uint32_t halGpioFindIomux(const GPIO_Regs* pmPort, uint32_t u32Pin)
{
    uint32_t u32Iomux;


    if (pmPort == GPIOA)
    {
        switch (u32Pin)
        {
            case (DL_GPIO_PIN_0):
                u32Iomux = IOMUX_PINCM1;
                break;
            case (DL_GPIO_PIN_1):
                u32Iomux = IOMUX_PINCM2;
                break;
            case (DL_GPIO_PIN_2):
                u32Iomux = IOMUX_PINCM7;
                break;
            case (DL_GPIO_PIN_3):
                u32Iomux = IOMUX_PINCM8;
                break;
            case (DL_GPIO_PIN_4):
                u32Iomux = IOMUX_PINCM9;
                break;
            case (DL_GPIO_PIN_5):
                u32Iomux = IOMUX_PINCM10;
                break;
            case (DL_GPIO_PIN_6):
                u32Iomux = IOMUX_PINCM11;
                break;
            case (DL_GPIO_PIN_7):
                u32Iomux = IOMUX_PINCM14;
                break;
            case (DL_GPIO_PIN_8):
                u32Iomux = IOMUX_PINCM19;
                break;
            case (DL_GPIO_PIN_9):
                u32Iomux = IOMUX_PINCM20;
                break;
            case (DL_GPIO_PIN_10):
                u32Iomux = IOMUX_PINCM21;
                break;
            case (DL_GPIO_PIN_11):
                u32Iomux = IOMUX_PINCM22;
                break;
            case (DL_GPIO_PIN_12):
                u32Iomux = IOMUX_PINCM34;
                break;
            case (DL_GPIO_PIN_13):
                u32Iomux = IOMUX_PINCM35;
                break;
            case (DL_GPIO_PIN_14):
                u32Iomux = IOMUX_PINCM36;
                break;
            case (DL_GPIO_PIN_15):
                u32Iomux = IOMUX_PINCM37;
                break;
            case (DL_GPIO_PIN_16):
                u32Iomux = IOMUX_PINCM38;
                break;
            case (DL_GPIO_PIN_17):
                u32Iomux = IOMUX_PINCM39;
                break;
            case (DL_GPIO_PIN_18):
                u32Iomux = IOMUX_PINCM40;
                break;
            case (DL_GPIO_PIN_19):
                u32Iomux = IOMUX_PINCM41;
                break;
            case (DL_GPIO_PIN_20):
                u32Iomux = IOMUX_PINCM42;
                break;
            case (DL_GPIO_PIN_21):
                u32Iomux = IOMUX_PINCM46;
                break;
            case (DL_GPIO_PIN_22):
                u32Iomux = IOMUX_PINCM47;
                break;
            case (DL_GPIO_PIN_23):
                u32Iomux = IOMUX_PINCM53;
                break;
            case (DL_GPIO_PIN_24):
                u32Iomux = IOMUX_PINCM54;
                break;
            case (DL_GPIO_PIN_25):
                u32Iomux = IOMUX_PINCM55;
                break;
            case (DL_GPIO_PIN_26):
                u32Iomux = IOMUX_PINCM59;
                break;
            case (DL_GPIO_PIN_27):
                u32Iomux = IOMUX_PINCM60;
                break;
            case (DL_GPIO_PIN_28):
                u32Iomux = IOMUX_PINCM3;
                break;
            case (DL_GPIO_PIN_29):
                u32Iomux = IOMUX_PINCM4;
                break;
            case (DL_GPIO_PIN_30):
                u32Iomux = IOMUX_PINCM5;
                break;
            case (DL_GPIO_PIN_31):
                u32Iomux = IOMUX_PINCM6;
                break;           
            default:
                u32Iomux = RES_ERROR_NOT_FOUND;
                break;                
        }
    }
    else if (pmPort == GPIOB)
    {
        switch (u32Pin)
        {
            case (DL_GPIO_PIN_0):
                u32Iomux = IOMUX_PINCM12;
                break;
            case (DL_GPIO_PIN_1):
                u32Iomux = IOMUX_PINCM13;
                break;
            case (DL_GPIO_PIN_2):
                u32Iomux = IOMUX_PINCM15;
                break;
            case (DL_GPIO_PIN_3):
                u32Iomux = IOMUX_PINCM16;
                break;
            case (DL_GPIO_PIN_4):
                u32Iomux = IOMUX_PINCM17;
                break;
            case (DL_GPIO_PIN_5):
                u32Iomux = IOMUX_PINCM18;
                break;
            case (DL_GPIO_PIN_6):
                u32Iomux = IOMUX_PINCM23;
                break;
            case (DL_GPIO_PIN_7):
                u32Iomux = IOMUX_PINCM24;
                break;
            case (DL_GPIO_PIN_8):
                u32Iomux = IOMUX_PINCM25;
                break;
            case (DL_GPIO_PIN_9):
                u32Iomux = IOMUX_PINCM26;
                break;
            case (DL_GPIO_PIN_10):
                u32Iomux = IOMUX_PINCM27;
                break;
            case (DL_GPIO_PIN_11):
                u32Iomux = IOMUX_PINCM28;
                break;
            case (DL_GPIO_PIN_12):
                u32Iomux = IOMUX_PINCM29;
                break;
            case (DL_GPIO_PIN_13):
                u32Iomux = IOMUX_PINCM30;
                break;
            case (DL_GPIO_PIN_14):
                u32Iomux = IOMUX_PINCM31;
                break;
            case (DL_GPIO_PIN_15):
                u32Iomux = IOMUX_PINCM32;
                break;
            case (DL_GPIO_PIN_16):
                u32Iomux = IOMUX_PINCM33;
                break;
            case (DL_GPIO_PIN_17):
                u32Iomux = IOMUX_PINCM43;
                break;
            case (DL_GPIO_PIN_18):
                u32Iomux = IOMUX_PINCM44;
                break;
            case (DL_GPIO_PIN_19):
                u32Iomux = IOMUX_PINCM45;
                break;
            case (DL_GPIO_PIN_20):
                u32Iomux = IOMUX_PINCM48;
                break;
            case (DL_GPIO_PIN_21):
                u32Iomux = IOMUX_PINCM49;
                break;
            case (DL_GPIO_PIN_22):
                u32Iomux = IOMUX_PINCM50;
                break;
            case (DL_GPIO_PIN_23):
                u32Iomux = IOMUX_PINCM51;
                break;
            case (DL_GPIO_PIN_24):
                u32Iomux = IOMUX_PINCM52;
                break;
            case (DL_GPIO_PIN_25):
                u32Iomux = IOMUX_PINCM56;
                break;
            case (DL_GPIO_PIN_26):
                u32Iomux = IOMUX_PINCM57;
                break;
            case (DL_GPIO_PIN_27):
                u32Iomux = IOMUX_PINCM58;
                break;
            case (DL_GPIO_PIN_28):
                u32Iomux = IOMUX_PINCM65;
                break;
            case (DL_GPIO_PIN_29):
                u32Iomux = IOMUX_PINCM66;
                break;
            case (DL_GPIO_PIN_30):
                u32Iomux = IOMUX_PINCM67;
                break;
            case (DL_GPIO_PIN_31):
                u32Iomux = IOMUX_PINCM68;
                break;
            default:
                u32Iomux = RES_ERROR_NOT_FOUND;
                break;                          
        }
    }
    else if (pmPort == GPIOC)
    {
        switch (u32Pin)
        {
            case (DL_GPIO_PIN_0):
                u32Iomux = IOMUX_PINCM74;
                break;
            case (DL_GPIO_PIN_1):
                u32Iomux = IOMUX_PINCM75;
                break;
            case (DL_GPIO_PIN_2):
                u32Iomux = IOMUX_PINCM76;
                break;
            case (DL_GPIO_PIN_3):
                u32Iomux = IOMUX_PINCM77;
                break;
            case (DL_GPIO_PIN_4):
                u32Iomux = IOMUX_PINCM78;
                break;
            case (DL_GPIO_PIN_5):
                u32Iomux = IOMUX_PINCM79;
                break;
            case (DL_GPIO_PIN_6):
                u32Iomux = IOMUX_PINCM84;
                break;
            case (DL_GPIO_PIN_7):
                u32Iomux = IOMUX_PINCM85;
                break;
            case (DL_GPIO_PIN_8):
                u32Iomux = IOMUX_PINCM86;
                break;
            case (DL_GPIO_PIN_9):
                u32Iomux = IOMUX_PINCM87;
                break;
            case (DL_GPIO_PIN_10):
                u32Iomux = IOMUX_PINCM88;
                break;
            case (DL_GPIO_PIN_11):
                u32Iomux = IOMUX_PINCM89;
                break;
            case (DL_GPIO_PIN_12):
                u32Iomux = IOMUX_PINCM61;
                break;
            case (DL_GPIO_PIN_13):
                u32Iomux = IOMUX_PINCM62;
                break;
            case (DL_GPIO_PIN_14):
                u32Iomux = IOMUX_PINCM63;
                break;
            case (DL_GPIO_PIN_15):
                u32Iomux = IOMUX_PINCM64;
                break;
            case (DL_GPIO_PIN_16):
                u32Iomux = IOMUX_PINCM69;
                break;
            case (DL_GPIO_PIN_17):
                u32Iomux = IOMUX_PINCM70;
                break;
            case (DL_GPIO_PIN_18):
                u32Iomux = IOMUX_PINCM71;
                break;
            case (DL_GPIO_PIN_19):
                u32Iomux = IOMUX_PINCM72;
                break;
            case (DL_GPIO_PIN_20):
                u32Iomux = IOMUX_PINCM73;
                break;
            case (DL_GPIO_PIN_21):
                u32Iomux = IOMUX_PINCM80;
                break;
            case (DL_GPIO_PIN_22):
                u32Iomux = IOMUX_PINCM81;
                break;
            case (DL_GPIO_PIN_23):
                u32Iomux = IOMUX_PINCM82;
                break;
            case (DL_GPIO_PIN_24):
                u32Iomux = IOMUX_PINCM83;
                break;
            case (DL_GPIO_PIN_25):
                u32Iomux = IOMUX_PINCM90;
                break;
            case (DL_GPIO_PIN_26):
                u32Iomux = IOMUX_PINCM91;
                break;
            case (DL_GPIO_PIN_27):
                u32Iomux = IOMUX_PINCM92;
                break;
            case (DL_GPIO_PIN_28):
                u32Iomux = IOMUX_PINCM93;
                break;
            case (DL_GPIO_PIN_29):
                u32Iomux = IOMUX_PINCM94;
                break;
            default:
                u32Iomux = RES_ERROR_NOT_FOUND;
                break;                    
        }
    }
    else 
    {
        u32Iomux = RES_ERROR_NOT_FOUND;
    }

    return (u32Iomux);
}

void HalGpioInit(tHalGpioConfig* pmHalGpioConfig)
{
    uint32_t u32Iomux;
    tHalDlGpioConfig mHalDlGpioConfig;


    u32Iomux = halGpioFindIomux(pmHalGpioConfig->pmPort, pmHalGpioConfig->u32Pin);

    if (u32Iomux == RES_ERROR_NOT_FOUND)
    {
        return;
    }

    mHalDlGpioConfig.eDlInversionType = (DL_GPIO_INVERSION)pmHalGpioConfig->eInversionType;
    mHalDlGpioConfig.eDlResistorType = (DL_GPIO_RESISTOR)pmHalGpioConfig->eResistorType;
    mHalDlGpioConfig.eDlHysteresisType = (DL_GPIO_HYSTERESIS)pmHalGpioConfig->eHysteresisType;
    mHalDlGpioConfig.eDlWakeUpType = (DL_GPIO_WAKEUP)pmHalGpioConfig->eWakeUpType;
    mHalDlGpioConfig.eDlDriveStrengthType = (DL_GPIO_DRIVE_STRENGTH)pmHalGpioConfig->eDriveStrengthType;
    mHalDlGpioConfig.eDlHizType = (DL_GPIO_HIZ)pmHalGpioConfig->eHizType;

    if (pmHalGpioConfig->eGpioIoType == kHAL_GPIO_INPUT)
    {
        DL_GPIO_initDigitalInputFeatures(u32Iomux, mHalDlGpioConfig.eDlInversionType, 
        mHalDlGpioConfig.eDlResistorType, mHalDlGpioConfig.eDlHysteresisType, mHalDlGpioConfig.eDlWakeUpType);
    }
    else if (pmHalGpioConfig->eGpioIoType == kHAL_GPIO_OUTPUT)
    {
        DL_GPIO_initDigitalOutputFeatures(u32Iomux, mHalDlGpioConfig.eDlInversionType, 
        mHalDlGpioConfig.eDlResistorType, mHalDlGpioConfig.eDlDriveStrengthType, mHalDlGpioConfig.eDlHizType);

        DL_GPIO_enableOutput(pmHalGpioConfig->pmPort, pmHalGpioConfig->u32Pin); 
    }
}

void HalGpioSetPin(GPIO_Regs* pmPort, uint32_t u32Pin)
{
    DL_GPIO_setPins(pmPort, u32Pin);
}

void HalGpioClearPin(GPIO_Regs* pmPort, uint32_t u32Pin)
{
    DL_GPIO_clearPins(pmPort, u32Pin); 
}

void HalGpioTogglePin(GPIO_Regs* pmPort, uint32_t u32Pin)
{
    DL_GPIO_togglePins(pmPort, u32Pin);
}

void HalGpioGetPin(GPIO_Regs* pmPort, uint32_t u32Pin)
{
    DL_GPIO_readPins(pmPort, u32Pin);    
}

void HalGpioInitExample(void)
 {
    tHalGpioConfig mHalGpioConfig;

    
    HalGpioPowerInit();

    mHalGpioConfig.eGpioIoType = kHAL_GPIO_OUTPUT;
    mHalGpioConfig.pmPort = BSP_LED_PORT;
    mHalGpioConfig.u32Pin = BSP_LED1_PIN;
    mHalGpioConfig.eInversionType = kHAL_GPIO_INVERSION_DISABLE;
    mHalGpioConfig.eResistorType = kHAL_GPIO_RESISTOR_NONE;
    mHalGpioConfig.eHysteresisType = kHAL_GPIO_HYSTERESIS_DISABLE;
    mHalGpioConfig.eWakeUpType = kHAL_GPIO_WAKEUP_DISABLE;
    mHalGpioConfig.eDriveStrengthType = kHAL_GPIO_DRIVE_STRENGTH_LOW;
    mHalGpioConfig.eHizType = kHAL_GPIO_HIZ_DISABLE;
    HalGpioInit(&mHalGpioConfig);

    mHalGpioConfig.eGpioIoType = kHAL_GPIO_OUTPUT;
    mHalGpioConfig.pmPort = BSP_LED_PORT;
    mHalGpioConfig.u32Pin = BSP_LED2_PIN;
    mHalGpioConfig.eInversionType = kHAL_GPIO_INVERSION_DISABLE;
    mHalGpioConfig.eResistorType = kHAL_GPIO_RESISTOR_NONE;
    mHalGpioConfig.eHysteresisType = kHAL_GPIO_HYSTERESIS_DISABLE;
    mHalGpioConfig.eWakeUpType = kHAL_GPIO_WAKEUP_DISABLE;
    mHalGpioConfig.eDriveStrengthType = kHAL_GPIO_DRIVE_STRENGTH_LOW;
    mHalGpioConfig.eHizType = kHAL_GPIO_HIZ_DISABLE;
    HalGpioInit(&mHalGpioConfig);

    mHalGpioConfig.eGpioIoType = kHAL_GPIO_OUTPUT;
    mHalGpioConfig.pmPort = BSP_LED_PORT;
    mHalGpioConfig.u32Pin = BSP_LED3_PIN;
    mHalGpioConfig.eInversionType = kHAL_GPIO_INVERSION_DISABLE;
    mHalGpioConfig.eResistorType = kHAL_GPIO_RESISTOR_NONE;
    mHalGpioConfig.eHysteresisType = kHAL_GPIO_HYSTERESIS_DISABLE;
    mHalGpioConfig.eWakeUpType = kHAL_GPIO_WAKEUP_DISABLE;
    mHalGpioConfig.eDriveStrengthType = kHAL_GPIO_DRIVE_STRENGTH_LOW;
    mHalGpioConfig.eHizType = kHAL_GPIO_HIZ_DISABLE;
    HalGpioInit(&mHalGpioConfig);
 }

 void HalGpioToggleExample(void)
 {
    HalGpioTogglePin(BSP_LED_PORT, BSP_LED1_PIN | BSP_LED2_PIN | BSP_LED3_PIN);
 }