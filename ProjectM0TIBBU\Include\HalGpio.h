/*
******************************************************************************
* @file     HalGpio.h
* <AUTHOR>
* @brief    This file include MSPM0G3519 GPIO Hardwarre Abstraction Layer Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_GPIO_H__
#define __HAL_GPIO_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h" 

/* Global define ------------------------------------------------------------*/
#define POWER_STARTUP_DELAY                                                (16)

/* Global typedef -----------------------------------------------------------*/
typedef enum 
{
    kHAL_GPIO_INPUT = 0,
    kHAL_GPIO_OUTPUT,
}eTypeHalGpioIo;

typedef enum 
{
    kHAL_GPIO_INVERSION_DISABLE = DL_GPIO_INVERSION_DISABLE,
    kHAL_GPIO_INVERSION_ENABLE = DL_GPIO_INVERSION_ENABLE,
}eTypeHalGpioInversion;

typedef enum 
{
    kHAL_GPIO_RESISTOR_NONE = DL_GPIO_RESISTOR_NONE,
    kHAL_GPIO_RESISTOR_PULL_UP = DL_GPIO_RESISTOR_PULL_UP,
    kHAL_GPIO_RESISTOR_PULL_DOWN = DL_GPIO_RESISTOR_PULL_DOWN,
}eTypeHalGpioResistor;

typedef enum 
{
    kHAL_GPIO_HYSTERESIS_DISABLE = DL_GPIO_HYSTERESIS_DISABLE,
    kHAL_GPIO_HYSTERESIS_ENABLE = DL_GPIO_HYSTERESIS_ENABLE,
}eTypeHalGpioHysteresis;

typedef enum 
{
    kHAL_GPIO_WAKEUP_DISABLE = DL_GPIO_WAKEUP_DISABLE,
    kHAL_GPIO_WAKEUP_ENABLE_0 = DL_GPIO_WAKEUP_ON_0,
    kHAL_GPIO_WAKEUP_ENABLE_1 = DL_GPIO_WAKEUP_ON_1,

}eTypeHalGpioWakeUp;

typedef enum 
{
    kHAL_GPIO_DRIVE_STRENGTH_LOW = DL_GPIO_DRIVE_STRENGTH_LOW,
    kHAL_GPIO_DRIVE_STRENGTH_HIGH = DL_GPIO_DRIVE_STRENGTH_HIGH,        
}eTypeHalGpioDriveStrength;

typedef enum 
{    
    kHAL_GPIO_HIZ_DISABLE = DL_GPIO_HIZ_DISABLE,
    kHAL_GPIO_HIZ_ENABLE = DL_GPIO_HIZ_ENABLE,
}eTypeHalGpioHiz;

typedef struct
{
    eTypeHalGpioIo eGpioIoType;
    GPIO_Regs* pmPort;
    uint32_t u32Pin;
    eTypeHalGpioInversion eInversionType;
    eTypeHalGpioResistor eResistorType;
    eTypeHalGpioHysteresis eHysteresisType;
    eTypeHalGpioWakeUp eWakeUpType;
    eTypeHalGpioDriveStrength eDriveStrengthType;       
    eTypeHalGpioHiz eHizType;
}tHalGpioConfig;

typedef struct
{
    DL_GPIO_INVERSION eDlInversionType;
    DL_GPIO_RESISTOR eDlResistorType;
    DL_GPIO_HYSTERESIS eDlHysteresisType;
    DL_GPIO_WAKEUP eDlWakeUpType;
    DL_GPIO_DRIVE_STRENGTH eDlDriveStrengthType; 
    DL_GPIO_HIZ eDlHizType;
}tHalDlGpioConfig;

/* Global function prototypes -----------------------------------------------*/
void HalGpioPowerInit(void);
void HalGpioInit(tHalGpioConfig* pmHalGpioConfig);
void HalGpioSetPin(GPIO_Regs* pmPort, uint32_t u32Pin);
void HalGpioClearPin(GPIO_Regs* pmPort, uint32_t u32Pin);
void HalGpioTogglePin(GPIO_Regs* pmPort, uint32_t u32Pin);
void HalGpioGetPin(GPIO_Regs* pmPort, uint32_t u32Pin);
void HalGpioInitExample(void);
void HalGpioToggleExample(void);

#ifdef __cplusplus
}
#endif

#endif