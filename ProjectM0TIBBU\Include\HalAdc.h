/*
******************************************************************************
* @file     HalAdc.h
* <AUTHOR>
* @brief    This file is the HAL common function of ADC internal

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_ADC_H__
#define	__HAL_ADC_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "Bsp.h"
#include "LibFunctionReturnValueDefine.h"
//#include "HalGpio.h"

/* Global define ------------------------------------------------------------*/
#define HAL_ADC_POWER_STARTUP_DELAY                (16)

/* Global typedef -----------------------------------------------------------*/
typedef enum
{
	kHAL_ADC_SAMPLETIME_1MS = 0,
	kHAL_ADC_SAMPLETIME_2MS,			
	kHAL_ADC_SAMPLETIME_5MS,
    kHAL_ADC_SAMPLETIME_10MS,		
	kHAL_ADC_SAMPLETIME_50MS,
	kHAL_ADC_SAMPLETIME_100MS,
}eTypeHalAdcSampleTime;

typedef struct
{
    GPIO_Regs*   pmGpioPort;
    uint32_t     u32GpioPin; 
}tHalAdcPortPin;

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void HalAdcOpen(void);
void HalAdcClose(void);
tFunRetunCode HalAdcSetPinRegister(GPIO_Regs* pmGpioPort, uint32_t u32GpioPin);
void HalAdcClearAllPinRegister(void);
tFunRetunCode HalAdcStart(eTypeHalAdcSampleTime eHalAdcSampleTime);
uint16_t HalAdcGetData(GPIO_Regs* pmGpioPort, uint32_t u32GpioPin);

#ifdef __cplusplus
}

#endif

#endif