#ifndef __DEFINE_H__
#define __DEFINE_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
/* Global define ------------------------------------------------------------*/
#define	MAX_CELL_NUMBER		    (144)
#define	MAX_NTC_NUMBER		    (40)
#define	MAX_BMU_NUM			    (8)

#define	MAX_CELL_BUSBAR_NUMBER	(10)
#define	MAX_NTC_BUSBAR_NUMBER	(10)
#define	MAX_NTC_AMBIENT_NUMBER	(10)
#define	MAX_NTC_OTHER_NUMBER	(10)
/* Global typedef -----------------------------------------------------------*/
typedef union
{
    uint8_t u8[8];
    uint16_t u16[4];
    uint32_t u32[2];
    uint64_t u64;
} tUnionU64Bits;

typedef union
{
    uint8_t u8[4];
    uint16_t u16[2];
    uint32_t u32;
} tUnionU32Bits;

typedef union
{
    uint8_t u8[2];
    uint16_t u16;
} tUnionU16Bits;

typedef union
{
    int8_t i8[8];
    int16_t i16[4];
    int32_t i32[2];
    int64_t i64;
    uint8_t u8[8];
    uint16_t u16[4];
    uint32_t u32[2];
    uint64_t u64;
} tUnion64Bits;

typedef union
{
    int8_t i8[4];
    int16_t i16[2];
    int32_t i32;
    uint8_t u8[4];
    uint16_t u16[2];
    uint32_t u32;
} tUnion32Bits;

typedef union
{
    int8_t i8[2];
    int16_t i16;
    uint8_t u8[2];
    uint16_t u16;
} tUnion16Bits;
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
#define GET_BYTE(Address)      ((uint8_t) (*(uint8_t *) Address))
#define GET_WORD(Address)      ((((uint16_t) (*(uint8_t *) (Address + 1))) << 8) + (((uint16_t) (*(uint8_t *) (Address + 0)))))
#define GET_DWORD(Address)     ((((uint32_t) (*(uint8_t *) (Address + 3))) << 24) + (((uint32_t) (*(uint8_t *) (Address + 2))) << 16) + (((uint32_t) (*(uint8_t *) (Address + 1))) << 8) + (((uint32_t) (*(uint8_t *) (Address + 0)))))

#ifdef __cplusplus
}
#endif

#endif