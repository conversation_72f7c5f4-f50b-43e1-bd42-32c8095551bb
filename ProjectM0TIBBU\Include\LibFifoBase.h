/*
******************************************************************************
* @file     LibFifoBase.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __LIB_FIFO_BASE_H__
#define __LIB_FIFO_BASE_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"
#include <string.h>
/* Global define ------------------------------------------------------------*/
#define LIB_FIFO_CALC_ARRAY_SIZE(Array)             (sizeof(Array) / sizeof((Array)[0]))
#define LIB_FIFO_MEMCPY_VERSION_MEMCPY              (0)
#define LIB_FIFO_MEMCPY_VERSION_LOOP_ALIGN_PTR      (1)
#define LIB_FIFO_MEMCPY_VERSION_ALIGN_PTR           (2)
#define LIB_FIFO_MEMCPY_VERSION_BYTE_TO_BYTE_COPY   (3)
#define LIB_FIFO_MEMCPY_VERSION_TARGET              LIB_FIFO_MEMCPY_VERSION_LOOP_ALIGN_PTR
/* Global typedef -----------------------------------------------------------*/
typedef struct
{
    void        *pmFifoStartAddr;   /* Pointer to Fifo start address */
    uint16_t    u16ElementSize;     /* Size of each Fifo element (e.g., type/struct/union) */
    // NOTE: Actual usable entries = u16FifoSize - 1 (1 slot reserved to detect full/empty)
    uint16_t    u16FifoSize;        /* Total number of elements (N entries) */
    uint16_t    u16FifoPushInPosi;  /* Fifo push in position */
    uint16_t    u16FifoPopOutPosi;  /* Fifo pop out position */
} tLibFifoBase;
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
bool LibFifoIsEmpty(tLibFifoBase *pmFifo);
bool LibFifoIsFull(tLibFifoBase *pmFifo);
uint16_t LibFifoGetCount(tLibFifoBase *pmFifo);
uint16_t LibFifoGetFreeCount(tLibFifoBase *pmFifo);
int8_t LibFifoGetUsage(tLibFifoBase *pmFifo, uint16_t *pu16Usage);
int8_t LibFifoPush(tLibFifoBase *pmFifo, void *pData);
int8_t LibFifoPop(tLibFifoBase *pmFifo, void *pData);
void * LibFifoGetFrontPointer(tLibFifoBase *pmFifo);
void * LibFifoGetBackPointer(tLibFifoBase *pmFifo);
void * LibFifoGetPushPointer(tLibFifoBase *pmFifo);
int8_t LibFifoPeekFront(tLibFifoBase *pmFifo, void *pData);
int8_t LibFifoPeekBack(tLibFifoBase *pmFifo, void *pData);
void LibFifoMemcpy(void *pSrcData, void *pDstData, uint16_t u16ElementSize);

#ifdef __cplusplus
}
#endif

#endif