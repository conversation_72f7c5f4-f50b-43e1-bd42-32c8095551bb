/*
******************************************************************************
* @file     ApiStackMemCalc.c
* <AUTHOR>
* @brief    This file is Stack memory size estimation.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "ApiStackMemCalc.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
/* Local variables ----------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
extern uint32_t __stack_top__;    // 由 linker script 定義於"device_linker.cmd"
extern uint32_t __stack_bottom__; // 由 linker script 定義於"device_linker.cmd"

static uint32_t gu32StackMonitorSkipCnt = 0;
/* Global function prototypes -----------------------------------------------*/

void ApiStackMemFillStack(void)
{
    uint32_t *pStack = (uint32_t *)&__stack_bottom__;
    while (pStack < (uint32_t *)&__stack_top__) {
        *pStack++ = API_STACK_MEM_FILL_PATTERN;
    }
}

uint32_t ApiStackMemGetUsedSize(void)
{
    uint32_t *pStack = (uint32_t *)&__stack_bottom__;
    while ((pStack < (uint32_t *)&__stack_top__) && (*pStack == API_STACK_MEM_FILL_PATTERN)) {
        pStack++;
    }

    return ((uint32_t)&__stack_top__ - (uint32_t)pStack);
}

uint32_t ApiStackMemGetFreeSize(void)
{
    return ((uint32_t)&__stack_top__ - (uint32_t)&__stack_bottom__) - ApiStackMemGetUsedSize();
}

tFunRetunCode ApiStackMemCalcExe(uint32_t* pu32StacUseSize)
{
    ++gu32StackMonitorSkipCnt;
    if(gu32StackMonitorSkipCnt > API_STACK_MEM_CALC_SKIP_CNT)
    {           
            *pu32StacUseSize = ApiStackMemGetUsedSize();
            gu32StackMonitorSkipCnt = 0; 
            return(RES_SUCCESS);
    }

    return(RES_ERROR_INVALID_STATE);   
}
