/*
******************************************************************************
* @file     HalRtc.h
* <AUTHOR>
* @brief    This file is the HAL common function of real-time clock

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_RTC_H__
#define	__HAL_RTC_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include <stdint.h>
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

/* Global define ------------------------------------------------------------*/
/* Global typedef -----------------------------------------------------------*/
typedef struct{
	uint16_t	u16Year;
	uint8_t		u8Month;
	uint8_t		u8Day;
	uint8_t		u8Hour;
	uint8_t		u8Minute;
	uint8_t		u8Second;
}tHalRtcDateTime;

typedef void (*tHalRtcCallbackHandler)(void);

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
tFunRetunCode HalRtcOpen(void);
tFunRetunCode HalRtcClose(void);
tFunRetunCode HalRtcSetupDate(uint16_t u16Year, uint8_t u8Month, uint8_t u8Day);
tFunRetunCode HalRtcSetupTime(uint8_t u8Hour, uint8_t u8Min, uint8_t u8Sec);
void HalRtcGetDateTime(tHalRtcDateTime *pmDateTime);
uint32_t HalRtcGetSelfUnixTime(void);
void HalRtcSelfUnixTimeToDateTime(uint32_t u8Sec, tHalRtcDateTime *pmRtcDateTime);
bool HalRtcIsRtcValid(void);

/* Add Call back function register*/
//------------------------------------------------------------------------------
void HalRtcRegisterPrescaler0Callback(tHalRtcCallbackHandler mCallBackfun);     //Execute once every 244us
void HalRtcRegisterPrescaler1Callback(tHalRtcCallbackHandler mCallBackfun);     //Execute once every 1000ms(1sec)
void HalRtcRegisterReadyCallback(tHalRtcCallbackHandler mCallBackfun);
uint32_t HalRtcGetPrescaler1Cnt(void);
void HalRtcSetPrescaler1Cnt(uint32_t u32SetCnt);
//------------------------------------------------------------------------------

#ifdef __cplusplus
}
#endif

#endif