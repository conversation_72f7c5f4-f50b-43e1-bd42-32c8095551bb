/*
******************************************************************************
* @file     ApiSpiFlash.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __API_SPI_FLASH_H__
#define __API_SPI_FLASH_H__

#ifdef __cplusplus
extern "C" {
#endif
/* Includes -----------------------------------------------------------------*/
#include "LibFunctionPointerRegister.h"
#include "LibHardwareTimerHandler.h"
#include "LibSoftwareTimerHandler.h"
#include "HalSpi.h"
#include "LibFifoBase.h"
#include "LibSpiFifo.h"
/* Global define ------------------------------------------------------------*/
//#define API_SPI_FLASH_ENABLE_EXAMPLE
#define API_SPI_FLASH_TEST_DISABLE_READ_REGISTER    (0)

#define API_SPI_FLASH_DEVICE_QTY                    (1)
#define API_SPI_FLASH_PAGE_SHIFT                    (8)
#define API_SPI_FLASH_SECTOR_SHIFT                  (12)
#define API_SPI_FLASH_BLOCK32K_SHIFT                (15)
#define API_SPI_FLASH_BLOCK64K_SHIFT                (16)
#define API_SPI_FLASH_PAGE_SIZE                     (1U << API_SPI_FLASH_PAGE_SHIFT)        // 256 Bytes
#define API_SPI_FLASH_SECTOR_SIZE                   (1U << API_SPI_FLASH_SECTOR_SHIFT)      // 4 KBytes (4096)
#define API_SPI_FLASH_BLOCK32K_SIZE                 (1U << API_SPI_FLASH_BLOCK32K_SHIFT)    // 32 KBytes (32768)
#define API_SPI_FLASH_BLOCK64K_SIZE                 (1U << API_SPI_FLASH_BLOCK64K_SHIFT)    // 64 KBytes (65536)
#define API_SPI_FLASH_GET_PAGE(Address)             ((Address) >> API_SPI_FLASH_PAGE_SHIFT)
#define API_SPI_FLASH_GET_SECTOR(Address)           ((Address) >> API_SPI_FLASH_SECTOR_SHIFT)
#define API_SPI_FLASH_GET_BLOCK32K(Address)         ((Address) >> API_SPI_FLASH_BLOCK32K_SHIFT)
#define API_SPI_FLASH_GET_BLOCK64K(Address)         ((Address) >> API_SPI_FLASH_BLOCK64K_SHIFT)
#define API_SPI_FLASH_ALIGN_TO_PAGE(Address)        ((Address) & ~(API_SPI_FLASH_PAGE_SIZE - 1))
#define API_SPI_FLASH_ALIGN_TO_SECTOR(Address)      ((Address) & ~(API_SPI_FLASH_SECTOR_SIZE - 1))
#define API_SPI_FLASH_ALIGN_TO_BLOCK32K(Address)    ((Address) & ~(API_SPI_FLASH_BLOCK32K_SIZE - 1))
#define API_SPI_FLASH_ALIGN_TO_BLOCK64K(Address)    ((Address) & ~(API_SPI_FLASH_BLOCK64K_SIZE - 1))

#define API_SPI_FLASH_PAGE_IN_SECTOR            (16)
#define API_SPI_FLASH_SECTOR_IN_BLOCK           (16)

#define API_SPI_FLASH_TX_SIZE                   (5)
#define API_SPI_FLASH_RX_SIZE                   (8)
#define API_SPI_FLASH_FIFO_SIZE                 (32)

#define API_SPI_FLASH_WINBOND_STATUS_REGISTER_SIZE  (3)

#define API_SPI_FLASH_FIFO_STATIC_POINTER       (0)
#define API_SPI_FLASH_FIFO_DYNAMIC_FIFO_BASE    (1)
#define API_SPI_FLASH_FIFO_TARGET               API_SPI_FLASH_FIFO_STATIC_POINTER
/* Global typedef -----------------------------------------------------------*/
typedef enum
{
    kAPI_SPI_FLASH_BLOCK_IN_CHIP_32M    = 64,
    kAPI_SPI_FLASH_BLOCK_IN_CHIP_64M    = 128,
    kAPI_SPI_FLASH_BLOCK_IN_CHIP_128M   = 256,
} eTypeApiSpiFlashBlockInChip;

typedef enum
{// Command                                           Value
    kAPI_SPI_FLASH_CMD_WRITE_ENABLE                 = 0x06,
    kAPI_SPI_FLASH_CMD_VOLATILE_SR_WRITE_ENABLE     = 0x50,
    kAPI_SPI_FLASH_CMD_WRITE_DISABLE                = 0x04,
    kAPI_SPI_FLASH_CMD_RELEASE_POWER_DOWN_ID        = 0xAB,
    kAPI_SPI_FLASH_CMD_MANUFACTURER_DEVICE_ID       = 0x90,
    kAPI_SPI_FLASH_CMD_JEDEC_ID                     = 0x9F,
    kAPI_SPI_FLASH_CMD_READ_UNIQUE_ID               = 0x4B,
    kAPI_SPI_FLASH_CMD_READ_DATA                    = 0x03,
    kAPI_SPI_FLASH_CMD_FAST_READ                    = 0x0B,
    kAPI_SPI_FLASH_CMD_PAGE_PROGRAM                 = 0x02,
    kAPI_SPI_FLASH_CMD_ERASE_SECTOR                 = 0x20,
    kAPI_SPI_FLASH_CMD_ERASE_BLOCK_32K              = 0x52,
    kAPI_SPI_FLASH_CMD_ERASE_BLOCK_64K              = 0xD8,
    kAPI_SPI_FLASH_CMD_ERASE_CHIP                   = 0xC7,
    kAPI_SPI_FLASH_CMD_ERASE_ALT_CHIP               = 0x60,
    kAPI_SPI_FLASH_CMD_READ_STATUS_REGISTER_1       = 0x05,
    kAPI_SPI_FLASH_CMD_WRITE_STATUS_REGISTER_1      = 0x01,
    kAPI_SPI_FLASH_CMD_READ_STATUS_REGISTER_2       = 0x35,
    kAPI_SPI_FLASH_CMD_WRITE_STATUS_REGISTER_2      = 0x31,
    kAPI_SPI_FLASH_CMD_READ_STATUS_REGISTER_3       = 0x15,
    kAPI_SPI_FLASH_CMD_WRITE_STATUS_REGISTER_3      = 0x11,
    kAPI_SPI_FLASH_CMD_READ_SFDP_REGISTER           = 0x5A,
    kAPI_SPI_FLASH_CMD_ERASE_SECURITY_REGISTER      = 0x44,
    kAPI_SPI_FLASH_CMD_PROGRAM_SECURITY_REGISTER    = 0x42,
    kAPI_SPI_FLASH_CMD_READ_SECURITY_REGISTER       = 0x48,
    kAPI_SPI_FLASH_CMD_GLOBAL_BLOCK_LOCK            = 0x7E,
    kAPI_SPI_FLASH_CMD_GLOBAL_BLOCK_UNLOCK          = 0x98,
    kAPI_SPI_FLASH_CMD_READ_BLOCK_LOCK              = 0x3D,
    kAPI_SPI_FLASH_CMD_INDIVIDUAL_BLOCK_LOCK        = 0x36,
    kAPI_SPI_FLASH_CMD_INDIVIDUAL_BLOCK_UNLOCK      = 0x39,
    kAPI_SPI_FLASH_CMD_ERASE_PROGRAM_SUSPEND        = 0x75,
    kAPI_SPI_FLASH_CMD_ERASE_PROGRAM_RESUME         = 0x7A,
    kAPI_SPI_FLASH_CMD_POWER_DOWN                   = 0xB9,
    kAPI_SPI_FLASH_CMD_ENABLE_RESET                 = 0x66,
    kAPI_SPI_FLASH_CMD_RESET_DEVICE                 = 0x99,
    kAPI_SPI_FLASH_CMD_NOP                          = 0x00,
    kAPI_SPI_FLASH_CMD_DUMMY                        = 0xFF,
} eTypeApiSpiFlashCmd;

typedef enum
{
    kAPI_SPI_FLASH_STATUS_REGISTER_NONE = 0,
    kAPI_SPI_FLASH_STATUS_REGISTER_1    = (1 << 0),
    kAPI_SPI_FLASH_STATUS_REGISTER_2    = (1 << 1),
    kAPI_SPI_FLASH_STATUS_REGISTER_3    = (1 << 2),
    kAPI_SPI_FLASH_STATUS_REGISTER_ALL  = 
    (
        kAPI_SPI_FLASH_STATUS_REGISTER_1 |
        kAPI_SPI_FLASH_STATUS_REGISTER_2 |
        kAPI_SPI_FLASH_STATUS_REGISTER_3
    ),
} eTypeApiSpiFlashStatusRegister;

typedef enum
{
    kAPI_SPI_STATUS_FLAG_BUSY   = (1 << 0),
    kAPI_SPI_STATUS_FLAG_WEL    = (1 << 1),
    kAPI_SPI_STATUS_FLAG_BP0    = (1 << 2),
    kAPI_SPI_STATUS_FLAG_BP1    = (1 << 3),
    kAPI_SPI_STATUS_FLAG_BP2    = (1 << 4),
    kAPI_SPI_STATUS_FLAG_TB     = (1 << 5),
    kAPI_SPI_STATUS_FLAG_SEC    = (1 << 6),
    kAPI_SPI_STATUS_FLAG_SRP    = (1 << 7),
    kAPI_SPI_STATUS_FLAG_SRL    = (1 << 8),
    kAPI_SPI_STATUS_FLAG_QE     = (1 << 9),
    kAPI_SPI_STATUS_FLAG_LB1    = (1 << 11),
    kAPI_SPI_STATUS_FLAG_LB2    = (1 << 12),
    kAPI_SPI_STATUS_FLAG_LB3    = (1 << 13),
    kAPI_SPI_STATUS_FLAG_CMP    = (1 << 14),
    kAPI_SPI_STATUS_FLAG_SUS    = (1 << 15),
    kAPI_SPI_STATUS_FLAG_WPS    = (1 << 18),
    kAPI_SPI_STATUS_FLAG_DRV0   = (1 << 21),
    kAPI_SPI_STATUS_FLAG_DRV1   = (1 << 22),
} eTypeApiSpiStatusFlag;

typedef union
{
    uint8_t u8WinbondSeries[API_SPI_FLASH_WINBOND_STATUS_REGISTER_SIZE];
    uint32_t u32WinbondSeries;
} tUnionStatusRegister;

typedef union
{
    struct
    {
        uint8_t CapacityId;
        uint8_t MemoryTypeId;
    };
    uint16_t u16JedecId;
} tUnionJedecId;

typedef union
{
    uint8_t u8UniqueId[8];
    uint64_t u64UniqueId;
} tUnionUniqueId;

typedef union
{
    uint8_t u8TxData[260];
    struct
    {
        uint8_t u8Command;
        uint8_t u8Address[3];
        uint8_t u8Data[256];
    };
} tUnionApiSpiFlashPacket;

typedef struct
{
    tHalSpi *pmHalSpi;
    uint8_t u8CsIndex;
//  private
    uint8_t u8TxData[API_SPI_FLASH_TX_SIZE], u8RxData[API_SPI_FLASH_RX_SIZE];
    tUnionStatusRegister uStatusRegister;
    uint8_t u8ManufacturerId;
    uint8_t u8DeviceId;
    tUnionJedecId uJedecId;
    tUnionUniqueId uUniqueId;
    tLibSpiFifo mSpiFifo;
    tLibSpiPacket mSpiPackets[API_SPI_FLASH_FIFO_SIZE];
#if API_SPI_FLASH_FIFO_TARGET == API_SPI_FLASH_FIFO_DYNAMIC_FIFO_BASE
    tLibFifoBase mSpiFlashTxFifo;
#endif
    tUnionApiSpiFlashPacket mSpiFlashTxPackets[API_SPI_FLASH_FIFO_SIZE];
} tApiSpiFlash;
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
int8_t ApiSpiFlashInit(tApiSpiFlash *pmApiSpiFlash);
uint16_t ApiSpiFlashFifoCommandSize(tApiSpiFlash *pmApiSpiFlash);
bool ApiSpiFlashIsBusy(tApiSpiFlash *pmApiSpiFlash);
bool ApiSpiFlashIsWriteEnable(tApiSpiFlash *pmApiSpiFlash);
// Blocking function
int8_t ApiSpiFlashReadManufacturerDeviceId(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashReadJedecId(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashReadUniqueId(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashReadStatusRegister(tApiSpiFlash *pmApiSpiFlash, eTypeApiSpiFlashStatusRegister eApiSpiFlashStatusRegister);
int8_t ApiSpiFlashWriteVolatileStatusRegisterEnable(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashWriteStatusRegister(tApiSpiFlash *pmApiSpiFlash, eTypeApiSpiFlashStatusRegister eApiSpiFlashStatusRegister, uint8_t u8Data);
int8_t ApiSpiFlashWriteEnable(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashWriteDisable(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashPowerDown(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashReleasePowerDown(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashReset(tApiSpiFlash *pmApiSpiFlash);
int8_t ApiSpiFlashBlockingReadDataByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, uint8_t *pu8Data, uint16_t u16Size);
int8_t ApiSpiFlashBlockingReadDataByPage(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size);
int8_t ApiSpiFlashBlockingFastReadDataByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, uint8_t *pu8Data, uint16_t u16Size);
int8_t ApiSpiFlashBlockingFastReadDataByPage(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size);
// Dma function
int8_t ApiSpiFlashDmaReadDataByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashDmaReadDataByPage(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashDmaFastReadDataByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashDmaFastReadDataByPage(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashPageProgram(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashSectorEraseByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashSectorEraseBySector(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Sector, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashBlockErase32kByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashBlockErase32kByBlock(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Block32k, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashBlockErase64kByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashBlockErase64kByBlock(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Block64k, tfpHalSpiEvent fpCallback);
int8_t ApiSpiFlashChipErase(tApiSpiFlash *pmApiSpiFlash, tfpHalSpiEvent fpCallback);
/* Global test function prototypes ------------------------------------------*/
#ifdef API_SPI_FLASH_ENABLE_EXAMPLE
void ApiSpiFlashExampleInit(void);
void ApiSpiFlashExampleTest(void);
#endif
#ifdef __cplusplus
}
#endif
#endif