/*
******************************************************************************
* @file     AppSerialUartHvBBU.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "Define.h"
#include "Main.h"
#include "LibSoftwareTimerHandler.h"
#include "HalUart.h"
#include "Bsp.h" 
#include "LibUartFifo.h"
#include "LibFunctionReturnValueDefine.h"
#include "SmpCanBusProtocol.h"
#include "SmpParameterId.h"
#include "LibSysPar.h"
#include "HalAfe.h"
#include "ApiProtectOvp.h"
#include "ApiProtectUvp.h"
#include "ApiProtectCotp.h"
#include "ApiProtectCutp.h"
#include "ApiProtectDotp.h"
#include "ApiProtectDutp.h"
#include "ApiSystemFlag.h"

/* Private define ------------------------------------------------------------*/
#define	UART_TX_BUF_SIZE	(300)
#define	UART_RX_BUF_SIZE	(500)

#define	MAKE_SMP_CAN_ID(fun, bmuid, obj, sub)	(((uint32_t)fun<<25)|((uint32_t)bmuid<<18)|((uint32_t)obj<<10)|(uint32_t)sub)
#define	SMP_CAN_GET_FUN(id)			((id >> 25) & 0x0f)
#define	SMP_CAN_GET_SCU_ID(id)		((id >> 18) & 0x7f)
#define	SMP_CAN_GET_OBJ_INDEX(id)	((id >> 10) & 0xff)
#define	SMP_CAN_GET_SUB_INDEX(id)	(id & 0x3ff)
#define	GET_U16_VALUE(addr)		((((uint16_t)(*(uint8_t *)(addr+1)))<<8) + (((uint16_t)(*(uint8_t *)(addr+0)))))
#define	GET_U32_VALUE(addr)		((((uint32_t)(*(uint8_t *)(addr+3)))<<24) + (((uint32_t)(*(uint8_t *)(addr+2)))<<16) + (((uint32_t)(*(uint8_t *)(addr+1)))<<8) + (((uint32_t)(*(uint8_t *)(addr+0)))))

#define	APP_UART_GET_CELL_NUM()		    LibSysParGetCellNumber()
#define	APP_UART_GET_NTC_NUM()		    LibSysParGetNtcNumber()
#define APP_UART_GET_CELL_VOL(u16Cells)	HalAfeGetCellVoltage(u16Cells)
#define	APP_UART_GET_OVP_FLAG(u16Cells)	ApiProtectOvpGetFlag(u16Cells)
#define	APP_UART_GET_UVP_FLAG(u16Cells)	ApiProtectUvpGetFlag(u16Cells)
#define APP_UART_GET_COTP_FLAG(u16Ntcs) ApiProtectCotpGetFlag(u16Ntcs)
#define APP_UART_GET_CUTP_FLAG(u16Ntcs) ApiProtectCutpGetFlag(u16Ntcs)
#define APP_UART_GET_DOTP_FLAG(u16Ntcs) ApiProtectDotpGetFlag(u16Ntcs)
#define APP_UART_GET_DUTP_FLAG(u16Ntcs) ApiProtectDutpGetFlag(u16Ntcs)


#define	APP_UART_GET_NTC_VOL(u16Ntcs)		HalAfeGetNtcVoltage(u16Ntcs)
#define	APP_UART_IS_CELL_BALANCE(u16Cells)	(0)	//AppBalanceIsBalanceSet(u16Cells)
#define APP_UART_GET_SYSTEM_FLAG1()     ApiSystemFlagGetFlag1()
#define APP_UART_GET_SYSTEM_FLAG2()     ApiSystemFlagGetFlag2()
#define APP_UART_GET_SYSTEM_FLAG3()     ApiSystemFlagGetFlag3()
#define APP_UART_GET_SYSTEM_FLAG4()     ApiSystemFlagGetFlag4()

#define	MAX_CAN_PKG_NUM_PER_TIME		(8)

#define	MAX_UART_DECODE_BUF_SIZE	    (250)
#define UART_DECODE_IDLE_MS             (50)
/* Private typedef -----------------------------------------------------------*/

typedef struct{
	union{
		uint8_t		u8Buf[4];
		uint32_t	u32Value;
	}mCanId;
	uint8_t	u8Dlc;
	uint8_t	u8Data[8];
}tCanPkg;
typedef struct{
	uint8_t	u8Status;
	union{
		uint8_t		u8Buf[2];
		uint16_t	u16Len;
	}mLen;
	uint8_t u8IdleTime;
	uint8_t	u8Index;
	uint8_t	u8Checksum;
	uint8_t	u8Buf[MAX_UART_DECODE_BUF_SIZE+4];
}tUartDecode;	

typedef uint8_t (* tfpNotificationRunTable)(void);

/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint8_t gu8Uart0TxFifoTest[UART_TX_BUF_SIZE];
static uint8_t gu8Uart0RxFifoTest[UART_RX_BUF_SIZE];
static tHalUartConfig gmHalUart0Config;
static uint8_t gu8NotiFunTableIndex = 0;	
static uint16_t gu16NotificationSubIndex = 0;
static uint8_t gu8NotificationDetailEnableFlag = 1;
static tUartDecode	mUartDecode;

/* Private function prototypes -----------------------------------------------*/
void AppSerialUartSendMessage(uint8_t *pu8Str);
static void notifyNextFunction(void);
static uint8_t notifyFunNone(void);
static uint8_t appSerialCanDavinciNotificationEnd(void);
static void AppSerialUartNotiBaseSystemFlag(void);
static void AppSerialUartNotiBaseCurrent(void);
static void AppSerialUartNotiMinMaxValue(void);
static uint8_t notifyBaseInfoPackage(void);
static uint8_t AppSerialUartNotificationCellVoltage(void);
static uint8_t AppSerialUartNotificationNtcVoltage(void);
static uint8_t AppSerialUartNotificationOvpFlag(void);
static uint8_t AppSerialUartNotificationUvpFlag(void);
static uint8_t AppSerialUartNotificationCotpFlag(void);
static uint8_t AppSerialUartNotificationCutpFlag(void);
static uint8_t AppSerialUartNotificationDotpFlag(void);
static uint8_t AppSerialUartNotificationDutpFlag(void);


const tfpNotificationRunTable	gfpNotificationFunctionTable[]={
	notifyFunNone,
	notifyBaseInfoPackage,
	AppSerialUartNotificationCellVoltage,
	AppSerialUartNotificationNtcVoltage,
	AppSerialUartNotificationOvpFlag,
	AppSerialUartNotificationUvpFlag,
    AppSerialUartNotificationCotpFlag,
    AppSerialUartNotificationCutpFlag,
    AppSerialUartNotificationDotpFlag,
    AppSerialUartNotificationDutpFlag,
    
	appSerialCanDavinciNotificationEnd
	
};

static void notifyNextFunction(void)
{
	gu16NotificationSubIndex = 0;
	gu8NotiFunTableIndex++;
}

static uint8_t notifyFunNone(void)
{
	notifyNextFunction();
	
	return 0;
}
static uint8_t notifyBaseInfoPackage(void)
{
	AppSerialUartNotiBaseSystemFlag();
	AppSerialUartNotiBaseCurrent();
	AppSerialUartNotiMinMaxValue();
	
	notifyNextFunction();	
	return 0;	
}

static uint8_t appSerialCanDavinciNotificationEnd(void)
{
	gu8NotiFunTableIndex = 0;
	gu16NotificationSubIndex = 0;
	return 0;
}

static void AppSerialUartPkgCanbusMsg(tCanPkg *pCanPkg, uint8_t u8PkgLen)
{
	uint8_t		u8Buffer[256];
	uint8_t		u8Index,u8Chekcsum,u8Len;
	uint8_t		i,u8Pkg;
	
	u8Buffer[0] = 0x5A;
	u8Buffer[1] = 0;
	u8Buffer[2] = 0; 
	u8Buffer[3] = 'C';
	u8Index = 4;
	u8Chekcsum = 'C';
	for(u8Pkg=0; u8Pkg<u8PkgLen; u8Pkg++)
	{
		u8Buffer[u8Index++] = pCanPkg->mCanId.u8Buf[0];
		u8Chekcsum ^= pCanPkg->mCanId.u8Buf[0];

		u8Buffer[u8Index++] = pCanPkg->mCanId.u8Buf[1];
		u8Chekcsum ^= pCanPkg->mCanId.u8Buf[1];

		u8Buffer[u8Index++] = pCanPkg->mCanId.u8Buf[2];
		u8Chekcsum ^= pCanPkg->mCanId.u8Buf[2];

		u8Buffer[u8Index++] = pCanPkg->mCanId.u8Buf[3];
		u8Chekcsum ^= pCanPkg->mCanId.u8Buf[3];

		u8Buffer[u8Index++] = pCanPkg->u8Dlc;
		u8Chekcsum ^= pCanPkg->u8Dlc;
		
		for(i=0; i<pCanPkg->u8Dlc; i++)
		{
			u8Buffer[u8Index++] = pCanPkg->u8Data[i];
			u8Chekcsum ^= pCanPkg->u8Data[i];
		}
		pCanPkg++;
	}
	u8Buffer[1] = u8Index;
	u8Chekcsum ^= u8Index;
	u8Buffer[u8Index++] = u8Chekcsum;
	u8Buffer[u8Index++] = 0x69;
	
	for(i=0; i<u8Index; i++)
		HalUartPut(&gmHalUart0Config, u8Buffer[i]);
}


static void AppSerialUartNotiBaseSystemFlag(void)
{
	tCanPkg	mCanPkg[2];
	tLbyte	Lbyte;
	
	mCanPkg[0].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, 1,
									SMP_BASE_SYSTEM_FLAG_OBJ_INDEX,
									0);
	mCanPkg[0].u8Dlc = 8;
	
	Lbyte.u32Value = APP_UART_GET_SYSTEM_FLAG1();
	mCanPkg[0].u8Data[0] = Lbyte.u8Buf[0];
	mCanPkg[0].u8Data[1] = Lbyte.u8Buf[1];
	mCanPkg[0].u8Data[2] = Lbyte.u8Buf[2];
	mCanPkg[0].u8Data[3] = Lbyte.u8Buf[3];

	Lbyte.u32Value = APP_UART_GET_SYSTEM_FLAG2();
	mCanPkg[0].u8Data[4] = Lbyte.u8Buf[0];
	mCanPkg[0].u8Data[5] = Lbyte.u8Buf[1];
	mCanPkg[0].u8Data[6] = Lbyte.u8Buf[2];
	mCanPkg[0].u8Data[7] = Lbyte.u8Buf[3];
	       
	mCanPkg[1].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, 1,
									SMP_BASE_SYSTEM_FLAG_OBJ_INDEX,
									1);
	mCanPkg[1].u8Dlc = 8;
	
	Lbyte.u32Value = APP_UART_GET_SYSTEM_FLAG3();
	mCanPkg[1].u8Data[0] = Lbyte.u8Buf[0];
	mCanPkg[1].u8Data[1] = Lbyte.u8Buf[1];
	mCanPkg[1].u8Data[2] = Lbyte.u8Buf[2];
	mCanPkg[1].u8Data[3] = Lbyte.u8Buf[3];

	Lbyte.u32Value = APP_UART_GET_SYSTEM_FLAG4();
	mCanPkg[1].u8Data[4] = Lbyte.u8Buf[0];
	mCanPkg[1].u8Data[5] = Lbyte.u8Buf[1];
	mCanPkg[1].u8Data[6] = Lbyte.u8Buf[2];
	mCanPkg[1].u8Data[7] = Lbyte.u8Buf[3];
	
	AppSerialUartPkgCanbusMsg(&mCanPkg[0], 2);
}

static void AppSerialUartNotiBaseCurrent(void)
{
   	tCanPkg	mCanPkg;
	tLbyte	mCurr;
	
	mCanPkg.mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, 1,
									SMP_BASE_CURRENT_OBJ_INDEX,
									0);
	mCanPkg.u8Dlc = 8;
								
	mCurr.u32Value = HalAfeGetCurrentValue(0);
	mCanPkg.u8Data[0] = mCurr.u8Buf[0];
	mCanPkg.u8Data[1] = mCurr.u8Buf[1];
	mCanPkg.u8Data[2] = mCurr.u8Buf[2];
	mCanPkg.u8Data[3] = mCurr.u8Buf[3];
	
	mCurr.u32Value = HalAfeGetCurrentValue(1);
	mCanPkg.u8Data[4] = mCurr.u8Buf[0];
	mCanPkg.u8Data[5] = mCurr.u8Buf[1];
	mCanPkg.u8Data[6] = mCurr.u8Buf[2];
	mCanPkg.u8Data[7] = mCurr.u8Buf[3];

    AppSerialUartPkgCanbusMsg(&mCanPkg, 1);
}
static void AppSerialUartNotiMinMaxValue(void)
{
    tCanPkg	mCanPkg;
    tUnionU16Bits   mU16Bits;
	uint8_t		    u8Bmu,u8Ch;

	mCanPkg.mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, 1,
									SMP_BASE_MIN_MAX_VALUE_OBJ_INDEX,
									0);
	mCanPkg.u8Dlc = 8;
											
	mU16Bits.u16 = HalAfeGetMinCellVoltage(&u8Bmu, &u8Ch, 0);
	mCanPkg.u8Data[0] = mU16Bits.u8[0];
	mCanPkg.u8Data[1] = mU16Bits.u8[1];
	mCanPkg.u8Data[4] = u8Bmu;
	mCanPkg.u8Data[5] = u8Ch;

	mU16Bits.u16 = HalAfeGetMaxCellVoltage(&u8Bmu, &u8Ch, 0);
	mCanPkg.u8Data[2] = mU16Bits.u8[0];
	mCanPkg.u8Data[3] = mU16Bits.u8[1];
	mCanPkg.u8Data[6] = u8Bmu;
	mCanPkg.u8Data[7] = u8Ch;
	AppSerialUartPkgCanbusMsg(&mCanPkg, 1);
	//----------------------------------------
	//	MinT & Max T
	mCanPkg.mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, 1,
									SMP_BASE_MIN_MAX_VALUE_OBJ_INDEX,
									1);
	mCanPkg.u8Dlc = 8;
		
	mU16Bits.u16 = HalAfeGetMinNtcTemp(&u8Bmu, &u8Ch);
	mCanPkg.u8Data[0] = mU16Bits.u8[0];
	mCanPkg.u8Data[1] = mU16Bits.u8[1];
	mCanPkg.u8Data[4] = u8Bmu;
	mCanPkg.u8Data[5] = u8Ch;
	
	mU16Bits.u16 = HalAfeGetMaxNtcTemp(&u8Bmu, &u8Ch);
	mCanPkg.u8Data[2] = mU16Bits.u8[0];
	mCanPkg.u8Data[3] = mU16Bits.u8[1];
	mCanPkg.u8Data[6] = u8Bmu;
	mCanPkg.u8Data[7] = u8Ch;
	AppSerialUartPkgCanbusMsg(&mCanPkg, 1);
}



static uint8_t AppSerialUartNotificationCellVoltage(void)
{
	tIbyte		mIbyte;
	tIbyte		mDiffV;
	tIbyte		mBaseV;
	tIbyte		mCellV;
	uint8_t		u8DatIndex;
	uint8_t 	u8ValidCnt;
	int8_t		i8ArrayDiffV[7] = {0};
	bool 		bMbmsBalanceFlag = false;
	tCanPkg		mCanPkg[MAX_CAN_PKG_NUM_PER_TIME];
	uint8_t		u8CanPkgIndex = 0;
	
	for(u8CanPkgIndex=0; u8CanPkgIndex<MAX_CAN_PKG_NUM_PER_TIME; u8CanPkgIndex++)
	{
		if (gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
		{
			if(u8CanPkgIndex == 0)
			{
				notifyNextFunction();
				return 0;
			}
			break;
		}
	
		//if(LibSysParGetSystemActiveFlag() & SYS_ACTIVE_FLAG_CELL_BALANCE_BY_MBMS)
		{
			//MbmsBalanceFlag = true;
		}
		mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_CELL_VOLTAGE_COMPRESS_OBJ_INDEX,
									gu16NotificationSubIndex);
		memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
	
		mBaseV.u16Value = APP_UART_GET_CELL_VOL(gu16NotificationSubIndex);
		if(bMbmsBalanceFlag)
		{
			if(APP_UART_IS_CELL_BALANCE(gu16NotificationSubIndex))
			{
				mBaseV.u16Value |= 1;
			}
			else
			{
				mBaseV.u16Value &= ~1;
			}
		}
		u8ValidCnt = 0;
		for (uint8_t i = 1; i < 7; i++)
		{		
			if(gu16NotificationSubIndex + i >= APP_UART_GET_CELL_NUM())
			{
				break;
			}
		
			mCellV.u16Value = APP_UART_GET_CELL_VOL(gu16NotificationSubIndex + i);
		
			if(bMbmsBalanceFlag)
			{
				if(APP_UART_IS_CELL_BALANCE(gu16NotificationSubIndex + i))
				{
					mCellV.u16Value |= 1;
				}
				else
				{
					mCellV.u16Value &= ~1;
				}		
			}
		
			mDiffV.i16Value = mCellV.i16Value - mBaseV.i16Value;
			if(abs(mDiffV.i16Value) > 127)
			{
				break;
			}
			else
			{
				i8ArrayDiffV[u8ValidCnt] = (int8_t)mDiffV.i16Value;
				u8ValidCnt++;
			}
		}
	
		if(u8ValidCnt > 2)
		{
			mCanPkg[u8CanPkgIndex].u8Data[0] = mBaseV.u8Buf[0];
			mCanPkg[u8CanPkgIndex].u8Data[1] = mBaseV.u8Buf[1];
			memcpy(&mCanPkg[u8CanPkgIndex].u8Data[2], i8ArrayDiffV, u8ValidCnt + 1);
			gu16NotificationSubIndex += (u8ValidCnt + 1);
			mCanPkg[u8CanPkgIndex].u8Dlc = u8ValidCnt + 2;
			continue;
		}
		mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_CELL_VOLTAGE_OBJ_INDEX,
									gu16NotificationSubIndex);
		memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
		u8DatIndex = 0;
		while (u8DatIndex < 8)
		{
			if(gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
				break;
		
			mIbyte.u16Value = APP_UART_GET_CELL_VOL(gu16NotificationSubIndex);
			if(bMbmsBalanceFlag)
			{
				if(APP_UART_IS_CELL_BALANCE(gu16NotificationSubIndex))
				{
					mIbyte.u16Value |= 1;
				}
				else
				{
					mIbyte.u16Value &= ~1;
				}		
			}		
			gu16NotificationSubIndex++;
		
			mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex++] = mIbyte.u8Buf[0];
			mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex++] = mIbyte.u8Buf[1];
		}
		mCanPkg[u8CanPkgIndex].u8Dlc = u8DatIndex;
	}
	AppSerialUartPkgCanbusMsg(&mCanPkg[0], u8CanPkgIndex);
	if (gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
		notifyNextFunction();
	return 1;
}

static uint8_t AppSerialUartNotificationNtcVoltage(void)
{
	tIbyte		mIbyte;
	tIbyte		mBaseV;
	tIbyte		mDiffV;
	uint8_t		u8DatIndex;
	uint8_t 	u8ValidCnt;
	int8_t		i8ArrayDiffV[7] = {0};
	tCanPkg		mCanPkg[MAX_CAN_PKG_NUM_PER_TIME];
	uint8_t		u8CanPkgIndex = 0;

	for(u8CanPkgIndex=0; u8CanPkgIndex<MAX_CAN_PKG_NUM_PER_TIME; u8CanPkgIndex++)
	{
		if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
		{
			if(u8CanPkgIndex == 0)
			{
				notifyNextFunction();
				return 0;	
			}
			break;
		}
		
		mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_NTC_VOLTAGE_COMPRESS_OBJ_INDEX,
									gu16NotificationSubIndex);
		memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
	
		mBaseV.u16Value = APP_UART_GET_NTC_VOL(gu16NotificationSubIndex);
		u8ValidCnt = 0;
		for(uint8_t i = 1; i < 7; i++)
		{
			if((gu16NotificationSubIndex + i) >= APP_UART_GET_NTC_NUM())
			{
				break;
			}
		
			mDiffV.i16Value = APP_UART_GET_NTC_VOL(gu16NotificationSubIndex + i) - mBaseV.i16Value;
			if( abs(mDiffV.i16Value) > 127)
			{
				break;
			}
			else
			{
				i8ArrayDiffV[u8ValidCnt] = (int8_t)mDiffV.i16Value;
				u8ValidCnt++;
			}
		}
	
		if(u8ValidCnt > 2)
		{
			mCanPkg[u8CanPkgIndex].u8Data[0] = mBaseV.u8Buf[0];
			mCanPkg[u8CanPkgIndex].u8Data[1] = mBaseV.u8Buf[1];
			memcpy(&mCanPkg[u8CanPkgIndex].u8Data[2], i8ArrayDiffV, u8ValidCnt + 1);
			gu16NotificationSubIndex += (u8ValidCnt + 1);
			mCanPkg[u8CanPkgIndex].u8Dlc = u8ValidCnt + 2;
			continue;
		}
	
		mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_CELL_NTC_VOLTAGE_OBJ_INDEX,
									gu16NotificationSubIndex);
		memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);

		u8DatIndex = 0;
		while(u8DatIndex < 8)
		{
			if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
				break;
		
			mIbyte.u16Value = APP_UART_GET_NTC_VOL(gu16NotificationSubIndex++);		
		
			mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex++] = mIbyte.u8Buf[0];
			mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex++] = mIbyte.u8Buf[1];
		}

		mCanPkg[u8CanPkgIndex].u8Dlc = u8DatIndex;
	}
	AppSerialUartPkgCanbusMsg(&mCanPkg[0], u8CanPkgIndex);
	if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
		notifyNextFunction();
	return 1;
}

static uint8_t AppSerialUartNotificationOvpFlag(void)
{
	uint8_t	u8DatIndex;
	tCanPkg	mCanPkg[MAX_CAN_PKG_NUM_PER_TIME];	
	uint8_t	u8CanPkgIndex = 0;

	if (gu8NotificationDetailEnableFlag == 0) 
	{
		notifyNextFunction();
		return 0;
	}

	for(u8CanPkgIndex=0; u8CanPkgIndex<MAX_CAN_PKG_NUM_PER_TIME; u8CanPkgIndex++)
	{		
		if (gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
		{
			if(u8CanPkgIndex == 0)
			{
				notifyNextFunction();
				return 0;
			}
			break;
		}
	
		mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_OVP_FLAG_OBJ_INDEX,
									gu16NotificationSubIndex);
		memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
		u8DatIndex = 0;
		while (u8DatIndex < 8)
		{
			if (gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
				break;
		
			mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex] = APP_UART_GET_OVP_FLAG(gu16NotificationSubIndex);
		
			if (APP_UART_IS_CELL_BALANCE(gu16NotificationSubIndex))
				mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex] |= 0x80;
		
			gu16NotificationSubIndex++;
			u8DatIndex++;
		}
		
		mCanPkg[u8CanPkgIndex].u8Dlc = 8;
	}
	AppSerialUartPkgCanbusMsg(&mCanPkg[0], u8CanPkgIndex);
	if (gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
		notifyNextFunction();
	return 1;
}

static uint8_t AppSerialUartNotificationUvpFlag(void)
{
	uint8_t	u8DatIndex;
	tCanPkg	mCanPkg[MAX_CAN_PKG_NUM_PER_TIME];	
	uint8_t	u8CanPkgIndex = 0;

	if (gu8NotificationDetailEnableFlag == 0) 
	{
		notifyNextFunction();
		return 0;
	}

	for(u8CanPkgIndex=0; u8CanPkgIndex<MAX_CAN_PKG_NUM_PER_TIME; u8CanPkgIndex++)
	{		
		if (gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
		{
			if(u8CanPkgIndex == 0)
			{
				notifyNextFunction();
				return 0;
			}
			break;
		}
	
		mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_UVP_FLAG_OBJ_INDEX,
									gu16NotificationSubIndex);
		memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
		u8DatIndex = 0;
		while (u8DatIndex < 8)
		{
			if (gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
				break;
		
			mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex] = APP_UART_GET_UVP_FLAG(gu16NotificationSubIndex);
		
			gu16NotificationSubIndex++;
			u8DatIndex++;
		}
		
		mCanPkg[u8CanPkgIndex].u8Dlc = 8;
	}
	AppSerialUartPkgCanbusMsg(&mCanPkg[0], u8CanPkgIndex);
	if (gu16NotificationSubIndex >= APP_UART_GET_CELL_NUM())
		notifyNextFunction();
	return 1;
}

static uint8_t AppSerialUartNotificationCotpFlag(void)
{
	uint8_t	u8DatIndex = 0;
	tCanPkg	mCanPkg[MAX_CAN_PKG_NUM_PER_TIME];	
	uint8_t	u8CanPkgIndex = 0;
	
	if (gu8NotificationDetailEnableFlag == 0)
	{
		notifyNextFunction();
		return 0;
	}
	for(u8CanPkgIndex=0; u8CanPkgIndex<MAX_CAN_PKG_NUM_PER_TIME; u8CanPkgIndex++)
	{
	    if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
	    {
	        if(u8CanPkgIndex == 0)
			{
				notifyNextFunction();
				return 0;
			}
			break;
    	}
	    mCanPkg[u8CanPkgIndex].mCanId.u32Value =  MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_COTP_FLAG_OBJ_INDEX,
									gu16NotificationSubIndex);
	    memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
	
    	while (u8DatIndex < 8)
	    {
		    if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
			    break;
		
		    mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex++] = APP_UART_GET_COTP_FLAG(gu16NotificationSubIndex++);
	    }
	
	    mCanPkg[u8CanPkgIndex].u8Dlc = 8;
    }
    AppSerialUartPkgCanbusMsg(&mCanPkg[0], u8CanPkgIndex);
	if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
		notifyNextFunction();
	return 1;
}

static uint8_t AppSerialUartNotificationCutpFlag(void)
{
	uint8_t		u8DatIndex = 0;
	tCanPkg	mCanPkg[MAX_CAN_PKG_NUM_PER_TIME];	
	uint8_t	u8CanPkgIndex = 0;
	if (gu8NotificationDetailEnableFlag == 0)
	{
		notifyNextFunction();
		return 0;
	}
	for(u8CanPkgIndex=0; u8CanPkgIndex<MAX_CAN_PKG_NUM_PER_TIME; u8CanPkgIndex++)
	{
	    if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
	    {
	        if(u8CanPkgIndex == 0)
			{
				notifyNextFunction();
				return 0;
			}
			break;
    	}
	    mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_CUTP_FLAG_OBJ_INDEX,
									gu16NotificationSubIndex);
	    memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
	
	    while (u8DatIndex < 8)
	    {
		    if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
			    break;
		    mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex++] = APP_UART_GET_CUTP_FLAG(gu16NotificationSubIndex++);
	    }
	    mCanPkg[u8CanPkgIndex].u8Dlc = 8;
	}
    AppSerialUartPkgCanbusMsg(&mCanPkg[0], u8CanPkgIndex);
	if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
		notifyNextFunction();
	return 1;
}


static uint8_t AppSerialUartNotificationDotpFlag(void)
{
	uint8_t		u8DatIndex = 0;
	tCanPkg	mCanPkg[MAX_CAN_PKG_NUM_PER_TIME];	
	uint8_t	u8CanPkgIndex = 0;
	
	if (gu8NotificationDetailEnableFlag == 0)
	{
		notifyNextFunction();
		return 0;
	}
	for(u8CanPkgIndex=0; u8CanPkgIndex<MAX_CAN_PKG_NUM_PER_TIME; u8CanPkgIndex++)
	{
	    if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
	    {
	        if(u8CanPkgIndex == 0)
			{
				notifyNextFunction();
				return 0;
			}
			break;
    	}
    	mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_DOTP_FLAG_OBJ_INDEX,
									gu16NotificationSubIndex);
        memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
	
    	while (u8DatIndex < 8)
	    {		
		    if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
			    break;
		
		    mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex++] = APP_UART_GET_DOTP_FLAG(gu16NotificationSubIndex++);
	    }
		mCanPkg[u8CanPkgIndex].u8Dlc = 8;
	}
    AppSerialUartPkgCanbusMsg(&mCanPkg[0], u8CanPkgIndex);
	if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
		notifyNextFunction();
	return 1;
}	

static uint8_t AppSerialUartNotificationDutpFlag(void)
{
	uint8_t		u8DatIndex = 0;
    tCanPkg	mCanPkg[MAX_CAN_PKG_NUM_PER_TIME];	
	uint8_t	u8CanPkgIndex = 0;
	
	if (gu8NotificationDetailEnableFlag == 0)
	{
		notifyNextFunction();
		return 0;
	}
	for(u8CanPkgIndex=0; u8CanPkgIndex<MAX_CAN_PKG_NUM_PER_TIME; u8CanPkgIndex++)
	{
	    if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
	    {
	        if(u8CanPkgIndex == 0)
			{
				notifyNextFunction();
				return 0;
			}
			break;
    	}
    	mCanPkg[u8CanPkgIndex].mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 1,
									SMP_DETAIL_DUTP_FLAG_OBJ_INDEX,
									gu16NotificationSubIndex);
        memset(&mCanPkg[u8CanPkgIndex].u8Data, 0, 8);
	
	    while (u8DatIndex < 8)
	    {
		    if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
			    break;
		
		    mCanPkg[u8CanPkgIndex].u8Data[u8DatIndex++] = APP_UART_GET_DUTP_FLAG(gu16NotificationSubIndex++);
	    }
	    mCanPkg[u8CanPkgIndex].u8Dlc = 8;
	}
    AppSerialUartPkgCanbusMsg(&mCanPkg[0], u8CanPkgIndex);
	if (gu16NotificationSubIndex >= APP_UART_GET_NTC_NUM())
		notifyNextFunction();
	return 1;
}


static void AppSerialUartSystemParResponse(tCanPkg *pmCanPkg)
{
   	tCanPkg	mCanPkg;
    uint16_t	u16SubIndex;
    tLbyte       mLbyte;

   	u16SubIndex = SMP_CAN_GET_SUB_INDEX(pmCanPkg->mCanId.u32Value);
    mCanPkg.mCanId.u32Value = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, 1,
									SMP_CMD_PAR_RD_OBJ_INDEX,
									u16SubIndex);								
    switch(u16SubIndex) 
    {
    case SMP_PAR_ID_AFE_NUMBER:
        mCanPkg.u8Dlc = 2;
		mCanPkg.u8Data[0] = LibSysParGetTotalAfeNumber();
		mCanPkg.u8Data[1] = LibSysParGetTotalAfeNumber();
		break;
	case SMP_PAR_ID_CELL_NTC_FLAG:
	    mCanPkg.u8Data[0] = pmCanPkg->u8Data[0];
		mCanPkg.u8Data[1] = pmCanPkg->u8Data[1];									
		if(pmCanPkg->u8Data[0] == 0x80) //Line Lose
		{	
		    ;
		}
		else if(pmCanPkg->u8Data[0] == 0x90)
		{
			mCanPkg.u8Dlc = 6;
			mLbyte.u32Value = LibSysParGetCellBusBarFlag(pmCanPkg->u8Data[1]);
			mCanPkg.u8Data[2] = mLbyte.u8Buf[0];
			mCanPkg.u8Data[3] = mLbyte.u8Buf[1];
			mCanPkg.u8Data[4] = mLbyte.u8Buf[2];
			mCanPkg.u8Data[5] = mLbyte.u8Buf[3];
		}
		else if(pmCanPkg->u8Data[0] == 0x91)
		{
			mCanPkg.u8Dlc = 6;
			mLbyte.u32Value = LibSysParGetNtcAmbientFlag(pmCanPkg->u8Data[1]);
			mCanPkg.u8Data[2] = mLbyte.u8Buf[0];
			mCanPkg.u8Data[3] = mLbyte.u8Buf[1];
			mCanPkg.u8Data[4] = mLbyte.u8Buf[2];
			mCanPkg.u8Data[5] = mLbyte.u8Buf[3];
		}
		else if(pmCanPkg->u8Data[0] == 0x92)
		{
			mCanPkg.u8Dlc = 6;
			mLbyte.u32Value = LibSysParGetNtcBusBarFlag(pmCanPkg->u8Data[1]);
			mCanPkg.u8Data[2] = mLbyte.u8Buf[0];
			mCanPkg.u8Data[3] = mLbyte.u8Buf[1];
			mCanPkg.u8Data[4] = mLbyte.u8Buf[2];
			mCanPkg.u8Data[5] = mLbyte.u8Buf[3];
		}
		else if(pmCanPkg->u8Data[0] == 0x93)
		{
			mCanPkg.u8Dlc = 6;
			mLbyte.u32Value = LibSysParGetNtcOtherFlag(pmCanPkg->u8Data[1]);
			mCanPkg.u8Data[2] = mLbyte.u8Buf[0];
			mCanPkg.u8Data[3] = mLbyte.u8Buf[1];
			mCanPkg.u8Data[4] = mLbyte.u8Buf[2];
			mCanPkg.u8Data[5] = mLbyte.u8Buf[3];
		}
		else
		{
			mCanPkg.u8Dlc = 8;
			mLbyte.u32Value = LibSysParGetCellFlag(pmCanPkg->u8Data[0]);
			mCanPkg.u8Data[1] = mLbyte.u8Buf[0];
			mCanPkg.u8Data[2] = mLbyte.u8Buf[1];
			mCanPkg.u8Data[3] = mLbyte.u8Buf[2];
			mCanPkg.u8Data[4] = mLbyte.u8Buf[3];
			mLbyte.u32Value = LibSysParGetNtcFlag(pmCanPkg->u8Data[0]);
			mCanPkg.u8Data[5] = mLbyte.u8Buf[0];
			mCanPkg.u8Data[6] = mLbyte.u8Buf[1];
			mCanPkg.u8Data[7] = mLbyte.u8Buf[2];
		}
		
	    break;
    default:
        mCanPkg.u8Dlc = 0;
    }
    AppSerialUartPkgCanbusMsg(&mCanPkg, 1);
}

static void AppSerialUartCanPkgDecode(tCanPkg *pmCanPkg)
{
	uint8_t	u8Fun;
	uint8_t	u8ObjIndex;
	uint16_t	u16SubIndex;
	uint8_t		i;
	uint16_t	u16Voltage;
	int32_t    i32Current;
	
	u8Fun = SMP_CAN_GET_FUN(pmCanPkg->mCanId.u32Value);
	u8ObjIndex = SMP_CAN_GET_OBJ_INDEX(pmCanPkg->mCanId.u32Value);
	u16SubIndex = SMP_CAN_GET_SUB_INDEX(pmCanPkg->mCanId.u32Value);
	
	switch(u8Fun)
	{
    case SMP_CAN_FUN_CMD_RX:
        switch(u8ObjIndex)
        {
        case SMP_CMD_PAR_RD_OBJ_INDEX:
            AppSerialUartSystemParResponse(pmCanPkg);
            break;
        }
        break;
	case SMP_CAN_FUN_DEBUG_RX:
		switch(u8ObjIndex)
		{
	    case SMP_DEBUG_SIMU_CURRENT_VALUE_OBJ_INDEX:	   
            i32Current = (int32_t)GET_U32_VALUE(&pmCanPkg->u8Data[0]);
            if(u16SubIndex == 0)
                HalAfeSetCurrentValue(0, i32Current);
            else
                HalAfeSetCurrentValue(1, i32Current);                  
	        break;
		case SMP_DEBUG_CELLV_SIMU_OBJ_INDEX:

			for(i=0; i<8 ; i+=2)
			{
				u16Voltage = GET_U16_VALUE(&pmCanPkg->u8Data[i]);
				HalAfeSetCellVoltage(u16SubIndex++, u16Voltage);
			}
			break;
		case SMP_DEBUG_NTCV_SIMU_OBJ_INDEX:
			for(i=0; i<8 ; i+=2)
			{
				u16Voltage = GET_U16_VALUE(&pmCanPkg->u8Data[i]);
				HalAfeSetNtcVoltage(u16SubIndex++, u16Voltage);
			}
			break;
		}
		break;
	}
}

static void AppSerialUartDecode(uint8_t *pu8Buf, uint16_t u16Len)
{
	tCanPkg	mCanPkg;
	uint8_t	i;
	switch(pu8Buf[2])
	{
	case 'C':
		//AppSerialUartSendMessage("Rcv Canbus Package");
		for(uint16_t i=3; i<u16Len;)
		{
			mCanPkg.mCanId.u8Buf[0] = pu8Buf[i++];
			mCanPkg.mCanId.u8Buf[1] = pu8Buf[i++];
			mCanPkg.mCanId.u8Buf[2] = pu8Buf[i++];
			mCanPkg.mCanId.u8Buf[3] = pu8Buf[i++];
			mCanPkg.u8Dlc = pu8Buf[i++];
			if(mCanPkg.u8Dlc > 8)
				break;
			for(uint8_t j=0; j<mCanPkg.u8Dlc; j++)
				mCanPkg.u8Data[j] = pu8Buf[i++];	
			AppSerialUartCanPkgDecode(&mCanPkg);
		}
		break;
	}
}

static void AppSerialUartGetPackage(void)
{
	uint8_t	u8RxData;
	int8_t	result;	
	char	c8Str[20];
	
	if(mUartDecode.u8IdleTime != 0)
	{
	    mUartDecode.u8IdleTime--;
	    if(mUartDecode.u8IdleTime == 0)
	        mUartDecode.u8Status = 0; 
    }
	
	for(uint8_t i=0; i<200; i++)
	{
	//	DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_9);

		result = HalUartGet(&gmHalUart0Config, &u8RxData);
		if(result != RES_SUCCESS)
			break;
		mUartDecode.u8IdleTime = UART_DECODE_IDLE_MS;
		//DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_9);
		
		sprintf(c8Str, "Rcv:%.2X", u8RxData);
		AppSerialUartSendMessage((uint8_t *)c8Str);
			
		if(mUartDecode.u8Status == 0)
		{
			if(u8RxData == 0x5A)
			{
				mUartDecode.u8Status = 1;
				mUartDecode.u8Index = 0;
				mUartDecode.u8Checksum = 0;
			}
		}
		else if(mUartDecode.u8Status == 1)
		{
			mUartDecode.u8Checksum ^= u8RxData;
			mUartDecode.u8Buf[mUartDecode.u8Index++] = u8RxData;
			if(mUartDecode.u8Index >= 2)
			{
				mUartDecode.mLen.u8Buf[0] = mUartDecode.u8Buf[0];
				mUartDecode.mLen.u8Buf[1] = mUartDecode.u8Buf[1];
				if(mUartDecode.mLen.u16Len >= 4 && mUartDecode.mLen.u16Len <= MAX_UART_DECODE_BUF_SIZE)
					mUartDecode.u8Status = 2;
				else
					mUartDecode.u8Status = 0;	
			}
		}
		else if(mUartDecode.u8Status == 2)
		{
			mUartDecode.u8Checksum ^= u8RxData;
			mUartDecode.u8Buf[mUartDecode.u8Index++] = u8RxData;
			if(mUartDecode.u8Index >= mUartDecode.mLen.u16Len)
				mUartDecode.u8Status = 3;
		}
		else if(mUartDecode.u8Status == 3)
		{
			if((u8RxData == 0x69) && (mUartDecode.u8Checksum == 0))
			{
				//AppSerialUartSendMessage("Rcv Uart Package");
				AppSerialUartDecode(mUartDecode.u8Buf, mUartDecode.mLen.u16Len);
			}
			mUartDecode.u8Status = 0;
		}
	}
}  
static void AppSerialUartHvBbuTimerHandler(__far void *pvDest, uint16_t u16Evt, void *pvDataPtr)
{	
	static	uint8_t	u8Count = 0;
	char	c8Str[20];
	if(u16Evt & kLIB_SW_TIMER_EVT_1_MS)
	{
		AppSerialUartGetPackage();
	}
	if(u16Evt & kLIB_SW_TIMER_EVT_10_2_MS)
	{
		if(gu8NotiFunTableIndex != 0)
		{
			gfpNotificationFunctionTable[gu8NotiFunTableIndex]();
		}
	}
	if(u16Evt & kLIB_SW_TIMER_EVT_1_S)
	{
		//sprintf(c8Str,"Uart Broadcast:%d", u8Count++);
		//AppSerialUartSendMessage((uint8_t *)c8Str);
		if(gu8NotiFunTableIndex == 0)
		{
			gu8NotiFunTableIndex = 1;
			//DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_8);
		}		
	}
}
static void HalUartCbExample(eTypeHalUartEvt eUartEvtt)
{

}



/* Public function prototypes -----------------------------------------------*/

void AppSerialUartSendMessage(uint8_t *pu8Str)
{
	uint8_t		u8Buffer[256];
	uint8_t		u8Index,u8Chekcsum,u8Len;
	uint8_t		i;
	
	u8Len = strlen((char *)pu8Str);
	u8Buffer[0] = 0x5A;
	u8Buffer[1] = u8Len + 5;
	u8Buffer[2] = 0; 
	u8Buffer[3] = 'T';
	u8Chekcsum = 'T';
	u8Chekcsum ^= u8Buffer[1];
	u8Index = 4;
	for(i=0; i<u8Len; i++)
	{
		u8Buffer[u8Index++] = pu8Str[i];
		u8Chekcsum ^= pu8Str[i];
	}
	u8Buffer[u8Index++] = 0;
	u8Buffer[u8Index++] = u8Chekcsum;
	u8Buffer[u8Index++] = 0x69;
	
	for(i=0; i<u8Index; i++)
		HalUartPut(&gmHalUart0Config, u8Buffer[i]);
}

void AppSerialUartHvBbuOpen(void)
{
	gmHalUart0Config.pmUartChannel = BSP_UART_0;
    gmHalUart0Config.mUartFifoConfig.pu8HalUartTxFifo = gu8Uart0TxFifoTest;
    gmHalUart0Config.mUartFifoConfig.u16HalUartTxFifoSize = UART_TX_BUF_SIZE;
    gmHalUart0Config.mUartFifoConfig.pu8HalUartRxFifo = gu8Uart0RxFifoTest;
    gmHalUart0Config.mUartFifoConfig.u16HalUartRxFifoSize = UART_RX_BUF_SIZE;
    gmHalUart0Config.fpUartEvent = HalUartCbExample;
    gmHalUart0Config.eBaud = kHAL_UART_BAUD_115200;
    gmHalUart0Config.eMode = kHAL_UART_MAIN_MODE_NORMAL;
    gmHalUart0Config.eDirection = kHAL_UART_MAIN_DIRECTION_TX_RX;
    gmHalUart0Config.eFlowControl = kHAL_UART_MAIN_FLOW_CONTROL_NONE;
    gmHalUart0Config.eParity = kHAL_UART_MAIN_PARITY_NONE;
    gmHalUart0Config.eWordLength = kHAL_UART_MAIN_WORD_LENGTH_8_BITS;
    gmHalUart0Config.eStopBits = kHAL_UART_MAIN_STOP_BITS_ONE;
    gmHalUart0Config.eOversamplingRate = kHAL_UART_MAIN_OVERSAMPLING_RATE_16X;
    
    HalUartInit(&gmHalUart0Config);

  	LibSoftwareTimerHandlerOpen(AppSerialUartHvBbuTimerHandler, 0);
  	
  	mUartDecode.u8Status = 0;
}



/************************ (C) COPYRIGHT *****END OF FILE****/    


