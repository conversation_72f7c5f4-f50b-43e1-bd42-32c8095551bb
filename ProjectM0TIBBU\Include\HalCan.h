/*
******************************************************************************
* @file     <PERSON><PERSON>an.h
* <AUTHOR> & <PERSON> Lee
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_CAN_H__
#define __HAL_CAN_H__

#ifdef __cplusplus
extern "C" {
#endif
/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "Bsp.h"
#include "LibFifoBase.h"
#include "HalMcuPeripheralConfig.h"
#include "LibSoftwareTimerHandler.h"
/* Global define ------------------------------------------------------------*/
//#define HAL_CAN_ENABLE_EXAMPLE

#define HAL_CAN_0                       CANFD0
#define HAL_CAN_0_IRQHandler            CANFD0_IRQHandler

#define HAL_CAN_1                       CANFD1
#define HAL_CAN_1_IRQHandler            CANFD1_IRQHandler
#define HAL_CAN_CALLBACK_HANDLER_SIZE   (4)

#define HAL_CAN_DATA_SIZE_CLASSIC       (8)
#define HAL_CAN_DATA_SIZE_FD            (64)
#define HAL_CAN_DATA_SIZE_TARGET        HAL_CAN_DATA_SIZE_CLASSIC
#define HAL_CAN_FW_TX_FIFO_SIZE         (400)
#define HAL_CAN_FW_RX_FIFO_SIZE         (400)
/* Global typedef -----------------------------------------------------------*/
typedef enum
{
    kHAL_CAN_CHANNEL_0 = 0,
    kHAL_CAN_CHANNEL_1,
    kHAL_CAN_CHANNEL_COUNT
} eTypeHalCanChannel;

typedef enum
{
    kHAL_CAN_EVENT_READY = 0,
    kHAL_CAN_EVENT_BUS_OFF,
} eTypeHalCanEvent;

typedef struct
{
    uint32_t u32Id;
    uint8_t u8Dlc;
    union
    {
        uint8_t u8Data[HAL_CAN_DATA_SIZE_TARGET];
        uint16_t u16Data[(HAL_CAN_DATA_SIZE_TARGET / 2)];
        uint32_t u32Data[(HAL_CAN_DATA_SIZE_TARGET / 4)];
        uint64_t u64Data[(HAL_CAN_DATA_SIZE_TARGET / 8)];
    } tUnionData;
} tHalCanFrame;

typedef void (*tfpHalCanEvent)(eTypeHalCanEvent eEvent, void *pData);
typedef void (*tfpHalCanEventCallback)(void *pContext, eTypeHalCanEvent eEvent, void *pData);

typedef struct
{
    eTypeHalCanChannel eChannel;
    bool bFdMode, bBitRateSwitch, bDisabledAutomaticRetransmission;
    bool bListenOnly;
    uint32_t u32NominalBitRate, u32DataBitRate;

    tfpHalCanEvent fpEvent;
} tHalCan;
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
int8_t HalCanOpen(tHalCan *pmHalCan);
bool HalCanIsExtendedFrame(uint32_t u32Id);
bool HalCanIsRemoteFrame(uint32_t u32Id);
uint32_t HalCanGetRawId(uint32_t u32Id);
uint32_t HalCanBuildId(uint32_t u32Id, bool bIsExtendedFrame, bool bIsRemoteFrame);
bool HalCanIsBusOff(tHalCan *pmHalCan);
uint16_t HalCanGetTxFifoFreeCount(tHalCan *pmHalCan);
int8_t HalCanPut(tHalCan *pmHalCan, tHalCanFrame *pmCanFrame);
uint16_t HalCanGetRxFifoCount(tHalCan *pmHalCan);
int8_t HalCanGet(tHalCan *pmHalCan, tHalCanFrame *pmCanFrame);
int8_t HalCanRegisterEventHandler(tHalCan *pmHalCan, void *pContext, tfpHalCanEventCallback fpCallback);
int8_t HalCanUnregisterEventHandler(tHalCan *pmHalCan, void *pContext, tfpHalCanEventCallback fpCallback);

#ifdef HAL_CAN_ENABLE_EXAMPLE
void HalCanExampleInit(void);
void HalCanExampleTransmit(void);
#endif

#ifdef __cplusplus
}
#endif

#endif