/**
  ******************************************************************************
  * @file        AppSerialCanDavinci.c
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2021/10/20
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

#include "HalAfe.h"

#include "HalCan.h"

#include "LibSoftwareTimerHandler.h"
#include "LibHardwareTimerHandler.h"
//#include "ApiModbusTCPIP.h"
//#include "ApiEthernetUdp.h"
#include "AppSerialCanDavinci.h"

#include "AppSerialCanDavinciDebug.h"
#include "AppSerialCanDavinciDetail.h"
#include "AppSerialCanDavinciCommon.h"
#include "AppSerialCanDavinciCmd.h"
#include "AppSerialCanDavinciBaseCmd.h"
#include "AppSerialCanDavinciFirmwareUpgrade.h"
#include "AppSerialCanDavinciNotification.h"
//#include "SEGGER_RTT.h"
#include "HalRtc.h"
//#include "AppBms.h"
#include "AppSerialCanDavinciCurrentBoard.h"

#define	BOARD_REV1

#define	appSerialCanDavinciDebugMsg(str)	appSerialCanDavinciSendTextMessage(str)

/* Private typedef -----------------------------------------------------------*/
#define	CAN_TX_BUF_SIZE	400
#define	CAN_RX_BUF_SIZE	400

tHalCanFrame	Davinci_can_tx_buffer[CAN_TX_BUF_SIZE + 1] = {0};
tHalCanFrame  Davinci_can_rx_buffer[CAN_RX_BUF_SIZE + 1] = {0};

#ifdef BOARD_REV1
tHalCanFrame	Davinci_can1_tx_buffer[CAN_TX_BUF_SIZE + 1] = {0};
tHalCanFrame  Davinci_can1_rx_buffer[CAN_RX_BUF_SIZE + 1] = {0};
#endif

#define DAVINCI_CAN0 { \
	.eChannel = kHAL_CAN_CHANNEL_0, \
	.u32NominalBitRate = 500000, \
	.bFdMode = false, \
	.bBitRateSwitch = false, \
	.bDisabledAutomaticRetransmission = true, \
	.bListenOnly = false, \
}  
tHalCan mDavinci_can0 = DAVINCI_CAN0;

#ifdef BOARD_REV1
#define DAVINCI_CAN1 { \
    .eChannel = kHAL_CAN_CHANNEL_0, \
    .u32NominalBitRate = 500000, \
    .bFdMode = false, \
    .bBitRateSwitch = false, \
    .bDisabledAutomaticRetransmission = true, \
    .bListenOnly = false, \
}  
tHalCan mDavinci_can1 = DAVINCI_CAN1;
#endif
						  

	
/* Private define ------------------------------------------------------------*/



/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint32_t	can1_buf_c = 0;
/* Private function prototypes -----------------------------------------------*/

SMP_CAN_DECODE_CMD_START(mDavinciCanFunDecodeTab)
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_RX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunBaseRxTx)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunBaseRxTx)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunCmdRx)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunDebugRx)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_COMMON_RX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunCommonRx)								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_COMMON_TX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunCommonRx)
															
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CB_TX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunCb)									

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_FU_RX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunFuRx)								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 0,
									0,
									0),
								CHECK_SMP_CAN_FUN,
								DavinciCanFunDetailTx)								

SMP_CAN_DECODE_CMD_END();



//--Send: 5A 05 55 70 70 50 69 

void DavinciCan_cb(eTypeHalCanEvent p_evt){
    #if 0
  switch(p_evt){
    case CAN_DATA_READY:
      /* received data handle */
		can1_buf_c++;
    break;
    case CAN_TX_EMPTY:
      /* Data transmission complete handle */
    break;
    case CAN_COMMUNICATION_ERR:
      /* occurred during reception */
    break;
    case CAN_BUFFER_FULL:
      /* occurred UART buffer full */
    break;    
    default:
    break;
  }
    #endif
}
static void dump_danpackage(tHalCanFrame *pCanPkg)
{
#if	0	
	char	str[100];
	char	str1[10];
	int		i;
	sprintf(str,"RCV: %.8lX %d",pCanPkg -> u32Id , pCanPkg -> u8Dlc);
	
	for(i=0; i<pCanPkg -> u8Dlc; i++)
	{
		sprintf(str1," %.2X",pCanPkg->tUnionData.u8Data[i]);
		strcat(str, str1);
	}
	appSerialCanDavinciDebugMsg(str);
#endif	
}


static void canDavinciPaserCanPackage(void)
{
	uint8_t	i;
	tHalCanFrame	CanPkg;
	uint8_t cmdIndex;
	
	for(i=0; i<6; i++)
	{
		if(HalCanGet(&mDavinci_can0, &CanPkg) != RES_SUCCESS)
			break;
		dump_danpackage(&CanPkg);	
	 	cmdIndex = 0;
 		for(cmdIndex = 0; mDavinciCanFunDecodeTab[cmdIndex].fun != 0; cmdIndex++)
 		{			
 			if((mDavinciCanFunDecodeTab[cmdIndex].canid & mDavinciCanFunDecodeTab[cmdIndex].mask) == 
 		  	 	(mDavinciCanFunDecodeTab[cmdIndex].mask & CanPkg.u32Id))
 		  	{
 				mDavinciCanFunDecodeTab[cmdIndex].fun(&CanPkg);
 				break;
 			}
 		}
	}		
}

static void appSerialCanDavinciTimerHandler(__far void *dest, uint16_t evt, void *vDataPtr)
{
	if(evt & kLIB_SW_TIMER_EVT_1_MS)
	{
		canDavinciPaserCanPackage();
	}
	else if(evt & kLIB_SW_TIMER_EVT_10_3_MS)
	{
		appSerialCanDavinciNotificationHandler(evt);
	}
}

/* Public function prototypes -----------------------------------------------*/
uint8_t appSerialCanDavinciIsCorrectScuId(tHalCanFrame *pCanPkg)
{
	if(SMP_CAN_GET_SCU_ID(pCanPkg -> u32Id) == appProjectGetScuId())
		return 1;
	else
		return 0;
}

int8_t appSerialCanDavinciPutPkgToCanFifo(tHalCanFrame *pCanPkg)
{
//	SMP_CAN_GET_SCU_ID(id)		((id>>18)&0x7f)
//	appSerialCanDavinciPutPkgToCanFifo
#if 0
	{
		static tUnion64Bits		CanTest = {0};
		tHalCanFrame	CanPkg;
		uint8_t	i;
		
		CanPkg.u8Dlc = 8;
		CanPkg.u32Id = 0x12345678;
		
	
		for(i=0; i<8; i++)
			CanPkg.tUnionData.u8Data[i] = CanTest.b[i];
		
		CanTest.ll++;		
		return HalCanPut(&mDavinci_can0, &CanPkg);
	}
#endif	
	/// [CH] : 寫值進UDP FIFO，滿了繼續寫
	ApiEthUdpPutCanPkgToFifo(pCanPkg);
	return HalCanPut(&mDavinci_can0, pCanPkg);
}

/// [CH] : 將UDP來的資料放入CAN Rx fifo
#if MAO_DISSABLE
int8_t appSerialCanDavinciPutPkgToCanRxFifo(tHalCanFrame *pCanPkg)
{
	return smp_can_put_data_to_rx_fifo(&mDavinci_can0, pCanPkg);	
}
#endif

void appSerialCanDavinciSendGpioStatus(uint32_t mask, uint32_t ststus)
{
	tHalCanFrame	CanPkg;
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_TX, appProjectGetScuId(),
							SMP_DEBUG_GPIO_OBJ_INDEX,
							0x201);
	CanPkg.u8Dlc = 8;
	LibFifoMemcpy(&CanPkg.tUnionData.u8Data[0], &mask, 4);
	LibFifoMemcpy(&CanPkg.tUnionData.u8Data[4], &ststus, 4);
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}



void appSerialCanDavinciSendTextMessage(char *msg)
{
	uint8_t	buffer[256];
	uint8_t	i,len,checksum,index;
	tHalCanFrame	CanPkg;
	uint8_t		scuid;
#ifndef JOHNNY_DISABLE		
	if(appBmsIsScudIdReady() == 0)
	{
		scuid = 1;
	}
	else
	{
		scuid = appProjectGetScuId();
	}
#endif
	scuid = appBmsGetScuId();
	
	len = strlen((char *)msg);
	if(len >250)
		len = 250;
	buffer[0] = 0x5A;
	buffer[1] = len + 5;
	buffer[2] = 0;
	buffer[3] = 'T';
	checksum = 'T';
	checksum ^= buffer[1];
	index = 4;
	for(i=0; i<len; i++)
	{
		buffer[index++] = msg[i];
		checksum ^= msg[i];
	}
	buffer[index++] = 0;
	buffer[index++] = checksum;
	buffer[index++] = 0x69;


	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_TX, scuid, //appProjectGetScuId(),
									SMP_DEBUG_PKG_TX_OBJ_INDEX,
									0);
	for(i=0; i<index; i+=8)
	{
		len = index - i;
		if(len > 8)
			len = 8;
		LibFifoMemcpy(CanPkg.tUnionData.u8Data, &buffer[i], len);
		CanPkg.u8Dlc = len;
		
		/// [CH] : 就是這段造成當 
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}
}



void canNotiHwTimerHandler(__far void *dest, uint16_t evt, void *vDataPtr);


void appSerialCanDavinciOpen(void)
{
	char	str[100];
	#if	1
	int	res;
	
	res = HalCanOpen(&mDavinci_can0);//

	sprintf(str,"Can0 ini %d", res);
	appSerialCanDavinciDebugMsg(str);

#ifdef BOARD_REV1	
	
	if(HalCanOpen(&mDavinci_can1)==RES_SUCCESS){
		appSerialCanDavinciDebugMsg("Can 1 ini success");
	 }else{
		appSerialCanDavinciDebugMsg("Can 1 ini fail");
  	}  
#endif
	
	#endif
  	LibSwTimerOpen(appSerialCanDavinciTimerHandler, 0);
	LibHwTimerOpen(canNotiHwTimerHandler, 0);
}
#if MAO_DISSABLE
void appSerialCanDavinciClose(void)
{
	smp_can_deinit(&mDavinci_can0);
#ifdef BOARD_REV1
	smp_can_deinit(&mDavinci_can1);
#endif	
}
#endif

uint16_t AppSerialCan0DavinciGetTxFifoFreeSize(void)
{
	return (HalCanGetTxFifoFreeCount(&mDavinci_can0));
}

/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    


