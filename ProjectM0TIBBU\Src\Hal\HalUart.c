/*
******************************************************************************
* @file     HalUart.c
* <AUTHOR>
* @brief    This file include MSPM0G3519 UART Hardwarre Abstraction Layer Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "HalUart.h" 
#include "Bsp.h" 
#include "LibUartFifo.h"
#include "LibFunctionReturnValueDefine.h"

/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
static tLibUartFifoStatus gmHalUart0TxFifoStatus;
static tLibUartFifoStatus gmHalUart0RxFifoStatus;
uint8_t gu8HalUart0TxFifo[UART0_TX_FIFO_SIZE];
uint8_t gu8HalUart0RxFifo[UART0_RX_FIFO_SIZE];
tfpHalUartEvent gfpHalUart0Event = NULL;

static tLibUartFifoStatus gmHalUart1TxFifoStatus;
static tLibUartFifoStatus gmHalUart1RxFifoStatus;
uint8_t gu8HalUart1TxFifo[UART1_TX_FIFO_SIZE];
uint8_t gu8HalUart1RxFifo[UART1_RX_FIFO_SIZE];
tfpHalUartEvent gfpHalUart1Event = NULL;

/* Local function prototypes ------------------------------------------------*/
static void HalUartPowerInit(UART_Regs* pmHalUartConfig);
static int8_t HalUartFifoStatusInit(tLibUartFifoStatus* pmUartFifoStatus, uint8_t* pu8FifoStartAddr, uint16_t u16FifoSize);
static tHalUartChannelConfig HalUartFindUartChannel(UART_Regs* pmUartChannel, tfpHalUartEvent fpUartEvent);
static tHalUartBaudConfig HalUartFindUartBaud(eTypeHalUartBaud eUartBaud);

/* Global function prototypes -----------------------------------------------*/

static void HalUartPowerInit(UART_Regs* pmHalUartConfig)
{
    DL_UART_Main_reset(pmHalUartConfig);

    DL_UART_Main_enablePower(pmHalUartConfig);
}

static int8_t HalUartFifoStatusInit(tLibUartFifoStatus* pmUartFifoStatus, uint8_t* pu8FifoStartAddr, uint16_t u16FifoSize)
{
    // Check buffer for null pointer.
    if (pu8FifoStartAddr == NULL)
    {
        return RES_ERROR_NULL;
    }

    pmUartFifoStatus->pu8FifoStartAddr   = pu8FifoStartAddr;
    pmUartFifoStatus->u16FifoSize 	     = (u16FifoSize - 1);
    pmUartFifoStatus->u16FifoPushInPosi  = 0;
    pmUartFifoStatus->u16FifoPopOutPosi  = 0;

    return RES_SUCCESS;
}

int8_t HalUartPut(tHalUartConfig* pmHalUartConfig, uint8_t u8Data)
{
    uint16_t u16Size = 0;
    uint16_t u16Idx = 0;
	

    if (pmHalUartConfig->pmUartChannel == UART0)
    {
        if (LibUartFifoPush(&gmHalUart0TxFifoStatus, u8Data) == RES_SUCCESS)
	    {
		    if (LibUartFifoGetUsedSize(&gmHalUart0TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > UART0_TX_FIFO_SIZE)	
                {
                    u16Size = UART0_TX_FIFO_SIZE;
                }
	      
			    if (gfpHalUart0Event != NULL)
                {
				    gfpHalUart0Event(kHAL_UART_TX_READY_TO_SEND);
                }

                if (DL_UART_Main_isTXFIFOEmpty(UART0) == true)
                {
			        for (u16Idx = 0; u16Idx < u16Size; u16Idx++)
			        {	
				        LibUartFifoPop(&gmHalUart0TxFifoStatus, &gu8HalUart0TxFifo[u16Idx]);
                    }

                    DL_UART_Main_fillTXFIFO(UART0, &gu8HalUart0TxFifo[0], u16Size);
			    }
            }
        }
	}
    else if (pmHalUartConfig->pmUartChannel == UART1)
    {
        if (LibUartFifoPush(&gmHalUart1TxFifoStatus, u8Data) == RES_SUCCESS)
	    {
		    if (LibUartFifoGetUsedSize(&gmHalUart1TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > UART1_TX_FIFO_SIZE)	
                {
                    u16Size = UART1_TX_FIFO_SIZE;
                }
	      
			    if (gfpHalUart1Event != NULL)
                {
				    gfpHalUart1Event(kHAL_UART_TX_READY_TO_SEND);
                }

                if (DL_UART_Main_isTXFIFOEmpty(UART1) == true)
                {
			        for (u16Idx = 0; u16Idx < u16Size; u16Idx++)
			        {	
				        LibUartFifoPop(&gmHalUart1TxFifoStatus, &gu8HalUart1TxFifo[u16Idx]);
                    }

                    DL_UART_Main_fillTXFIFO(UART1, &gu8HalUart1TxFifo[0], u16Size);
			    }
            }
        }
	}
    else
	{
	    return RES_ERROR_NULL;
	}
	
	return RES_SUCCESS;
}

int8_t HalUartGet(tHalUartConfig* pmHalUartConfig, uint8_t* pu8Data)
{
    int8_t i8ReturnValue;
	

    if (pmHalUartConfig->pmUartChannel == UART0)
    {
	    i8ReturnValue = LibUartFifoPop(&gmHalUart0RxFifoStatus, pu8Data);
    }
    else if (pmHalUartConfig->pmUartChannel == UART1)
    {
	    i8ReturnValue = LibUartFifoPop(&gmHalUart1RxFifoStatus, pu8Data);
    }
    else
	{
		return RES_ERROR_NULL;
	}

	return(i8ReturnValue);
}

static tHalUartChannelConfig HalUartFindUartChannel(UART_Regs* pmUartChannel, tfpHalUartEvent fpUartEvent)
{
    tHalUartChannelConfig mUartChannelConfig;


    if (pmUartChannel == UART0)
    {
        mUartChannelConfig.pmTxPort = BSP_UART_0_TX_PORT;
        mUartChannelConfig.u32TxPin = BSP_UART_0_TX_PIN;
        mUartChannelConfig.u32TxIomux = BSP_UART_0_TX_IOMUX;
        mUartChannelConfig.u32TxIomuxPf = BSP_UART_0_TX_IOMUX_PF;
        mUartChannelConfig.pmRxPort = BSP_UART_0_RX_PORT;
        mUartChannelConfig.u32RxPin = BSP_UART_0_RX_PIN;
        mUartChannelConfig.u32RxIomux = BSP_UART_0_RX_IOMUX;
        mUartChannelConfig.u32RxIomuxPf = BSP_UART_0_RX_IOMUX_PF;
        mUartChannelConfig.u8IntIrqn = UART0_INT_IRQn;
        mUartChannelConfig.pmUartTxFifoStatus = &gmHalUart0TxFifoStatus;
        mUartChannelConfig.pmUartRxFifoStatus = &gmHalUart0RxFifoStatus;
        gfpHalUart0Event = fpUartEvent;
    }
    else if (pmUartChannel == UART1)
    {   
        mUartChannelConfig.pmTxPort = BSP_UART_1_TX_PORT;
        mUartChannelConfig.u32TxPin = BSP_UART_1_TX_PIN;
        mUartChannelConfig.u32TxIomux = BSP_UART_1_TX_IOMUX;
        mUartChannelConfig.u32TxIomuxPf = BSP_UART_1_TX_IOMUX_PF;
        mUartChannelConfig.pmRxPort = BSP_UART_1_RX_PORT;
        mUartChannelConfig.u32RxPin = BSP_UART_1_RX_PIN;
        mUartChannelConfig.u32RxIomux = BSP_UART_1_RX_IOMUX;
        mUartChannelConfig.u32RxIomuxPf = BSP_UART_1_RX_IOMUX_PF;
        mUartChannelConfig.u8IntIrqn = UART1_INT_IRQn;
        mUartChannelConfig.pmUartTxFifoStatus = &gmHalUart1TxFifoStatus;
        mUartChannelConfig.pmUartRxFifoStatus = &gmHalUart1RxFifoStatus;
        gfpHalUart1Event = fpUartEvent;
	}

    return (mUartChannelConfig);
}

static tHalUartBaudConfig HalUartFindUartBaud(eTypeHalUartBaud eUartBaud)
{
    tHalUartBaudConfig mHalUartBaudConfig;


    switch (eUartBaud)
    {
		case (kHAL_UART_BAUD_9600):	
            mHalUartBaudConfig.u16Ibrd = UART_IBRD_40_MHZ_9600_BAUD;
            mHalUartBaudConfig.u16Fbrd = UART_FBRD_40_MHZ_9600_BAUD;
            break;
		case (kHAL_UART_BAUD_19200):
            mHalUartBaudConfig.u16Ibrd = UART_IBRD_40_MHZ_19200_BAUD;
            mHalUartBaudConfig.u16Fbrd = UART_FBRD_40_MHZ_19200_BAUD;	
            break;
        case (kHAL_UART_BAUD_38400):
            mHalUartBaudConfig.u16Ibrd = UART_IBRD_40_MHZ_38400_BAUD;
            mHalUartBaudConfig.u16Fbrd = UART_FBRD_40_MHZ_38400_BAUD;	
            break;
		case (kHAL_UART_BAUD_57600):
            mHalUartBaudConfig.u16Ibrd = UART_IBRD_40_MHZ_57600_BAUD;
            mHalUartBaudConfig.u16Fbrd = UART_FBRD_40_MHZ_57600_BAUD;		
            break;
		case (kHAL_UART_BAUD_115200):
            mHalUartBaudConfig.u16Ibrd = UART_IBRD_40_MHZ_115200_BAUD;
            mHalUartBaudConfig.u16Fbrd = UART_FBRD_40_MHZ_115200_BAUD;		
            break;     
	}

    return (mHalUartBaudConfig); 
}

void HalUartInit(tHalUartConfig* pmHalUartConfig)
{
    DL_UART_Main_Config mHalDlUartConfig;
    DL_UART_Main_ClockConfig mHalDlUartClockConfig;
    tHalUartChannelConfig mHalUartChannelConfig;
    tHalUartBaudConfig mHalUartBaudConfig;


    mHalUartChannelConfig = HalUartFindUartChannel(pmHalUartConfig->pmUartChannel, pmHalUartConfig->fpUartEvent);

    HalUartPowerInit(pmHalUartConfig->pmUartChannel);

    DL_GPIO_initPeripheralOutputFunction(mHalUartChannelConfig.u32TxIomux, mHalUartChannelConfig.u32TxIomuxPf);
    DL_GPIO_initPeripheralInputFunction(mHalUartChannelConfig.u32RxIomux, mHalUartChannelConfig.u32RxIomuxPf);

    /// [CH] : 40MHz No divide
    mHalDlUartClockConfig.clockSel = DL_UART_MAIN_CLOCK_BUSCLK;
    mHalDlUartClockConfig.divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1;

    DL_UART_Main_setClockConfig(pmHalUartConfig->pmUartChannel, (DL_UART_Main_ClockConfig *)&mHalDlUartClockConfig);

    mHalDlUartConfig.mode = (DL_UART_MODE)pmHalUartConfig->eMode;
    mHalDlUartConfig.direction = (DL_UART_DIRECTION)pmHalUartConfig->eDirection;
    mHalDlUartConfig.flowControl = (DL_UART_FLOW_CONTROL)pmHalUartConfig->eFlowControl;
    mHalDlUartConfig.parity = (DL_UART_PARITY)pmHalUartConfig->eParity;
    mHalDlUartConfig.wordLength = (DL_UART_WORD_LENGTH)pmHalUartConfig->eWordLength;
    mHalDlUartConfig.stopBits = (DL_UART_STOP_BITS)pmHalUartConfig->eStopBits;

    DL_UART_Main_init(pmHalUartConfig->pmUartChannel, (DL_UART_Main_Config *)&mHalDlUartConfig);
	
    DL_UART_Main_setOversampling(pmHalUartConfig->pmUartChannel, (DL_UART_OVERSAMPLING_RATE)pmHalUartConfig->eOversamplingRate);

    mHalUartBaudConfig = HalUartFindUartBaud(pmHalUartConfig->eBaud);

    DL_UART_Main_setBaudRateDivisor(pmHalUartConfig->pmUartChannel, mHalUartBaudConfig.u16Ibrd, mHalUartBaudConfig.u16Fbrd);

    DL_UART_Main_enableInterrupt(pmHalUartConfig->pmUartChannel, DL_UART_MAIN_INTERRUPT_TX 
    | DL_UART_MAIN_INTERRUPT_RX);

    DL_UART_Main_enableFIFOs(pmHalUartConfig->pmUartChannel);
    DL_UART_Main_setRXFIFOThreshold(pmHalUartConfig->pmUartChannel, DL_UART_RX_FIFO_LEVEL_ONE_ENTRY);
    DL_UART_Main_setTXFIFOThreshold(pmHalUartConfig->pmUartChannel, DL_UART_TX_FIFO_LEVEL_EMPTY);

    DL_UART_Main_enable(pmHalUartConfig->pmUartChannel);

    NVIC_EnableIRQ(mHalUartChannelConfig.u8IntIrqn);

    HalUartFifoStatusInit(mHalUartChannelConfig.pmUartTxFifoStatus, pmHalUartConfig->mUartFifoConfig.pu8HalUartTxFifo, 
    pmHalUartConfig->mUartFifoConfig.u16HalUartTxFifoSize);
    HalUartFifoStatusInit(mHalUartChannelConfig.pmUartRxFifoStatus, pmHalUartConfig->mUartFifoConfig.pu8HalUartRxFifo, 
    pmHalUartConfig->mUartFifoConfig.u16HalUartRxFifoSize);
}

void UART0_IRQHandler(void)
{
    uint16_t u16Size = 0;
    uint16_t u16Idx = 0;

    
    //HalGpioTogglePin(GPIOB, DL_GPIO_PIN_30); 
    switch (DL_UART_Main_getPendingInterrupt(UART0)) 
    {   
        case DL_UART_MAIN_IIDX_TX:
            //HalGpioTogglePin(GPIOB, DL_GPIO_PIN_29); 
		    if (LibUartFifoGetUsedSize(&gmHalUart0TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > UART0_TX_FIFO_SIZE)	
                {
                    u16Size = UART0_TX_FIFO_SIZE;
                }

			    for (u16Idx = 0; u16Idx < u16Size; u16Idx++)
			    {	
				    LibUartFifoPop(&gmHalUart0TxFifoStatus, &gu8HalUart0TxFifo[u16Idx]);
                }

                DL_UART_Main_fillTXFIFO(UART0, &gu8HalUart0TxFifo[0], u16Size);
            } 
            break;    
        case DL_UART_MAIN_IIDX_RX:  
            while (DL_UART_Main_isRXFIFOEmpty(UART0) == false)
            {
                //HalGpioTogglePin(GPIOC, DL_GPIO_PIN_3); 

                gu8HalUart0RxFifo[0] = DL_UART_Main_receiveData(UART0);
                if (LibUartFifoPush(&gmHalUart0RxFifoStatus, gu8HalUart0RxFifo[0]) == RES_SUCCESS)
                {
                    if (gfpHalUart0Event != NULL)
                    {
                        gfpHalUart0Event(kHAL_UART_DATA_READY);
                    }   
                }
                else
                {
                    if (gfpHalUart0Event != NULL)
                    {
                        gfpHalUart0Event(kHAL_UART_BUFFER_FULL);
                    }
                }
            }
            break;
        default:
            break;
    }
}

void UART1_IRQHandler(void)
{
    uint16_t u16Size = 0;
    uint16_t u16Idx = 0;

    
    switch (DL_UART_Main_getPendingInterrupt(UART1)) 
    {   
        case DL_UART_MAIN_IIDX_TX:
		    if (LibUartFifoGetUsedSize(&gmHalUart1TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > UART1_TX_FIFO_SIZE)	
                {
                    u16Size = UART1_TX_FIFO_SIZE;
                }

			    for (u16Idx = 0; u16Idx < u16Size; u16Idx++)
			    {	
				    LibUartFifoPop(&gmHalUart1TxFifoStatus, &gu8HalUart1TxFifo[u16Idx]);
                }

                DL_UART_Main_fillTXFIFO(UART1, &gu8HalUart1TxFifo[0], u16Size);
            } 
            break;     
        case DL_UART_MAIN_IIDX_RX:  
            while (DL_UART_Main_isRXFIFOEmpty(UART1) == false)
            {
                gu8HalUart1RxFifo[0] = DL_UART_Main_receiveData(UART1);
                if (LibUartFifoPush(&gmHalUart1RxFifoStatus, gu8HalUart1RxFifo[0]) == RES_SUCCESS)
                {
                    if (gfpHalUart1Event != NULL)
                    {
                        gfpHalUart1Event(kHAL_UART_DATA_READY);
                    }   
                }
                else
                {
                    if (gfpHalUart1Event != NULL)
                    {
                        gfpHalUart1Event(kHAL_UART_BUFFER_FULL);
                    }
                }
            }	
            break;
        default:
            break;
    }
}


#define	UART_FIFO_TEST_SIZE	(256)
static uint8_t gu8Uart0TxFifoTest[UART_FIFO_TEST_SIZE];
static uint8_t gu8Uart0RxFifoTest[UART_FIFO_TEST_SIZE];
static tHalUartConfig gmHalUart0Config;
static uint8_t gu8Uart1TxFifoTest[UART_FIFO_TEST_SIZE];
static uint8_t gu8Uart1RxFifoTest[UART_FIFO_TEST_SIZE];
static tHalUartConfig gmHalUart1Config;

static void HalUartCbExample(eTypeHalUartEvt eUartEvtt)
{

}

void HalUartInitExample(void)
{
    gmHalUart0Config.pmUartChannel = BSP_UART_0;
    gmHalUart0Config.mUartFifoConfig.pu8HalUartTxFifo = gu8Uart0TxFifoTest;
    gmHalUart0Config.mUartFifoConfig.u16HalUartTxFifoSize = UART_FIFO_TEST_SIZE;
    gmHalUart0Config.mUartFifoConfig.pu8HalUartRxFifo = gu8Uart0RxFifoTest;
    gmHalUart0Config.mUartFifoConfig.u16HalUartRxFifoSize = UART_FIFO_TEST_SIZE;
    gmHalUart0Config.fpUartEvent = HalUartCbExample;
    gmHalUart0Config.eBaud = kHAL_UART_BAUD_115200;
    gmHalUart0Config.eMode = kHAL_UART_MAIN_MODE_NORMAL;
    gmHalUart0Config.eDirection = kHAL_UART_MAIN_DIRECTION_TX_RX;
    gmHalUart0Config.eFlowControl = kHAL_UART_MAIN_FLOW_CONTROL_NONE;
    gmHalUart0Config.eParity = kHAL_UART_MAIN_PARITY_NONE;
    gmHalUart0Config.eWordLength = kHAL_UART_MAIN_WORD_LENGTH_8_BITS;
    gmHalUart0Config.eStopBits = kHAL_UART_MAIN_STOP_BITS_ONE;
    gmHalUart0Config.eOversamplingRate = kHAL_UART_MAIN_OVERSAMPLING_RATE_16X;
    HalUartInit(&gmHalUart0Config);

    gmHalUart1Config.pmUartChannel = BSP_UART_1;
    gmHalUart1Config.mUartFifoConfig.pu8HalUartTxFifo = gu8Uart1TxFifoTest;
    gmHalUart1Config.mUartFifoConfig.u16HalUartTxFifoSize = UART_FIFO_TEST_SIZE;
    gmHalUart1Config.mUartFifoConfig.pu8HalUartRxFifo = gu8Uart1RxFifoTest;
    gmHalUart1Config.mUartFifoConfig.u16HalUartRxFifoSize = UART_FIFO_TEST_SIZE;
    gmHalUart1Config.fpUartEvent = HalUartCbExample;
    gmHalUart1Config.eBaud = kHAL_UART_BAUD_115200;
    gmHalUart1Config.eMode = kHAL_UART_MAIN_MODE_NORMAL;
    gmHalUart1Config.eDirection = kHAL_UART_MAIN_DIRECTION_TX_RX;
    gmHalUart1Config.eFlowControl = kHAL_UART_MAIN_FLOW_CONTROL_NONE;
    gmHalUart1Config.eParity = kHAL_UART_MAIN_PARITY_NONE;
    gmHalUart1Config.eWordLength = kHAL_UART_MAIN_WORD_LENGTH_8_BITS;
    gmHalUart1Config.eStopBits = kHAL_UART_MAIN_STOP_BITS_ONE;
    gmHalUart1Config.eOversamplingRate = kHAL_UART_MAIN_OVERSAMPLING_RATE_16X;
    HalUartInit(&gmHalUart1Config);
}

void HalUartTxRxExample(void)
{
	uint8_t	u8Idx;
	const uint8_t u8TxData[10] = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09};
    uint8_t u8RxData;

   
    for (u8Idx = 0; u8Idx < 10; u8Idx++)
    {
        if (HalUartGet(&gmHalUart0Config, &u8RxData) != RES_SUCCESS)
        {
            break;
        }

        if (u8RxData == 0x87)
        {
	        for (u8Idx = 0; u8Idx < 10; u8Idx++)
            {
		        HalUartPut(&gmHalUart0Config, u8TxData[u8Idx]);
            }
        }	 
    }
}

void HalUartSendTxExample(uint8_t* pu8TxData, uint8_t u8TxSize)
{
    uint8_t	u8Idx;


    for (u8Idx = 0; u8Idx < u8TxSize; u8Idx++)
    {
	    HalUartPut(&gmHalUart0Config, pu8TxData[u8Idx]);
    }   
}