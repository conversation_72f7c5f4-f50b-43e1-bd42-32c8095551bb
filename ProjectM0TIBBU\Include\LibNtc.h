/*
******************************************************************************
* @file     LibNtc.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

#ifndef _LIB_NTC_H_
#define _LIB_NTC_H_

#ifdef __cplusplus
extern "C" {
#endif
	
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>

	
/* Public define ------------------------------------------------------------*/
	
/* Public typedef -----------------------------------------------------------*/

/* Public macro -------------------------------------------------------------*/

/* Public variables ---------------------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
uint16_t LibTemperatureToVoltage(int16_t i16Temp);
uint16_t LibNtcRToTemperature(double dNtcR);
uint16_t LibNtcVoltageToTemperature(uint16_t u16NtcVoltage);
uint16_t LibSetRealTemperatureToInternalValue(int16_t i16Temp);


#ifdef __cplusplus
}
#endif

#endif /* _LIB_NTC_H_ */
