/*
******************************************************************************
* @file     LibFifoBase.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "LibFifoBase.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
/* Local function prototypes ------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
bool LibFifoIsEmpty(tLibFifoBase *pmFifo)
{
    if (pmFifo -> u16FifoPushInPosi == pmFifo -> u16FifoPopOutPosi)
    {
        return true;
    }
    return false;
}

bool LibFifoIsFull(tLibFifoBase *pmFifo)
{
    uint16_t u16Index = (pmFifo -> u16FifoPushInPosi + 1);
    if (u16Index >= pmFifo -> u16FifoSize)
    {
        u16Index = 0;
    }
    if (u16Index == pmFifo -> u16FifoPopOutPosi)
    {
        return true;
    }
    return false;
}

uint16_t LibFifoGetCount(tLibFifoBase *pmFifo)
{
    uint16_t u16CountSize;
    if (pmFifo -> u16FifoPushInPosi >= pmFifo -> u16FifoPopOutPosi)
    {
        u16CountSize = pmFifo -> u16FifoPushInPosi - pmFifo -> u16FifoPopOutPosi;
    }
    else
    {
        u16CountSize = pmFifo -> u16FifoSize - (pmFifo -> u16FifoPopOutPosi - pmFifo -> u16FifoPushInPosi);
    }
    return u16CountSize;
}

uint16_t LibFifoGetFreeCount(tLibFifoBase *pmFifo)
{
    uint16_t u16FreeCountSize = (pmFifo -> u16FifoSize - LibFifoGetCount(pmFifo) - 1);
    return u16FreeCountSize;
}

int8_t LibFifoGetUsage(tLibFifoBase *pmFifo, uint16_t *pu16Usage)
{
    *pu16Usage = LibFifoGetCount(pmFifo);
    if ((*pu16Usage) == 0)
    {
        return RES_ERROR_RESOURCES;
    }
    return RES_SUCCESS;
}

int8_t LibFifoPush(tLibFifoBase *pmFifo, void *pData)
{
    if (LibFifoIsFull(pmFifo) == true)
    {
        return RES_ERROR_NULL;
    }
    if (pData != NULL)
    {
        void *pDstData = ((uint8_t *) pmFifo -> pmFifoStartAddr) + (pmFifo -> u16FifoPushInPosi * pmFifo -> u16ElementSize);
        LibFifoMemcpy(pData, pDstData, pmFifo -> u16ElementSize);
    }
    pmFifo -> u16FifoPushInPosi++;
    if (pmFifo -> u16FifoPushInPosi >= pmFifo -> u16FifoSize)
    {
        pmFifo -> u16FifoPushInPosi = 0;
    }
    return RES_SUCCESS;
}

int8_t LibFifoPop(tLibFifoBase *pmFifo, void *pData)
{
    if (pData == NULL && LibFifoIsEmpty(pmFifo) == false)
    {
        pmFifo -> u16FifoPopOutPosi++;
        if (pmFifo -> u16FifoPopOutPosi >= pmFifo -> u16FifoSize)
        {
            pmFifo -> u16FifoPopOutPosi = 0;
        }
        return RES_SUCCESS;
    }
    if (LibFifoPeekFront(pmFifo, pData) != RES_SUCCESS)
    {
        return RES_ERROR_RESOURCES;
    }
    pmFifo -> u16FifoPopOutPosi++;
    if (pmFifo -> u16FifoPopOutPosi >= pmFifo -> u16FifoSize)
    {
        pmFifo -> u16FifoPopOutPosi = 0;
    }
    return RES_SUCCESS;
}

void * LibFifoGetFrontPointer(tLibFifoBase *pmFifo)
{
    if (LibFifoIsEmpty(pmFifo) == true)
    {
        return NULL;
    }
    void *pDstData = ((uint8_t *) pmFifo -> pmFifoStartAddr) + (pmFifo -> u16FifoPopOutPosi * pmFifo -> u16ElementSize);
    return pDstData;
}

void * LibFifoGetBackPointer(tLibFifoBase *pmFifo)
{
    if (LibFifoIsEmpty(pmFifo) == true)
    {
        return NULL;
    }
    uint16_t u16LastIndex;
    if (pmFifo -> u16FifoPushInPosi == 0)
    {
        u16LastIndex = pmFifo -> u16FifoSize - 1;
    }
    else
    {
        u16LastIndex = pmFifo -> u16FifoPushInPosi - 1;
    }
    void *pDstData = ((uint8_t *) pmFifo -> pmFifoStartAddr) + (u16LastIndex * pmFifo -> u16ElementSize);
    return pDstData;
}

void * LibFifoGetPushPointer(tLibFifoBase *pmFifo)
{
    if (LibFifoIsFull(pmFifo) == true)
    {
        return NULL;
    }
    void *pDstData = ((uint8_t *) pmFifo -> pmFifoStartAddr) + (pmFifo -> u16FifoPushInPosi * pmFifo -> u16ElementSize);
    return pDstData;
}

int8_t LibFifoPeekFront(tLibFifoBase *pmFifo, void *pData)
{
    if (LibFifoIsEmpty(pmFifo) == true)
    {
        return RES_ERROR_RESOURCES;
    }
    void *pSrcData = LibFifoGetFrontPointer(pmFifo);
    if (pSrcData == NULL)
    {
        return RES_ERROR_RESOURCES;
    }
    LibFifoMemcpy(pSrcData, pData, pmFifo -> u16ElementSize);
    return RES_SUCCESS;
}

int8_t LibFifoPeekBack(tLibFifoBase *pmFifo, void *pData)
{
    if (LibFifoIsEmpty(pmFifo) == true)
    {
        return RES_ERROR_RESOURCES;
    }
    void *pSrcData = LibFifoGetBackPointer(pmFifo);
    if (pSrcData == NULL)
    {
        return RES_ERROR_RESOURCES;
    }
    LibFifoMemcpy(pSrcData, pData, pmFifo -> u16ElementSize);
    return RES_SUCCESS;
}

void LibFifoMemcpy(void *pSrcData, void *pDstData, uint16_t u16ElementSize)
{
#if LIB_FIFO_MEMCPY_VERSION_TARGET == LIB_FIFO_MEMCPY_VERSION_BYTE_TO_BYTE_COPY
    uint8_t *pu8SrcData = (uint8_t *) pSrcData;
    uint8_t *pu8DstData = (uint8_t *) pDstData;
    while (u16ElementSize--)
    {
        *pu8DstData++ = *pu8SrcData++;
    }
#endif
#if LIB_FIFO_MEMCPY_VERSION_TARGET == LIB_FIFO_MEMCPY_VERSION_MEMCPY
    memcpy(pDstData, pSrcData, u16ElementSize);
#endif
#if LIB_FIFO_MEMCPY_VERSION_TARGET == LIB_FIFO_MEMCPY_VERSION_LOOP_ALIGN_PTR
    uintptr_t pSrcPtr, pDstPtr;
    while (u16ElementSize > 0)
    {
        pSrcPtr = (uintptr_t) pSrcData;
        pDstPtr = (uintptr_t) pDstData;
        if (u16ElementSize >= 8 && ((pSrcPtr | pDstPtr) & 0x07) == 0)
        {
            *((uint64_t *) pDstData) = *((uint64_t *) pSrcData);
            pSrcData += 8;
            pDstData += 8;
            u16ElementSize -= 8;
        }
        else if (u16ElementSize >= 4 && ((pSrcPtr | pDstPtr) & 0x03) == 0)
        {
            *((uint32_t *) pDstData) = *((uint32_t *) pSrcData);
            pSrcData += 4;
            pDstData += 4;
            u16ElementSize -= 4;
        }
        else if (u16ElementSize >= 2 && ((pSrcPtr | pDstPtr) & 0x01) == 0)
        {
            *((uint16_t *) pDstData) = *((uint16_t *) pSrcData);
            pSrcData += 2;
            pDstData += 2;
            u16ElementSize -= 2;
        }
        else
        {
            *((uint8_t *) pDstData) = *((uint8_t *) pSrcData);
            pSrcData++;
            pDstData++;
            u16ElementSize--;
        }
    }
#endif
#if LIB_FIFO_MEMCPY_VERSION_TARGET == LIB_FIFO_MEMCPY_VERSION_ALIGN_PTR
    uintptr_t pSrcPtr, pDstPtr;
    pSrcPtr = (uintptr_t) pSrcData;
    pDstPtr = (uintptr_t) pDstData;
    if (((pSrcPtr | pDstPtr) & 0x07) == 0)
    {
        while (u16ElementSize >= 8)
        {
            *((uint64_t *) pDstData) = *((uint64_t *) pSrcData);
            pSrcData += 8;
            pDstData += 8;
            u16ElementSize -= 8;
        }
        pSrcPtr = (uintptr_t) pSrcData;
        pDstPtr = (uintptr_t) pDstData;
    }
    if (((pSrcPtr | pDstPtr) & 0x03) == 0)
    {
        while (u16ElementSize >= 4)
        {
            *((uint32_t *) pDstData) = *((uint32_t *) pSrcData);
            pSrcData += 4;
            pDstData += 4;
            u16ElementSize -= 4;
        }
        pSrcPtr = (uintptr_t) pSrcData;
        pDstPtr = (uintptr_t) pDstData;
    }
    if (((pSrcPtr | pDstPtr) & 0x01) == 0)
    {
        while (u16ElementSize >= 2)
        {
            *((uint16_t *) pDstData) = *((uint16_t *) pSrcData);
            pSrcData += 2;
            pDstData += 2;
            u16ElementSize -= 2;
        }
    }
    while (u16ElementSize >= 1)
    {
        *((uint8_t *) pDstData) = *((uint8_t *) pSrcData);
        pSrcData++;
        pDstData++;
        u16ElementSize--;
    }
#endif
    return;
}