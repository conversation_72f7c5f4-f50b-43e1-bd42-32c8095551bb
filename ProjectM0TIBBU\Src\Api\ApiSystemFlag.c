/*
******************************************************************************
* @file     ApiSystemFlag.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "Main.h"
#include "LibSysPar.h"
#include "HalAfe.h"
#include "LibSoftwareTimerHandler.h"
#include "ApiSystemFlag.h"
#include "ApiProtect.h"
#include "ApiProtectOvp.h"
#include "ApiProtectUvp.h"
#include "ApiProtectCotp.h"
#include "ApiProtectCutp.h"
#include "ApiProtectDotp.h"
#include "ApiProtectDutp.h"
#include "ApiProtectDocp.h"


void AppSerialUartSendMessage(uint8_t *pu8Str);

/* Private define ------------------------------------------------------------*/
#define API_SYS_FLAG_DEBUG_MSG(pu8Str)  AppSerialUartSendMessage((uint8_t *)pu8Str)
#define API_SYS_FLAG_GET_CELL_NUM()     LibSysParGetCellNumber()
#define API_SYS_FLAG_GET_NTC_NUM()      LibSysParGetNtcNumber()
#define API_SYS_FLAG_GET_OVP_FLAG(u16Cells)     ApiProtectOvpGetFlag(u16Cells)
#define API_SYS_FLAG_GET_UVP_FLAG(u16Cells)     ApiProtectUvpGetFlag(u16Cells)
#define API_SYS_FLAG_GET_COTP_FLAG(u16Ntcs)     ApiProtectCotpGetFlag(u16Ntcs)
#define API_SYS_FLAG_GET_CUTP_FLAG(u16Ntcs)     ApiProtectCutpGetFlag(u16Ntcs)
#define API_SYS_FLAG_GET_DOTP_FLAG(u16Ntcs)     ApiProtectDotpGetFlag(u16Ntcs)
#define API_SYS_FLAG_GET_DUTP_FLAG(u16Ntcs)     ApiProtectDutpGetFlag(u16Ntcs)


/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef void (* tfpApiSystemFlagCheckFunTable)(void);
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint16_t	gu16SystemFlagSubIndex = 0;
static uint32_t	gu32SystemFlag1Temp;
static uint32_t	gu32SystemFlag2Temp;
static uint32_t	gu32SystemFlag3Temp;
static uint32_t	gu32SystemFlag4Temp;

static uint8_t 	gu32SystemFlagFunIndex = 0;
static uint32_t	gu32SystemFlag1 = 0;
static uint32_t	gu32SystemFlag2 = 0;
static uint32_t	gu32SystemFlag3 = 0;
static uint32_t	gu32SystemFlag4 = 0;

/* Private function prototypes -----------------------------------------------*/
static void ApiSystemFlagNextFunction(void)
{
	gu16SystemFlagSubIndex = 0;
	gu32SystemFlagFunIndex++;
}
static void ApiSystemCheckFlagNone(void)
{
	ApiSystemFlagNextFunction();
}
static void ApiSystemFlagCheckFinish(void)
{
	gu32SystemFlag1 = gu32SystemFlag1Temp;
	gu32SystemFlag2 = gu32SystemFlag2Temp;
	gu32SystemFlag3 = gu32SystemFlag3Temp;
	gu32SystemFlag4 = gu32SystemFlag4Temp;

	gu32SystemFlagFunIndex = 0;
	gu16SystemFlagSubIndex = 0;
}

static void ApiSystemFlagCheckDocpCocp(void)
{
	uint8_t	u8Flag;
#if 0	
	u8Flag = ApiProtectCocpGetFlag();
	if( (u8Flag & API_PROTECT_FLAG_L1_MASK) >= API_PROTECT_FLAG_L1_SETTED)
	{
		gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_COCP_L1;
	}

	if( (u8Flag & API_PROTECT_FLAG_L2_MASK) >= API_PROTECT_FLAG_L2_SETTED)
	{
		gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_COCP_L2;
	}

	if( (u8Flag & API_PROTECT_FLAG_L3_MASK) >= API_PROTECT_FLAG_L3_SETTED)
	{
		gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_COCP_L3;
	}
#endif			
	u8Flag = ApiProtectDocpGetFlag();
	if( (u8Flag & API_PROTECT_FLAG_L1_MASK) >= API_PROTECT_FLAG_L1_SETTED)
	{
		gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DOCP_L1;
	}

	if( (u8Flag & API_PROTECT_FLAG_L2_MASK) >= API_PROTECT_FLAG_L2_SETTED)
	{
		gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DOCP_L2;
	}

	if( (u8Flag & API_PROTECT_FLAG_L3_MASK) >= API_PROTECT_FLAG_L3_SETTED)
	{
		gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DOCP_L3;
	}
	ApiSystemFlagNextFunction();
}

static void ApiSystemFlagCheckDUTP_L3(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_DUTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L3_MASK) >= API_PROTECT_FLAG_L3_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DUTP_L3;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}


static void ApiSystemFlagCheckDUTP_L2(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_DUTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L2_MASK) >= API_PROTECT_FLAG_L2_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DUTP_L2;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}


static void ApiSystemFlagCheckDUTP_L1(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_DUTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L1_MASK) >= API_PROTECT_FLAG_L1_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DUTP_L1;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}

static void ApiSystemFlagCheckDOTP_L3(void)
{
	uint8_t	flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		flag = API_SYS_FLAG_GET_DOTP_FLAG(gu16SystemFlagSubIndex);
		if( (flag & API_PROTECT_FLAG_L3_MASK) >= API_PROTECT_FLAG_L3_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DOTP_L3;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}


static void ApiSystemFlagCheckDOTP_L2(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_DOTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L2_MASK) >= API_PROTECT_FLAG_L2_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DOTP_L2;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}



static void ApiSystemFlagCheckDOTP_L1(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_DOTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L1_MASK) >= API_PROTECT_FLAG_L1_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_DOTP_L1;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}


static void ApiSystemFlagCheckCUTP_L3(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_CUTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L3_MASK) >= API_PROTECT_FLAG_L3_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_CUTP_L3;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}

static void ApiSystemFlagCheckCUTP_L2(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_CUTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L2_MASK) >= API_PROTECT_FLAG_L2_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_CUTP_L2;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}

static void ApiSystemFlagCheckCUTP_L1(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_CUTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L1_MASK) >= API_PROTECT_FLAG_L1_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_CUTP_L1;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}

static void ApiSystemFlagCheckCOTP_L3(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_COTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L3_MASK) >= API_PROTECT_FLAG_L3_SETTED)
		{
			
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_COTP_L3;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}


static void ApiSystemFlagCheckCOTP_L2(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_COTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L2_MASK) >= API_PROTECT_FLAG_L2_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_COTP_L2;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}


static void ApiSystemFlagCheckCOTP_L1(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_NTC_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_COTP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L1_MASK) >= API_PROTECT_FLAG_L1_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_COTP_L1;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_NTC_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}
static void ApiSystemFlagCheckUVP_L3(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_CELL_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_UVP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L3_MASK) >= API_PROTECT_FLAG_L3_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_UVP_L3;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_CELL_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}
static void ApiSystemFlagCheckUVP_L2(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_CELL_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_UVP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L2_MASK) >= API_PROTECT_FLAG_L2_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_UVP_L2;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_CELL_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}

static void ApiSystemFlagCheckUVP_L1(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_CELL_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_UVP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L1_MASK) >= API_PROTECT_FLAG_L1_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_UVP_L1;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_CELL_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}
static void ApiSystemFlagCheckOVP_L3(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_CELL_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_OVP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L3_MASK) >= API_PROTECT_FLAG_L3_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_OVP_L3;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_CELL_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}

static void ApiSystemFlagCheckOVP_L2(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_CELL_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_OVP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L2_MASK) >= API_PROTECT_FLAG_L2_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_OVP_L2;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_CELL_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}
static void ApiSystemFlagCheckOVP_L1(void)
{
	uint8_t	u8Flag;
	
	for(; gu16SystemFlagSubIndex < API_SYS_FLAG_GET_CELL_NUM(); gu16SystemFlagSubIndex++)
	{
		u8Flag = API_SYS_FLAG_GET_OVP_FLAG(gu16SystemFlagSubIndex);
		if( (u8Flag & API_PROTECT_FLAG_L1_MASK) >= API_PROTECT_FLAG_L1_SETTED)
		{
			gu32SystemFlag1Temp |= API_SYSTEM_FLAG1_OVP_L1;
			ApiSystemFlagNextFunction();
			return;
		}
	}
	if(gu16SystemFlagSubIndex >= API_SYS_FLAG_GET_CELL_NUM())
	{
		ApiSystemFlagNextFunction();
	}
}

static void ApiSystemFlagCheckOtherFlag(void)
{
	if(ApiProtectOvpPfGetFlag())
		gu32SystemFlag2Temp |= API_SYSTEM_FLAG2_OVP_PF;
	if(ApiProtectUvpPfGetFlag())
		gu32SystemFlag2Temp |= API_SYSTEM_FLAG2_UVP_PF;		
    
    
#if 0     
	if(halAfeIsL1Protect())
		SystemFlag2Temp |= SYSTEM_FLAG2_AFE_L1;
	if(halAfeIsL2Protect())
		SystemFlag2Temp |= SYSTEM_FLAG2_AFE_L2;

	if(appProjectIsRtcValid())
		SystemFlag2Temp |= SYSTEM_FLAG2_RTC_VALID;
		
	if(appBmsIsScudIdReady())
		SystemFlag1Temp |= SYSTEM_FLAG1_CANID_READY;

	if(appProjectIsSystemReadyFlag())
		SystemFlag1Temp |= SYSTEM_FLAG1_SYSTEM_READY;
	
	if(appProjectGetRelayOnFlag())
		SystemFlag2Temp |= SYSTEM_FLAG2_RELAY_ON;
	if(appBmsIsMaster())
		SystemFlag2Temp |= SYSTEM_FLAG2_MASTER;
	
		
	if(apiProtectCDvpPfGetFlag())
		SystemFlag2Temp |= SYSTEM_FLAG2_CDVP_PF;		

	if(apiRelayControlGetPreDischargeFailFlagStatus())
		SystemFlag3Temp |= SYSTEM_FLAG3_PRE_CHG_FAIL;

	if(isAfeBridgeFail())
		SystemFlag3Temp |= SYSTEM_FLAG3_AFE_BRIDGE_FAIL;
	
	if(appGaugeGetFullChargeFlag() == true)
		SystemFlag4Temp |= SYSTEM_FLAG4_FULL_CHARGE;

	if(appGaugeGetFullDisChargeFlag() == true)
		SystemFlag4Temp |= SYSTEM_FLAG4_FULL_DISCHARGE;

	if(apiProtectGetCellOwpFlag() == true)
		SystemFlag4Temp |= SYSTEM_FLAG4_CELL_OPENWIRE;
	
	if(apiProtectGetCellOwCnt())
		SystemFlag4Temp |= SYSTEM_FLAG4_CELL_OPENWIRE_WARN;
	
	if(appBalanceIsAnyBalEnable())
		SystemFlag4Temp |= SYSTEM_FLAG4_CELL_BAL_ENABLE;
	
	if(halAfeGetComDir() == LTC_CMD_DIR_NORTH)
		SystemFlag4Temp |= SYSTEM_FLAG4_AFE_DIR;
	
	if(LibSysParGetSystemActiveFlag() & SYS_ACTIVE_FLAG_CELL_BALANCE_BY_MBMS){
		SystemFlag4Temp |= SYSTEM_FLAG4_VOLT_LASTBIT_EQL_BAL_STATE;
	}
#endif
		
	ApiSystemFlagNextFunction();
}


static void ApiSystemFlagIni(void)
{
	gu32SystemFlagFunIndex = 1;
	gu16SystemFlagSubIndex = 0;
	gu32SystemFlag1Temp = 0;
	gu32SystemFlag2Temp = 0;
	gu32SystemFlag3Temp = 0;
	gu32SystemFlag4Temp = 0;
}

const tfpApiSystemFlagCheckFunTable mfpApiSystemFlagCheckFunTable[]={
		ApiSystemCheckFlagNone,
		ApiSystemFlagCheckOVP_L1,
		ApiSystemFlagCheckOVP_L2,
		ApiSystemFlagCheckOVP_L3,
		ApiSystemFlagCheckUVP_L1,
		ApiSystemFlagCheckUVP_L2,
        ApiSystemFlagCheckUVP_L3,
        ApiSystemFlagCheckCOTP_L1,
        ApiSystemFlagCheckCOTP_L2,
        ApiSystemFlagCheckCOTP_L3,
        ApiSystemFlagCheckCUTP_L1,
		ApiSystemFlagCheckCUTP_L2,
		ApiSystemFlagCheckCUTP_L3,
		ApiSystemFlagCheckDOTP_L1,
		ApiSystemFlagCheckDOTP_L2,
		ApiSystemFlagCheckDOTP_L3,
		ApiSystemFlagCheckDUTP_L1,
		ApiSystemFlagCheckDUTP_L2,
		ApiSystemFlagCheckDUTP_L3,
		ApiSystemFlagCheckDocpCocp,
#if 0		
		
		
		
		
		
		
		checkScuOt,
		checkDvp,
		checkDtp,
		checkDip,
#endif				
        ApiSystemFlagCheckOtherFlag,
		ApiSystemFlagCheckFinish
};
	
static void ApiSystemFlagSwTimerHandler(__far void *dest, uint16_t u16Evt, void *vDataPtr)
{
	if(u16Evt & kLIB_SW_TIMER_EVT_1_MS)
	{
		if(gu32SystemFlagFunIndex != 0)
			mfpApiSystemFlagCheckFunTable[gu32SystemFlagFunIndex]();
	}
	if(u16Evt & kLIB_SW_TIMER_EVT_1_S)
	{
		if(gu32SystemFlagFunIndex == 0)
		{
			ApiSystemFlagIni();
		}
	}
}

/* Public function prototypes -----------------------------------------------*/
uint32_t ApiSystemFlagGetFlag1(void)
{
	return gu32SystemFlag1;
}
uint32_t ApiSystemFlagGetFlag2(void)
{
	return gu32SystemFlag2;
}
uint32_t ApiSystemFlagGetFlag3(void)
{
	return gu32SystemFlag3;
}
uint32_t ApiSystemFlagGetFlag4(void)
{
	return gu32SystemFlag4;
}

void ApiSystemFlagOpen(void)
{
  	LibSoftwareTimerHandlerOpen(ApiSystemFlagSwTimerHandler, 0);
}

/************************ (C) COPYRIGHT *****END OF FILE****/    

