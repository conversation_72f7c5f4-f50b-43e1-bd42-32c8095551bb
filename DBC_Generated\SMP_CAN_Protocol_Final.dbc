VERSION ""

NS_ :
        NS_DESC_
        CM_
        BA_DEF_
        BA_
        VAL_
        CAT_DEF_
        CAT_
        FILTER
        BA_DEF_DEF_
        EV_DATA_
        ENVVAR_DATA_
        SGTYPE_
        SGTYPE_VAL_
        BA_DEF_SGTYPE_
        BA_SGTYPE_
        SIG_VALTYPE_
        SIG_TYPE_REF_
        SIGTYPE_VALTYPE_
        BO_TX_BU_
        BA_DEF_REL_
        BA_REL_
        BA_DEF_DEF_REL_
        BU_SG_REL_
        BU_EV_REL_
        BU_BO_REL_
        SG_MUL_VAL_

BS_:

BU_: HOST SCU

BO_ 2147516416 SMP_BASE_SCU_ID: 0 SCU

BO_ 2147517440 SMP_BASE_SYSTEM_FLAG: 8 SCU
 SG_ SystemFlag1 : 0|32@1+ (1,0) [0|4294967295] "" HOST
 SG_ SystemFlag2 : 32|32@1+ (1,0) [0|4294967295] "" HOST

BO_ 2147518464 SMP_BASE_RM_QMAX: 8 SCU
 SG_ RM_Value : 0|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ QMAX_Value : 32|32@1+ (1,0) [0|4294967295] "mAh" HOST

BO_ 2147519488 SMP_BASE_CURRENT: 8 SCU
 SG_ Current1 : 0|32@1- (1,0) [-2147483648|2147483647] "mA" HOST
 SG_ Current2 : 32|32@1- (1,0) [-2147483648|2147483647] "mA" HOST

BO_ 2147520512 SMP_BASE_FCC: 4 SCU
 SG_ FCC_Value : 0|32@1+ (1,0) [0|4294967295] "mAh" HOST

BO_ 2147521536 SMP_BASE_VB: 8 SCU
 SG_ VBat_Voltage : 0|32@1+ (1,0) [0|4294967295] "mV" HOST
 SG_ VPack_Voltage : 32|32@1+ (1,0) [0|4294967295] "mV" HOST

BO_ 2147522560 SMP_BASE_MIN_MAX_VALUE: 8 SCU
 SG_ Min_Cell_Voltage : 0|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ Min_Cell_Position : 16|8@1+ (1,0) [0|255] "" HOST
 SG_ Max_Cell_Voltage : 24|16@1+ (1,0) [0|65535] "mV" HOST
 SG_ Max_Cell_Position : 40|8@1+ (1,0) [0|255] "" HOST

BO_ 2147523584 SMP_BASE_VALID_BMU: 8 SCU
 SG_ Valid_BMU_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2147524608 SMP_BASE_SCU_NTC_VOLTAGE: 8 SCU
 SG_ SCU_NTC_Voltage_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2147525632 SMP_BASE_SCU_NTC_TEMP: 8 SCU
 SG_ SCU_NTC_Temp_Data : 0|64@1+ (1,0) [0|18446744073709551615] "0.1C" HOST

BO_ 2147526656 SMP_BASE_QSTART_RSOC_SOH: 8 SCU
 SG_ QStart : 0|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ RSOC : 32|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ SOH : 48|16@1+ (0.01,0) [0|655.35] "%" HOST

BO_ 2147527680 SMP_BASE_RAMSOC_ENDSOC_DSOC_SOC0: 8 SCU
 SG_ RamSOC : 0|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ EndSOC : 16|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ DSOC : 32|16@1+ (0.01,0) [0|655.35] "%" HOST
 SG_ SOC0 : 48|16@1+ (0.01,0) [0|655.35] "%" HOST

BO_ 2147528704 SMP_BASE_QPASS_RPASS: 8 SCU
 SG_ QPass : 0|32@1+ (1,0) [0|4294967295] "mAh" HOST
 SG_ RPass : 32|32@1+ (1,0) [0|4294967295] "mAh" HOST

BO_ 2147529728 SMP_BASE_CYCLE_COUNT: 2 SCU
 SG_ Cycle_Count : 0|16@1+ (1,0) [0|65535] "Times" HOST

BO_ 2147530752 SMP_BASE_IR_VALUE: 8 SCU
 SG_ IR_Positive : 0|32@1+ (1,0) [0|4294967295] "mOhm" HOST
 SG_ IR_Negative : 32|32@1+ (1,0) [0|4294967295] "mOhm" HOST

BO_ 2147531776 SMP_BASE_ACCPOWER: 8 SCU
 SG_ AccPower_Charge : 0|32@1+ (1,0) [0|4294967295] "mWh" HOST
 SG_ AccPower_Discharge : 32|32@1+ (1,0) [0|4294967295] "mWh" HOST

BO_ 2147532800 SMP_BASE_AFESTATUS: 4 SCU
 SG_ AFE_North_Count : 0|8@1+ (1,0) [0|255] "" HOST
 SG_ AFE_South_Count : 8|8@1+ (1,0) [0|255] "" HOST
 SG_ AFE_Ring_Flag : 16|8@1+ (1,0) [0|255] "" HOST
 SG_ AFE_Total_Number : 24|8@1+ (1,0) [0|255] "" HOST

BO_ 2147533824 SMP_BASE_SCUCURRENTADC: 8 SCU
 SG_ SCU_Current_ADC : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2147534848 SMP_BASE_PCS_BMS_SYSTEM_STATUS: 1 SCU
 SG_ PCS_BMS_Status : 0|8@1+ (1,0) [0|255] "" HOST

BO_ 2147535872 SMP_BASE_EKF: 6 SCU
 SG_ EKF_Data : 0|48@1+ (1,0) [0|281474976710655] "" HOST

BO_ 2147536896 SMP_BASE_EKF_GROUP: 8 SCU
 SG_ EKF_Group_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2147537920 SMP_BASE_DI_TOGGLING_COUNT: 8 SCU
 SG_ DI_Toggling_Count_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2147569664 SMP_BASE_FLOW_METER: 8 SCU
 SG_ Flow_Meter_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2147598336 SMP_BASE_DETAIL_MSG_SEND_END: 0 SCU

BO_ 2281701632 SMP_DETAIL_CELL_VOLTAGE: 8 SCU
 SG_ Cell_Voltage_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281702656 SMP_DETAIL_CELL_NTC_VOLTAGE: 8 SCU
 SG_ Cell_NTC_Voltage_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281703680 SMP_DETAIL_CELL_NTC_TEMP: 8 SCU
 SG_ Cell_NTC_Temp_Data : 0|64@1+ (1,0) [0|18446744073709551615] "0.1C" HOST

BO_ 2281704704 SMP_DETAIL_OVP_FLAG: 8 SCU
 SG_ OVP_Flag_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2281705728 SMP_DETAIL_UVP_FLAG: 8 SCU
 SG_ UVP_Flag_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2281706752 SMP_DETAIL_COTP_FLAG: 8 SCU
 SG_ COTP_Flag_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2281707776 SMP_DETAIL_CUTP_FLAG: 8 SCU
 SG_ CUTP_Flag_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2281708800 SMP_DETAIL_DOTP_FLAG: 8 SCU
 SG_ DOTP_Flag_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2281709824 SMP_DETAIL_DUTP_FLAG: 8 SCU
 SG_ DUTP_Flag_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2281710848 SMP_DETAIL_PACK_VOLTAGE: 8 SCU
 SG_ Pack_Voltage_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281711872 SMP_DETAIL_CELL_BUSBAR_VOLTAGE: 8 SCU
 SG_ Cell_Busbar_Voltage_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281712896 SMP_DETAIL_BUSBAR_NTC_VOLTAGE: 8 SCU
 SG_ Busbar_NTC_Voltage_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281713920 SMP_DETAIL_AMBIENT_NTC_VOLTAGE: 8 SCU
 SG_ Ambient_NTC_Voltage_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281714944 SMP_DETAIL_OTHER_NTC_VOLTAGE: 8 SCU
 SG_ Other_NTC_Voltage_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281715968 SMP_DETAIL_CELL_VOLTAGE_COMPRESS: 8 SCU
 SG_ Cell_Voltage_Compress_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281716992 SMP_DETAIL_NTC_VOLTAGE_COMPRESS: 8 SCU
 SG_ NTC_Voltage_Compress_Data : 0|64@1+ (1,0) [0|18446744073709551615] "mV" HOST

BO_ 2281718016 SMP_DETAIL_EKF_SOC: 8 SCU
 SG_ EKF_SOC_Data : 0|64@1+ (1,0) [0|18446744073709551615] "%" HOST

BO_ 2281719040 SMP_DETAIL_EKF_SOC_COMPRESS: 8 SCU
 SG_ EKF_SOC_Compress_Data : 0|64@1+ (1,0) [0|18446744073709551615] "%" HOST

BO_ 2281720064 SMP_DETAIL_EKF_POV1: 8 SCU
 SG_ EKF_POV1_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2281721088 SMP_DETAIL_EKF_POV2: 8 SCU
 SG_ EKF_POV2_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" HOST

BO_ 2281701376 SMP_CMD_PAR_WR: 8 HOST
 SG_ Parameter_Index_WR : 0|16@1+ (1,0) [0|65535] "" SCU
 SG_ Parameter_Data : 16|48@1+ (1,0) [0|281474976710655] "" SCU

BO_ 2281702400 SMP_CMD_PAR_RD: 8 HOST
 SG_ Parameter_Index_RD : 0|16@1+ (1,0) [0|65535] "" SCU
 SG_ Reserved : 16|48@1+ (1,0) [0|281474976710655] "" SCU

BO_ 2281703424 SMP_CMD_RTC: 8 HOST
 SG_ RTC_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281704448 SMP_CMD_ADC: 8 HOST
 SG_ ADC_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281705472 SMP_CMD_RELAY_ON: 8 HOST
 SG_ Relay_Control_ON : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281706496 SMP_CMD_RELAY_OFF: 8 HOST
 SG_ Relay_Control_OFF : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281707520 SMP_CMD_CHECKSUM: 8 HOST
 SG_ Checksum_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281708544 SMP_CMD_PF_FLAG: 8 HOST
 SG_ PF_Flag_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281709568 SMP_CMD_CLEAN_DATA: 8 HOST
 SG_ Clean_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281710592 SMP_CMD_FAULT_LOG: 8 HOST
 SG_ Fault_Log_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281711616 SMP_CMD_SOC: 8 HOST
 SG_ SOC_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281712640 SMP_CMD_SPIROM: 8 HOST
 SG_ SPIROM_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281713664 SMP_CMD_CALIRLY_CTRL: 8 HOST
 SG_ CALIRLY_CTRL_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281714688 SMP_CMD_MBMSBAL_CTRL: 8 HOST
 SG_ MBMSBAL_CTRL_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281715712 SMP_CMD_SAVE_EKF_STATUS: 8 HOST
 SG_ SAVE_EKF_STATUS_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281717760 SMP_CMD_HWOCP_CALB: 8 HOST
 SG_ HWOCP_CALB_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281725952 SMP_CMD_CANBUS_PROTOCOL_VER: 8 HOST
 SG_ CANBUS_PROTOCOL_VER_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2281726976 SMP_CMD_OPEN_WIRE_FLAG: 8 HOST
 SG_ OPEN_WIRE_FLAG_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2415919104 SMP_COMMON_FIND_FIRST_SCU: 8 HOST
 SG_ Find_SCU_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2415920128 SMP_COMMON_RESET_SCU_ID: 8 HOST
 SG_ Reset_SCU_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2415921152 SMP_COMMON_GET_SCUID: 8 HOST
 SG_ Get_SCU_ID_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2415922176 SMP_COMMON_STOP_ID_ASSIGN: 8 HOST
 SG_ Stop_ID_Assign_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2415923200 SMP_COMMON_EMS_RELAY_CTRL: 8 HOST
 SG_ EMS_Relay_Ctrl_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

BO_ 2415924224 SMP_COMMON_PC_SCUDATA_REQUEST: 8 HOST
 SG_ PC_SCUDATA_Request_Data : 0|64@1+ (1,0) [0|18446744073709551615] "" SCU

CM_ BO_ 2147516416 "SCU ID message";
CM_ BO_ 2147517440 "System flags and status information";
CM_ BO_ 2147518464 "Remaining capacity and maximum capacity";
CM_ BO_ 2147519488 "Current measurements from multiple sensors";
CM_ BO_ 2147520512 "Full charge capacity";
CM_ BO_ 2147521536 "Battery and pack voltage measurements";
CM_ BO_ 2147522560 "Minimum and maximum cell voltage with positions";
CM_ BO_ 2147523584 "Valid BMU information";
CM_ BO_ 2147524608 "SCU NTC voltage measurements";
CM_ BO_ 2147525632 "SCU NTC temperature measurements";
CM_ BO_ 2147526656 "QStart, RSOC and SOH values";
CM_ BO_ 2147527680 "Various SOC calculations";
CM_ BO_ 2147528704 "QPass and RPass values";
CM_ BO_ 2147529728 "Battery cycle count";
CM_ BO_ 2147530752 "Internal resistance values";
CM_ BO_ 2147531776 "Accumulated power values";
CM_ BO_ 2147532800 "AFE status information";
CM_ BO_ 2147533824 "SCU current ADC raw values";
CM_ BO_ 2147534848 "PCS BMS system status";
CM_ BO_ 2147535872 "EKF data";
CM_ BO_ 2147536896 "EKF group data";
CM_ BO_ 2147537920 "DI toggling count data";
CM_ BO_ 2147569664 "Flow meter data";
CM_ BO_ 2147598336 "Detail message send end marker";

BA_DEF_ BO_ "VFrameFormat" ENUM "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ BO_ "GenMsgCycleTime" INT 0 3600000;
BA_DEF_ BO_ "GenMsgSendType" ENUM "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType";
BA_DEF_ SG_ "GenSigStartValue" INT 0 0;
BA_DEF_ "DBName" STRING ;

BA_DEF_DEF_ "VFrameFormat" "";
BA_DEF_DEF_ "GenMsgCycleTime" 250;
BA_DEF_DEF_ "GenMsgSendType" "Cyclic";
BA_DEF_DEF_ "GenSigStartValue" 0;
BA_DEF_DEF_ "DBName" "";

BA_ "DBName" "SMP_CAN_Protocol_Complete";

BA_ "VFrameFormat" BO_ 2147516416 1;
BA_ "GenMsgCycleTime" BO_ 2147516416 0;
BA_ "VFrameFormat" BO_ 2147517440 1;
BA_ "GenMsgCycleTime" BO_ 2147517440 100;
BA_ "VFrameFormat" BO_ 2147518464 1;
BA_ "GenMsgCycleTime" BO_ 2147518464 1000;
BA_ "VFrameFormat" BO_ 2147519488 1;
BA_ "GenMsgCycleTime" BO_ 2147519488 100;
BA_ "VFrameFormat" BO_ 2147520512 1;
BA_ "GenMsgCycleTime" BO_ 2147520512 1000;
BA_ "VFrameFormat" BO_ 2147521536 1;
BA_ "GenMsgCycleTime" BO_ 2147521536 100;
BA_ "VFrameFormat" BO_ 2147522560 1;
BA_ "GenMsgCycleTime" BO_ 2147522560 100;
BA_ "VFrameFormat" BO_ 2147523584 1;
BA_ "GenMsgCycleTime" BO_ 2147523584 1000;
BA_ "VFrameFormat" BO_ 2147524608 1;
BA_ "GenMsgCycleTime" BO_ 2147524608 1000;
BA_ "VFrameFormat" BO_ 2147525632 1;
BA_ "GenMsgCycleTime" BO_ 2147525632 1000;
BA_ "VFrameFormat" BO_ 2147526656 1;
BA_ "GenMsgCycleTime" BO_ 2147526656 100;
BA_ "VFrameFormat" BO_ 2147527680 1;
BA_ "GenMsgCycleTime" BO_ 2147527680 100;
BA_ "VFrameFormat" BO_ 2147528704 1;
BA_ "GenMsgCycleTime" BO_ 2147528704 1000;
BA_ "VFrameFormat" BO_ 2147529728 1;
BA_ "GenMsgCycleTime" BO_ 2147529728 10000;
BA_ "VFrameFormat" BO_ 2147530752 1;
BA_ "GenMsgCycleTime" BO_ 2147530752 1000;
BA_ "VFrameFormat" BO_ 2147531776 1;
BA_ "GenMsgCycleTime" BO_ 2147531776 1000;
BA_ "VFrameFormat" BO_ 2147532800 1;
BA_ "GenMsgCycleTime" BO_ 2147532800 1000;
BA_ "VFrameFormat" BO_ 2147533824 1;
BA_ "GenMsgCycleTime" BO_ 2147533824 100;
BA_ "VFrameFormat" BO_ 2147534848 1;
BA_ "GenMsgCycleTime" BO_ 2147534848 100;
BA_ "VFrameFormat" BO_ 2147535872 1;
BA_ "GenMsgCycleTime" BO_ 2147535872 1000;
BA_ "VFrameFormat" BO_ 2147536896 1;
BA_ "GenMsgCycleTime" BO_ 2147536896 1000;
BA_ "VFrameFormat" BO_ 2147537920 1;
BA_ "GenMsgCycleTime" BO_ 2147537920 1000;
BA_ "VFrameFormat" BO_ 2147569664 1;
BA_ "GenMsgCycleTime" BO_ 2147569664 1000;
BA_ "VFrameFormat" BO_ 2147598336 1;
BA_ "GenMsgCycleTime" BO_ 2147598336 0;
BA_ "VFrameFormat" BO_ 2281701632 1;
BA_ "VFrameFormat" BO_ 2281702656 1;
BA_ "VFrameFormat" BO_ 2281703680 1;
BA_ "VFrameFormat" BO_ 2281704704 1;
BA_ "VFrameFormat" BO_ 2281705728 1;
BA_ "VFrameFormat" BO_ 2281706752 1;
BA_ "VFrameFormat" BO_ 2281707776 1;
BA_ "VFrameFormat" BO_ 2281708800 1;
BA_ "VFrameFormat" BO_ 2281709824 1;
BA_ "VFrameFormat" BO_ 2281710848 1;
BA_ "VFrameFormat" BO_ 2281711872 1;
BA_ "VFrameFormat" BO_ 2281712896 1;
BA_ "VFrameFormat" BO_ 2281713920 1;
BA_ "VFrameFormat" BO_ 2281714944 1;
BA_ "VFrameFormat" BO_ 2281715968 1;
BA_ "VFrameFormat" BO_ 2281716992 1;
BA_ "VFrameFormat" BO_ 2281718016 1;
BA_ "VFrameFormat" BO_ 2281719040 1;
BA_ "VFrameFormat" BO_ 2281720064 1;
BA_ "VFrameFormat" BO_ 2281721088 1;
BA_ "VFrameFormat" BO_ 2281701376 1;
BA_ "GenMsgCycleTime" BO_ 2281701376 0;
BA_ "VFrameFormat" BO_ 2281702400 1;
BA_ "GenMsgCycleTime" BO_ 2281702400 0;
BA_ "VFrameFormat" BO_ 2281703424 1;
BA_ "GenMsgCycleTime" BO_ 2281703424 0;
BA_ "VFrameFormat" BO_ 2281704448 1;
BA_ "GenMsgCycleTime" BO_ 2281704448 0;
BA_ "VFrameFormat" BO_ 2281705472 1;
BA_ "GenMsgCycleTime" BO_ 2281705472 0;
BA_ "VFrameFormat" BO_ 2281706496 1;
BA_ "GenMsgCycleTime" BO_ 2281706496 0;
BA_ "VFrameFormat" BO_ 2281707520 1;
BA_ "GenMsgCycleTime" BO_ 2281707520 0;
BA_ "VFrameFormat" BO_ 2281708544 1;
BA_ "GenMsgCycleTime" BO_ 2281708544 0;
BA_ "VFrameFormat" BO_ 2281709568 1;
BA_ "GenMsgCycleTime" BO_ 2281709568 0;
BA_ "VFrameFormat" BO_ 2281710592 1;
BA_ "GenMsgCycleTime" BO_ 2281710592 0;
BA_ "VFrameFormat" BO_ 2281711616 1;
BA_ "GenMsgCycleTime" BO_ 2281711616 0;
BA_ "VFrameFormat" BO_ 2281712640 1;
BA_ "GenMsgCycleTime" BO_ 2281712640 0;
BA_ "VFrameFormat" BO_ 2281713664 1;
BA_ "GenMsgCycleTime" BO_ 2281713664 0;
BA_ "VFrameFormat" BO_ 2281714688 1;
BA_ "GenMsgCycleTime" BO_ 2281714688 0;
BA_ "VFrameFormat" BO_ 2281715712 1;
BA_ "GenMsgCycleTime" BO_ 2281715712 0;
BA_ "VFrameFormat" BO_ 2281717760 1;
BA_ "GenMsgCycleTime" BO_ 2281717760 0;
BA_ "VFrameFormat" BO_ 2281725952 1;
BA_ "GenMsgCycleTime" BO_ 2281725952 0;
BA_ "VFrameFormat" BO_ 2281726976 1;
BA_ "GenMsgCycleTime" BO_ 2281726976 0;
BA_ "VFrameFormat" BO_ 2415919104 1;
BA_ "GenMsgCycleTime" BO_ 2415919104 0;
BA_ "VFrameFormat" BO_ 2415920128 1;
BA_ "GenMsgCycleTime" BO_ 2415920128 0;
BA_ "VFrameFormat" BO_ 2415921152 1;
BA_ "GenMsgCycleTime" BO_ 2415921152 0;
BA_ "VFrameFormat" BO_ 2415922176 1;
BA_ "GenMsgCycleTime" BO_ 2415922176 0;
BA_ "VFrameFormat" BO_ 2415923200 1;
BA_ "GenMsgCycleTime" BO_ 2415923200 0;
BA_ "VFrameFormat" BO_ 2415924224 1;
BA_ "GenMsgCycleTime" BO_ 2415924224 0;

VAL_ 2147517440 SystemFlag1 31 "SYSTEM_READY" 30 "CANID_READY" 29 "DOCP_LATCH" 28 "DOCP_L3" 27 "DOCP_L2" 26 "DOCP_L1" 25 "COCP_LATCH" 24 "COCP_L3" 23 "COCP_L2" 22 "COCP_L1" 21 "OT_2nd" 20 "UT_2nd" 19 "DUTP_L3" 18 "DUTP_L2" 17 "DUTP_L1" 16 "DOTP_L3" 15 "DOTP_L2" 14 "DOTP_L1" 13 "CUTP_L3" 12 "CUTP_L2" 11 "CUTP_L1" 10 "COTP_L3" 9 "COTP_L2" 8 "COTP_L1" 7 "UVP_2nd" 6 "UVP_L3" 5 "UVP_L2" 4 "UVP_L1" 3 "OVP_2nd" 2 "OVP_L3" 1 "OVP_L2" 0 "OVP_L1" ;
VAL_ 2147517440 SystemFlag2 31 "MDVP_PF" 30 "CDVP_PF" 29 "RN_URP_L3" 28 "RN_URP_L2" 27 "RN_URP_L1" 26 "RP_URP_L3" 25 "RP_URP_L2" 24 "RP_URP_L1" 23 "AFE_INI_STATE" 22 "ENG_MODE" 21 "NFAULT" 20 "OD_IN" 19 "DI2" 18 "DI1" 17 "SP_FB" 16 "K4" 15 "K3" 14 "K2" 13 "K1" 12 "PS3_FAIL" 11 "PS2_FAIL" 10 "PS1_FAIL" 9 "RTC_VALID" 8 "RELAY_ON" 7 "MASTER" 6 "M_RELAY_FAIL" 5 "P_RELAY_FAIL" 4 "UVP_PF" 3 "OVP_PF" 2 "EPO_ENABLE" 1 "AFE_L2" 0 "AFE_L1" ;
VAL_ 2281701376 Parameter_Index_WR 255 "PACK_SN" 254 "FW_INT_VER" 253 "SCU_ID" 252 "CELL_NUM" 251 "NTC_NUM" 250 "CELL_BUSBAR_NUM" 249 "NTC_BUSBAR_NUM" 248 "NTC_AMBIENT_NUM" 247 "NTC_OTHER_NUM" 246 "SYS_ACTIVE_FLAG" 245 "BAL_ENABLE" 244 "BAL_V_DIFF" 243 "BAL_V_MIN" 242 "BAL_I_MAX" 241 "BAL_T_MAX" 240 "BAL_PAUSE_T" 239 "BAL_TEMP_MAX" 238 "BAL_TEMP_MIN" 237 "BAL_SOC_MAX" 236 "BAL_SOC_MIN" 235 "BAL_V_MAX" 234 "BAL_V_DIFF_MAX" 233 "BAL_V_DIFF_MIN" 232 "BAL_I_MIN" 231 "BAL_T_MIN" 230 "BAL_PAUSE_T_MIN" 160 "OVP_PROTECT" 159 "UVP_PROTECT" 158 "COTP_PROTECT" 157 "CUTP_PROTECT" 156 "DOTP_PROTECT" 155 "DUTP_PROTECT" 154 "DTP_PROTECT" 153 "DOCP_PROTECT" 152 "COCP_PROTECT" 151 "MOSOT_PROTECT" 150 "SOC_PROTECT" 149 "PF_PROTECT" 148 "DVP_PROTECT" 147 "CURRENT_DIFF" 146 "DVB_PROTECT" 145 "SCU_TEMP_PROTECT" 50 "CELL_CALI_CURR1" 49 "CELL_CALI_CURR2" 48 "VBAT_CALI" 47 "VPACK_CALI" 46 "VBCAL_CALI" 45 "SCU_NTC_CALI" 44 "BUSBAR_NTC_CALI" 43 "AMBIENT_NTC_CALI" 42 "OTHER_NTC_CALI" 41 "CELL_CALI_OFFSET" 40 "CELL_CALI_GAIN" 39 "NTC_CALI_OFFSET" 38 "NTC_CALI_GAIN" 37 "CURRENT_CALI_OFFSET" 36 "CURRENT_CALI_GAIN" 35 "VOLTAGE_CALI_OFFSET" 34 "VOLTAGE_CALI_GAIN" 33 "TEMP_CALI_OFFSET" 32 "TEMP_CALI_GAIN" 31 "GAUGE_DESIGN_CAP" 30 "GAUGE_QMAX" 29 "GAUGE_RSOC" 28 "GAUGE_FCC" 27 "GAUGE_RM" 26 "GAUGE_CYCLE_COUNT" 25 "GAUGE_SOH" 24 "GAUGE_IR_POS" 23 "GAUGE_IR_NEG" 22 "GAUGE_TEMP_COEFF" 21 "GAUGE_AGING_FACTOR" 20 "GAUGE_SELF_DISCHARGE" 19 "GAUGE_CHARGE_EFF" 18 "GAUGE_DISCHARGE_EFF" 17 "GAUGE_RELAX_TIME" 16 "GAUGE_UPDATE_TIME" 15 "GAUGE_LEARN_CAP" 14 "GAUGE_LEARN_CYCLE" 13 "GAUGE_LEARN_FLAG" 12 "NOTE_MSG" 11 "PACK_SN_EXT" 10 "BAUDRATE" 9 "CELL_TYPE" 8 "PART_NUM" 7 "BMU_TYPE" 6 "MODEL_NAME" 5 "MID" 4 "FW_BUILD_DT" 3 "FW_VER" 2 "HW_VER" 1 "MAGIC_CODE" 0 "RESERVED" ;
VAL_ 2281702400 Parameter_Index_RD 255 "PACK_SN" 254 "FW_INT_VER" 253 "SCU_ID" 252 "CELL_NUM" 251 "NTC_NUM" 250 "CELL_BUSBAR_NUM" 249 "NTC_BUSBAR_NUM" 248 "NTC_AMBIENT_NUM" 247 "NTC_OTHER_NUM" 246 "SYS_ACTIVE_FLAG" 245 "BAL_ENABLE" 244 "BAL_V_DIFF" 243 "BAL_V_MIN" 242 "BAL_I_MAX" 241 "BAL_T_MAX" 240 "BAL_PAUSE_T" 239 "BAL_TEMP_MAX" 238 "BAL_TEMP_MIN" 237 "BAL_SOC_MAX" 236 "BAL_SOC_MIN" 235 "BAL_V_MAX" 234 "BAL_V_DIFF_MAX" 233 "BAL_V_DIFF_MIN" 232 "BAL_I_MIN" 231 "BAL_T_MIN" 230 "BAL_PAUSE_T_MIN" 160 "OVP_PROTECT" 159 "UVP_PROTECT" 158 "COTP_PROTECT" 157 "CUTP_PROTECT" 156 "DOTP_PROTECT" 155 "DUTP_PROTECT" 154 "DTP_PROTECT" 153 "DOCP_PROTECT" 152 "COCP_PROTECT" 151 "MOSOT_PROTECT" 150 "SOC_PROTECT" 149 "PF_PROTECT" 148 "DVP_PROTECT" 147 "CURRENT_DIFF" 146 "DVB_PROTECT" 145 "SCU_TEMP_PROTECT" 50 "CELL_CALI_CURR1" 49 "CELL_CALI_CURR2" 48 "VBAT_CALI" 47 "VPACK_CALI" 46 "VBCAL_CALI" 45 "SCU_NTC_CALI" 44 "BUSBAR_NTC_CALI" 43 "AMBIENT_NTC_CALI" 42 "OTHER_NTC_CALI" 41 "CELL_CALI_OFFSET" 40 "CELL_CALI_GAIN" 39 "NTC_CALI_OFFSET" 38 "NTC_CALI_GAIN" 37 "CURRENT_CALI_OFFSET" 36 "CURRENT_CALI_GAIN" 35 "VOLTAGE_CALI_OFFSET" 34 "VOLTAGE_CALI_GAIN" 33 "TEMP_CALI_OFFSET" 32 "TEMP_CALI_GAIN" 31 "GAUGE_DESIGN_CAP" 30 "GAUGE_QMAX" 29 "GAUGE_RSOC" 28 "GAUGE_FCC" 27 "GAUGE_RM" 26 "GAUGE_CYCLE_COUNT" 25 "GAUGE_SOH" 24 "GAUGE_IR_POS" 23 "GAUGE_IR_NEG" 22 "GAUGE_TEMP_COEFF" 21 "GAUGE_AGING_FACTOR" 20 "GAUGE_SELF_DISCHARGE" 19 "GAUGE_CHARGE_EFF" 18 "GAUGE_DISCHARGE_EFF" 17 "GAUGE_RELAX_TIME" 16 "GAUGE_UPDATE_TIME" 15 "GAUGE_LEARN_CAP" 14 "GAUGE_LEARN_CYCLE" 13 "GAUGE_LEARN_FLAG" 12 "NOTE_MSG" 11 "PACK_SN_EXT" 10 "BAUDRATE" 9 "CELL_TYPE" 8 "PART_NUM" 7 "BMU_TYPE" 6 "MODEL_NAME" 5 "MID" 4 "FW_BUILD_DT" 3 "FW_VER" 2 "HW_VER" 1 "MAGIC_CODE" 0 "RESERVED" ;
