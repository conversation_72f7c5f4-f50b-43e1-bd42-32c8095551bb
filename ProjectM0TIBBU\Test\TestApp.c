/*
******************************************************************************
* @file     TestApp.c
* <AUTHOR>
* @brief    This file is TestApp function

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "Main.h"
#include "HalUart.h"
#include "Bsp.h" 
#include "LibUartFifo.h"
#include "LibSoftwareTimerHandler.h"
#include "LibHardwareTimerHandler.h"
#include "HalMcuPeripheralConfig.h"
#include "HalGeneralTimer.h"
#include "HalAfe.h"
#include "HalEeProm.h"
#include "ApiProtect.h"
#include "ApiProtectEvent.h"
#include "TestHalRtc.h"
#include "TestHalAdc.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
#define	TEST_APP_PROJECT_DEBUG_MSG             (msg)
#define TEST_APP_HARDWARE_TIMER_TASK_PERIOD    (1000)

#define	UART_TX_BUF_SIZE	                   500
#define	UART_RX_BUF_SIZE	                   500

/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/

/* Global variables ---------------------------------------------------------*/
static uint8_t gu8Uart0TxFifoTest[UART_TX_BUF_SIZE];
static uint8_t gu8Uart0RxFifoTest[UART_RX_BUF_SIZE];
static tHalUartConfig gmHalUart0Config;
/* Local function prototypes ------------------------------------------------*/
void TestAppSerialUartBytese(uint8_t *u8Packet, uint16_t u16Len);
void TestAppSerialUartSendMessage(uint8_t *pu8Str);
static void TeatAppHalUartCbExample(eTypeHalUartEvt eUartEvtt);
/* Global function prototypes -----------------------------------------------*/

int fputc(int ch, FILE *f)
{
    uint8_t u8data = (uint8_t)ch;
    HalUartPut(&gmHalUart0Config, u8data);
    return ch;
}

//Test App
//---------------------------------------------------------
static void TestAppTemporaryHalTimerHandler(void *pvDest, uint16_t u16Event, void *pvData)
{
    LibSwTimerHardwareTimerCounter(kLIB_SW_TIMER_EVT_HW_1MS, 0);
    LibHardwareTimerHandler();
}

void TestAppSerialUartBytese(uint8_t *u8Packet, uint16_t u16Len)
{
	uint16_t i = 0;

	for(i = 0 ; i < u16Len; i++)
    {
  	    HalUartPut(&gmHalUart0Config, u8Packet[i]);
    }
}

void TestAppSerialUartSendMessage(uint8_t *pu8Str)
{
	uint8_t		i;
	uint8_t     u8Len = 0;

	u8Len = strlen((char *)pu8Str);	
	for(i = 0 ; i < u8Len ; i++)
    {
  	    HalUartPut(&gmHalUart0Config, pu8Str[i]);
    }
}

void TestAppSerialUartOpen(void)
{
    //Config Uart 0
    //------------------------------------------------
	gmHalUart0Config.pmUartChannel = BSP_UART_0;
    gmHalUart0Config.mUartFifoConfig.pu8HalUartTxFifo = gu8Uart0TxFifoTest;
    gmHalUart0Config.mUartFifoConfig.u16HalUartTxFifoSize = UART_TX_BUF_SIZE;
    gmHalUart0Config.mUartFifoConfig.pu8HalUartRxFifo = gu8Uart0RxFifoTest;
    gmHalUart0Config.mUartFifoConfig.u16HalUartRxFifoSize = UART_RX_BUF_SIZE;
    gmHalUart0Config.fpUartEvent = TeatAppHalUartCbExample;
    gmHalUart0Config.eBaud = kHAL_UART_BAUD_115200;
    gmHalUart0Config.eMode = kHAL_UART_MAIN_MODE_NORMAL;
    gmHalUart0Config.eDirection = kHAL_UART_MAIN_DIRECTION_TX_RX;
    gmHalUart0Config.eFlowControl = kHAL_UART_MAIN_FLOW_CONTROL_NONE;
    gmHalUart0Config.eParity = kHAL_UART_MAIN_PARITY_NONE;
    gmHalUart0Config.eWordLength = kHAL_UART_MAIN_WORD_LENGTH_8_BITS;
    gmHalUart0Config.eStopBits = kHAL_UART_MAIN_STOP_BITS_ONE;
    gmHalUart0Config.eOversamplingRate = kHAL_UART_MAIN_OVERSAMPLING_RATE_16X;
    
    HalUartInit(&gmHalUart0Config);
    //------------------------------------------------
}

void TestApp(void)
{
    char c8Str[100];

    HalMcuPeripheralInit(); 
	TestAppSerialUartOpen();
    
    HalGeneralTimerLowPowerOpenAndStart(TEST_APP_HARDWARE_TIMER_TASK_PERIOD, TestAppTemporaryHalTimerHandler);

	sprintf(c8Str,"Test App...\r\n");
	TestAppSerialUartSendMessage((uint8_t *)c8Str);

    //Test HalRtc
    //-----------------------------------
    #if 0
    TestHalRtcApp();
    #endif
    //-----------------------------------

    //Test HalAdc
    //-----------------------------------
    #if 1
    TestHalAdcApp();
    #endif
    //-----------------------------------    
}

static void TeatAppHalUartCbExample(eTypeHalUartEvt eUartEvtt)
{

}
