/*
******************************************************************************
* @file     ApiProtectDutp.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "Define.h"
#include "HalAfe.h"
#include "ApiProtect.h"
#include "LibSysPar.h"

void AppSerialUartSendMessage(char *str);

/* Private define ------------------------------------------------------------*/
#define	API_PROTECT_DUTP_DEBUG_MSG(pc8Msg)		AppSerialUartSendMessage(pc8Msg)
#define	API_PROTECT_DUTP_CHECK_NUM_PER_TIME	    (20)
#define	API_PROTECT_DUTP_GET_NTC_NUMBER()		LibSysParGetNtcNumber()
#define API_PROTECT_DUTP_GET_NTC_VOLTAGE(u16Ntcs)      HalAfeGetNtcVoltage(u16Ntcs);

#define	API_PROTECT_DUTP_GET_DUTP_PAR(u8ProtectLevel, mProtectPar) 		LibSysParGetDutpProtectPar(u8ProtectLevel, mProtectPar)
#define	API_PROTECT_DUTP_GET_LEVEL_MASK(u8ProtectLevel, mProtectFlagValue)	ApiProtectGetLevelMask(u8ProtectLevel, mProtectFlagValue)
#define API_PROTECT_DUTP_IS_OT(u16u16NtcVoltage, u32ParValue)  ApiProtectIsOverTemperter(u16u16NtcVoltage, u32ParValue)
#define API_PROTECT_DUTP_IS_UT(u16u16NtcVoltage, u32ParValue)  ApiProtectIsUnderTemperter(u16u16NtcVoltage, u32ParValue)
/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t	u8Flag[MAX_NTC_NUMBER];
	uint8_t	u8SetCount[API_PROTECT_LEVEL][MAX_NTC_NUMBER];
	uint8_t	u8ReleaseCount[API_PROTECT_LEVEL][MAX_NTC_NUMBER];
	tfpApiProtectEvtHandler  fpEvtHandler;
}tDutpProtect;

static tDutpProtect	gmDutpProtect={0};
static uint16_t	gu16DutpNtcIndex = 0;
static bool gbDutpEnable = 0;
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static void ApiProtectDutpProtectIni(void)
{
	gu16DutpNtcIndex = 0;
}
/* Private function prototypes -----------------------------------------------*/

/* Public function prototypes -----------------------------------------------*/
uint8_t	ApiProtectDutpGetFlag(uint16_t u16NtcIndex)
{
	return gmDutpProtect.u8Flag[u16NtcIndex];
}


uint8_t ApiProtectDutpHandler(uint8_t u8ProtectLevel)
{
	uint8_t	u8checkcount = 0;

	uint16_t		u16NtcVoltage;
	tProtectFlagValue	mProtectFlagValue;
	tScuProtectPar		mProtectPar;
	
	if (gbDutpEnable == 0)
	{
		return 1;
	}

	API_PROTECT_DUTP_GET_DUTP_PAR(u8ProtectLevel, &mProtectPar);
	API_PROTECT_DUTP_GET_LEVEL_MASK(u8ProtectLevel, &mProtectFlagValue);

	while(1)
	{			
		u16NtcVoltage = API_PROTECT_DUTP_GET_NTC_VOLTAGE(gu16DutpNtcIndex);
		if(u16NtcVoltage > NTC_ADC_UPPER_BOUND)
		{
			gmDutpProtect.u8Flag[gu16DutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
			gmDutpProtect.u8ReleaseCount[u8ProtectLevel][gu16DutpNtcIndex] = 0;
			gmDutpProtect.u8SetCount[u8ProtectLevel][gu16DutpNtcIndex] = 0;
			goto compareNext;
		}
		
		if((mProtectPar.mSTime.u32Value != 0) &&
		   (API_PROTECT_DUTP_IS_UT(u16NtcVoltage, mProtectPar.mSetValue.u32Value) != 0)) 
		{
			if((gmDutpProtect.u8Flag[gu16DutpNtcIndex] & mProtectFlagValue.u8Mask) == 0)
			{
				gmDutpProtect.u8Flag[gu16DutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
				gmDutpProtect.u8Flag[gu16DutpNtcIndex] |= mProtectFlagValue.u8Setting;
				gmDutpProtect.u8SetCount[u8ProtectLevel][gu16DutpNtcIndex] = 1;
			}	
			else if((gmDutpProtect.u8Flag[gu16DutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
			{
				gmDutpProtect.u8SetCount[u8ProtectLevel][gu16DutpNtcIndex]++;
				if(gmDutpProtect.u8SetCount[u8ProtectLevel][gu16DutpNtcIndex] >= mProtectPar.mSTime.u32Value)
				{
					gmDutpProtect.u8Flag[gu16DutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
					gmDutpProtect.u8Flag[gu16DutpNtcIndex] |= mProtectFlagValue.u8Setted;
					gmDutpProtect.u8SetCount[u8ProtectLevel][gu16DutpNtcIndex] = 0;

					if(gmDutpProtect.fpEvtHandler)
					{
						gmDutpProtect.fpEvtHandler(0, kAPI_PROTECT_DUTP_L1_SET + u8ProtectLevel, &gu16DutpNtcIndex);
					}
				}
			}
		}
		else if((gmDutpProtect.u8Flag[gu16DutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
		{
			gmDutpProtect.u8Flag[gu16DutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
		}
		//--------------------------------------------------------------------------
		//	Level	Release
		if((mProtectPar.mRTime.u32Value != 0) &&
		   API_PROTECT_DUTP_IS_OT(u16NtcVoltage, mProtectPar.mRelValue.u32Value) != 0)
		{
			if((gmDutpProtect.u8Flag[gu16DutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setted)
			{
				gmDutpProtect.u8Flag[gu16DutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
				gmDutpProtect.u8Flag[gu16DutpNtcIndex] |= mProtectFlagValue.u8Releasing;
				gmDutpProtect.u8ReleaseCount[u8ProtectLevel][gu16DutpNtcIndex] = 1;
			}	
			else if((gmDutpProtect.u8Flag[gu16DutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
			{
				gmDutpProtect.u8ReleaseCount[u8ProtectLevel][gu16DutpNtcIndex]++;
				if(gmDutpProtect.u8ReleaseCount[u8ProtectLevel][gu16DutpNtcIndex] >= mProtectPar.mRTime.u32Value)
				{
					gmDutpProtect.u8Flag[gu16DutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
					gmDutpProtect.u8ReleaseCount[u8ProtectLevel][gu16DutpNtcIndex] = 0;
					if(gmDutpProtect.fpEvtHandler)
					{
						gmDutpProtect.fpEvtHandler(0, kAPI_PROTECT_DUTP_L1_RELEASE + u8ProtectLevel, &gu16DutpNtcIndex);
					}
				}
			}
		}
		else if((gmDutpProtect.u8Flag[gu16DutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
		{
			gmDutpProtect.u8Flag[gu16DutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
			gmDutpProtect.u8Flag[gu16DutpNtcIndex] |= mProtectFlagValue.u8Setted;
		}
	
compareNext:
		gu16DutpNtcIndex++;
		if(gu16DutpNtcIndex >= API_PROTECT_DUTP_GET_NTC_NUMBER())
		{
			gu16DutpNtcIndex = 0;
			return 1;
		}
		u8checkcount++;
		if(u8checkcount >= API_PROTECT_DUTP_CHECK_NUM_PER_TIME)
			break;
	}
	return 0;
}

void ApiProtectDutpOpen(tfpApiProtectEvtHandler fpEvtHandler)
{
	ApiProtectDutpProtectIni();
	
	gbDutpEnable = 1;
	
	gmDutpProtect.fpEvtHandler = fpEvtHandler;
}

/************************ (C) COPYRIGHT *****END OF FILE****/    

