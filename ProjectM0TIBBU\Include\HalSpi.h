/*
******************************************************************************
* @file     Hal<PERSON><PERSON>.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_SPI_H__
#define __HAL_SPI_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include <ti/devices/msp/m0p/mspm0g351x.h>

#include "Main.h"
#include "Bsp.h"
#include "Define.h"
#include "Feature.h"
#include "LibFunctionReturnValueDefine.h"
#include "LibSoftwareTimerHandler.h"
/* Global define ------------------------------------------------------------*/
//#define HAL_SPI_ENABLE_SPI_EXAMPLE
//#define HAL_SPI_ENABLE_UART

#define HAL_SPI_POWER_STARTUP_DELAY         (16)
#define HAL_SPI_FREQ_MCLK                   (80000000)
#define HAL_SPI_FREQ_MFCLK                  (4000000)
#define HAL_SPI_FREQ_LFCLK                  (32768)
#define HAL_SPI_HW_TX_FIFO_SIZE             (4)
#define HAL_SPI_HW_RX_FIFO_SIZE             (4)

// SPI DMA IRQ
#define HAL_SPI_0                           SPI0
#define HAL_SPI_1                           SPI1
#define HAL_SPI_2                           SPI2

#define HAL_SPI_0_DMA_TX_TRIGGER            DMA_SPI0_TX_TRIG
#define HAL_SPI_0_DMA_RX_TRIGGER            DMA_SPI0_RX_TRIG
#define HAL_SPI_1_DMA_TX_TRIGGER            DMA_SPI1_TX_TRIG
#define HAL_SPI_1_DMA_RX_TRIGGER            DMA_SPI1_RX_TRIG
#define HAL_SPI_2_DMA_TX_TRIGGER            DMA_SPI2_TX_TRIG
#define HAL_SPI_2_DMA_RX_TRIGGER            DMA_SPI2_RX_TRIG

#define HAL_SPI_DMA_INTERRUPT_RX_EVENT      DL_SPI_DMA_INTERRUPT_RX
#define HAL_SPI_DMA_INTERRUPT_TX_VALUE      (DL_SPI_INTERRUPT_DMA_DONE_TX)  // | DL_SPI_INTERRUPT_TX_EMPTY
#define HAL_SPI_DMA_INTERRUPT_RX_VALUE      (DL_SPI_INTERRUPT_DMA_DONE_RX)
#define HAL_SPI_DMA_INTERRUPT_TXRX_VALUE    (HAL_SPI_DMA_INTERRUPT_TX_VALUE | HAL_SPI_DMA_INTERRUPT_RX_VALUE)
#define HAL_SPI_0_IRQHandler                SPI0_IRQHandler
#define HAL_SPI_1_IRQHandler                SPI1_IRQHandler
#define HAL_SPI_2_IRQHandler                SPI2_IRQHandler

#define HAL_SPI_CHIP_SELECT_SIZE            (4)
#define HAL_SPI_BUFFER_SIZE                 (256)   // 5 + N (256)
#define HAL_SPI_CALLBACK_HANDLER_SIZE       (4)
/* Global typedef -----------------------------------------------------------*/
typedef enum
{
    kHAL_SPI_CHANNEL_0 = 0,
    kHAL_SPI_CHANNEL_1,
    kHAL_SPI_CHANNEL_2,
    kHAL_SPI_CHANNEL_COUNT
} eTypeHalSpiChannel;

typedef enum
{
    //kHAL_SPI_MODE_CONTROLLER = DL_SPI_MODE_CONTROLLER,
    //kHAL_SPI_MODE_PERIPHERAL = DL_SPI_MODE_PERIPHERAL,
    kHAL_SPI_MODE_MASTER = DL_SPI_MODE_CONTROLLER,
    kHAL_SPI_MODE_SLAVE = DL_SPI_MODE_PERIPHERAL
} eTypeHalSpiMode;

typedef enum
{
    kHAL_SPI_FRAME_FORMAT_3WIRE_SPO0_SPH0 = DL_SPI_FRAME_FORMAT_MOTO3_POL0_PHA0,
    kHAL_SPI_FRAME_FORMAT_3WIRE_SPO0_SPH1 = DL_SPI_FRAME_FORMAT_MOTO3_POL0_PHA1,
    kHAL_SPI_FRAME_FORMAT_3WIRE_SPO1_SPH0 = DL_SPI_FRAME_FORMAT_MOTO3_POL1_PHA0,
    kHAL_SPI_FRAME_FORMAT_3WIRE_SPO1_SPH1 = DL_SPI_FRAME_FORMAT_MOTO3_POL1_PHA1,
    kHAL_SPI_FRAME_FORMAT_4WIRE_SPO0_SPH0 = DL_SPI_FRAME_FORMAT_MOTO4_POL0_PHA0,
    kHAL_SPI_FRAME_FORMAT_4WIRE_SPO0_SPH1 = DL_SPI_FRAME_FORMAT_MOTO4_POL0_PHA1,
    kHAL_SPI_FRAME_FORMAT_4WIRE_SPO1_SPH0 = DL_SPI_FRAME_FORMAT_MOTO4_POL1_PHA0,
    kHAL_SPI_FRAME_FORMAT_4WIRE_SPO1_SPH1 = DL_SPI_FRAME_FORMAT_MOTO4_POL1_PHA1,
    kHAL_SPI_FRAME_FORMAT_TI_SYNC = DL_SPI_FRAME_FORMAT_TI_SYNC,
} eTypeHalSpiFrameFormat;

typedef enum
{
    kHAL_SPI_PARITY_NONE = DL_SPI_PARITY_NONE,
    kHAL_SPI_PARITY_ODD = DL_SPI_PARITY_ODD,
    kHAL_SPI_PARITY_EVEN = DL_SPI_PARITY_EVEN
} eTypeHalSpiParity;

typedef enum
{
    kHAL_SPI_FRAME_SIZE_BITS_4 = DL_SPI_DATA_SIZE_4,
    kHAL_SPI_FRAME_SIZE_BITS_5 = DL_SPI_DATA_SIZE_5,
    kHAL_SPI_FRAME_SIZE_BITS_6 = DL_SPI_DATA_SIZE_6,
    kHAL_SPI_FRAME_SIZE_BITS_7 = DL_SPI_DATA_SIZE_7,
    kHAL_SPI_FRAME_SIZE_BITS_8 = DL_SPI_DATA_SIZE_8,
    kHAL_SPI_FRAME_SIZE_BITS_9 = DL_SPI_DATA_SIZE_9,
    kHAL_SPI_FRAME_SIZE_BITS_10 = DL_SPI_DATA_SIZE_10,
    kHAL_SPI_FRAME_SIZE_BITS_11 = DL_SPI_DATA_SIZE_11,
    kHAL_SPI_FRAME_SIZE_BITS_12 = DL_SPI_DATA_SIZE_12,
    kHAL_SPI_FRAME_SIZE_BITS_13 = DL_SPI_DATA_SIZE_13,
    kHAL_SPI_FRAME_SIZE_BITS_14 = DL_SPI_DATA_SIZE_14,
    kHAL_SPI_FRAME_SIZE_BITS_15 = DL_SPI_DATA_SIZE_15,
    kHAL_SPI_FRAME_SIZE_BITS_16 = DL_SPI_DATA_SIZE_16,
} eTypeHalSpiFrameSize;

typedef enum
{
    kHAL_SPI_BIT_ORDER_MSB_FIRST = DL_SPI_BIT_ORDER_MSB_FIRST,
    kHAL_SPI_BIT_ORDER_LSB_FIRST = DL_SPI_BIT_ORDER_LSB_FIRST
} eTypeHalSpiBitOrder;

typedef enum
{
    kHAL_SPI_CHIP_SELECT_0 = DL_SPI_CHIP_SELECT_0,
    kHAL_SPI_CHIP_SELECT_1 = DL_SPI_CHIP_SELECT_1,
    kHAL_SPI_CHIP_SELECT_2 = DL_SPI_CHIP_SELECT_2,
    kHAL_SPI_CHIP_SELECT_3 = DL_SPI_CHIP_SELECT_3,
    kHAL_SPI_CHIP_SELECT_NONE = DL_SPI_CHIP_SELECT_NONE
} eTypeHalSpiChipSelect;

typedef struct
{
    eTypeHalSpiMode eMode;
    eTypeHalSpiFrameFormat eFrameFormat;
    eTypeHalSpiParity eParity;
    eTypeHalSpiFrameSize eFrameSize;
    eTypeHalSpiBitOrder eBitOrder;
    eTypeHalSpiChipSelect eChipSelect;
} tHalSpiConfig;

typedef enum
{
    kHAL_SPI_EVENT_DONE = 0,
    kHAL_SPI_EVENT_BUSY,
    kHAL_SPI_EVENT_ERROR
} eTypeHalSpiEvent;

typedef void (*tfpHalSpiEvent)(eTypeHalSpiEvent eEvent);
typedef void (*tfpHalSpiEventCallback)(void *pContext, uint8_t u8CsIndex, eTypeHalSpiEvent eEvent);

typedef struct
{
    eTypeHalSpiChannel eChannel;
    tHalSpiConfig mConfig;
    uint32_t u32BitRate;
    tfpHalSpiEvent fpSpiEvent;
} tHalSpi;

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
int8_t HalSpiOpen(tHalSpi *pmHalSpi);
bool HalSpiIsReady(tHalSpi *pmHalSpi);
int8_t HalSpiSendRecvBlockingFullDuplex(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint8_t *pu8RxData, uint16_t u16Size, uint8_t u8CsIndex);
int8_t HalSpiSendRecvBlockingHalfDuplex(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint16_t u16TxSize, uint8_t *pu8RxData, uint16_t u16RxSize, uint8_t u8CsIndex);
int8_t HalSpiSendRecvDmaFullDuplex(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint8_t *pu8RxData, uint16_t u16Size, uint8_t u8CsIndex);
//int8_t HalSpiSendRecvDmaHalfDuplex(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint16_t u16TxSize, uint8_t *pu8RxData, uint16_t u16RxSize, uint8_t u8CsIndex);
int8_t HalSpiSendBlockingRecvDma(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint16_t u16TxSize, uint8_t *pu8RxData, uint16_t u16RxSize, uint8_t u8CsIndex);
int8_t HalSpiRegisterEventHandler(tHalSpi *pmHalSpi, void *pContext, uint8_t u8CsIndex, tfpHalSpiEventCallback fpCallback);
int8_t HalSpiUnregisterEventHandler(tHalSpi *pmHalSpi, void *pContext, uint8_t u8CsIndex, tfpHalSpiEventCallback fpCallback);
/* Global test function prototypes ------------------------------------------*/
#ifdef HAL_SPI_ENABLE_SPI_EXAMPLE
void HalSpiExampleInitSpi(void);
void HalSpiExampleTransmit(void);
#endif

#ifdef HAL_SPI_ENABLE_UART
#include <stdarg.h>
#include <stdio.h>
#define UART_INIT                       DrvSyscfgDlUart0Init
#define UART_DEBUG                      DrvUart0SendMsg
void DrvSyscfgDlUart0Init(void);
void DrvUart0SendMsg(const char *strFormatMsg, ...);
#else
#define UART_INIT()                     ((void) 0)
#define UART_DEBUG(...)                 do {(void)0;} while(0)
#endif

#ifdef __cplusplus
}
#endif
#endif