/*
******************************************************************************
* @file     ApiProtectCocp.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "HalAfe.h"
#include "ApiProtect.h"
#include "LibSysPar.h"

void AppSerialUartSendMessage(char *str);

/* Private define ------------------------------------------------------------*/
#define API_PROTECT_COCP_GET_PAR(u8u8ProtectLevel, mProtectPar)   LibSysParGetCocpPar(u8u8ProtectLevel, mProtectPar)
#define API_PROTECT_COCP_GET_LEVEL(u8u8ProtectLevel, mProtectFlagValue)   ApiProtectGetLevelMask(u8u8ProtectLevel, mProtectFlagValue)
#define API_PROTECT_COCP_GET_CURRENT_VALUE(u8Index)     HalAfeGetCurrentValue(u8Index)

/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t	u8Flag;
	uint8_t	u8SetCount[API_PROTECT_LEVEL];
	uint8_t	u8ReleaseCount[API_PROTECT_LEVEL];
	tfpApiProtectEvtHandler  fpEvtHandler;
}tCocpProtect;

static tCocpProtect	gmCocpProtect={0};
static bool gbCocpEnable = 0;
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
uint8_t	apiProtectCocpGetFlag(void)
{
	return gmCocpProtect.u8Flag;
}
uint8_t apiProtectCocpHandler(uint8_t u8ProtectLevel)
{
	uint16_t		u16Value;
	int32_t		    i32CurrentValue;
	tProtectFlagValue	mProtectFlagValue;
	tScuProtectPar		mProtectPar;
	
	if (gbCocpEnable == 0)
	{
		return 1;
	}
	
	API_PROTECT_COCP_GET_PAR(u8ProtectLevel, &mProtectPar);
	API_PROTECT_COCP_GET_LEVEL(u8ProtectLevel, &mProtectFlagValue);

	i32CurrentValue = abs(API_PROTECT_COCP_GET_CURRENT_VALUE(0)) / 1000;
	//i32CurrentValue = 0;//abs(API_PROTECT_DOCP_GET_CURRENT_VALUE(P_CURRENT)) / 1000;

	//if(appGaugeGetCurrentMode() != APP_SCU_GAUGE_DISCHARGE_MODE)
	//	i32CurrentValue = 0;

	if(i32CurrentValue > mProtectPar.mSetValue.u32Value && mProtectPar.mSTime.u32Value != 0)
	{
		if((gmCocpProtect.u8Flag & mProtectFlagValue.u8Mask) == 0)
		{
			gmCocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
			gmCocpProtect.u8Flag |= mProtectFlagValue.u8Setting;
			gmCocpProtect.u8SetCount[u8ProtectLevel] = 1;			
		}
		else if((gmCocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
		{
			gmCocpProtect.u8SetCount[u8ProtectLevel]++;
			if(gmCocpProtect.u8SetCount[u8ProtectLevel] >= mProtectPar.mSTime.u32Value)
			{
				if(gmCocpProtect.fpEvtHandler)
				{
					u16Value = i32CurrentValue;
					gmCocpProtect.fpEvtHandler(0, kAPI_PROTECT_COCP_L1_SET + u8ProtectLevel, &u16Value);	
				}	
				gmCocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
				gmCocpProtect.u8Flag |= mProtectFlagValue.u8Setted;
				gmCocpProtect.u8SetCount[u8ProtectLevel] = 0;
				//appProtectCocpDebugMsg("COCP Set");
			}
		}
	}
	else if((gmCocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
	{
		gmCocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
	}
	//-----------------------------------------
    if(i32CurrentValue < mProtectPar.mRelValue.u32Value && mProtectPar.mRTime.u32Value != 0)
	{
		if((gmCocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setted)	//Seted!!
		{
			gmCocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
			gmCocpProtect.u8Flag |= mProtectFlagValue.u8Releasing;
			gmCocpProtect.u8ReleaseCount[u8ProtectLevel] = 1;
		}
		else if((gmCocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
		{
			gmCocpProtect.u8ReleaseCount[u8ProtectLevel] ++;
			if(gmCocpProtect.u8ReleaseCount[u8ProtectLevel]  >=  mProtectPar.mRTime.u32Value)
			{
				if(gmCocpProtect.fpEvtHandler != NULL)
				{
					u16Value = i32CurrentValue;
					gmCocpProtect.fpEvtHandler(0, kAPI_PROTECT_COCP_L1_RELEASE + u8ProtectLevel, &u16Value);
				}	
				gmCocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
				gmCocpProtect.u8ReleaseCount[u8ProtectLevel] = 0;
			}		
		}
	}
	else if((gmCocpProtect.u8Flag & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
	{
		gmCocpProtect.u8Flag &= mProtectFlagValue.u8ClearMask;
		gmCocpProtect.u8Flag |= mProtectFlagValue.u8Setted;
 	}	
	return 1;
}

void apiProtectCocpOpen(tfpApiProtectEvtHandler fpEvtHandler)
{
	gbCocpEnable = 1;
	
	gmCocpProtect.fpEvtHandler = fpEvtHandler;
}

/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    

