/*
******************************************************************************
* @file     HalAfeInternal.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef HAL_AFE_INTERNAL_H_
#define HAL_AFE_INTERNAL_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "LibFunctionPointerRegister.h"

#ifdef __cplusplus
extern "C" {
#endif

#define HAL_AFE_GET_CELL_NUM()	        LibSysParGetCellNumber()
#define HAL_AFE_GET_NTC_NUM()  	        LibSysParGetNtcNumber()
#define HAL_AFE_GET_AFE_NUM()	        LibSysParGetTotalAfeNumber()
#define HAL_AFE_GET_CELL_FLAG(bmu)      LibSysParGetCellFlag(bmu)
#define HAL_AFE_GET_NTC_FLAG(bmu)       LibSysParGetNtcFlag(bmu)
#define HAL_AFE_CVT_VOL_TO_TEMP(vol)	LibNtcVoltageToTemperature(vol)


#ifdef __cplusplus
}
#endif


	

#endif /* HAL_AFE_H_ */

/************************ (C) COPYRIGHT *****END OF FILE****/    
