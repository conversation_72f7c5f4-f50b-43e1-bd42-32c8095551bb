/*
******************************************************************************
* @file     <PERSON><PERSON><PERSON>.c
* <AUTHOR> @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "<PERSON><PERSON><PERSON>.h"
/* Local typedef ------------------------------------------------------------*/
typedef enum
{
    kHAL_SPI_CLOCK_SOURCE_BUSCLK = DL_SPI_CLOCK_BUSCLK,
    kHAL_SPI_CLOCK_SOURCE_MFCLK = DL_SPI_CLOCK_MFCLK,
    kHAL_SPI_CLOCK_SOURCE_LFCLK = DL_SPI_CLOCK_LFCLK,
} eTypeHalSpiClockSource;

typedef enum
{
    kHAL_SPI_CLOCK_DIVIDER_1 = DL_SPI_CLOCK_DIVIDE_RATIO_1,
    kHAL_SPI_CLOCK_DIVIDER_2 = DL_SPI_CLOCK_DIVIDE_RATIO_2,
    kHAL_SPI_CLOCK_DIVIDER_3 = DL_SPI_CLOCK_DIVIDE_RATIO_3,
    kHAL_SPI_CLOCK_DIVIDER_4 = DL_SPI_CLOCK_DIVIDE_RATIO_4,
    kHAL_SPI_CLOCK_DIVIDER_5 = DL_SPI_CLOCK_DIVIDE_RATIO_5,
    kHAL_SPI_CLOCK_DIVIDER_6 = DL_SPI_CLOCK_DIVIDE_RATIO_6,
    kHAL_SPI_CLOCK_DIVIDER_7 = DL_SPI_CLOCK_DIVIDE_RATIO_7,
    kHAL_SPI_CLOCK_DIVIDER_8 = DL_SPI_CLOCK_DIVIDE_RATIO_8,
} eTypeHalSpiClockDivider;

typedef enum
{
    kHAL_SPI_FUNCTION_PIN_SCLK = 0,
    kHAL_SPI_FUNCTION_PIN_PICO,
    kHAL_SPI_FUNCTION_PIN_POCI,
    kHAL_SPI_FUNCTION_PIN_COUNT
} eTypeHalSpiFunctionPin;

typedef enum
{
    kHAL_SPI_TASK_MODE_NONE = 0,
    kHAL_SPI_TASK_MODE_DMA_SEND = (1 << 0),
    kHAL_SPI_TASK_MODE_DMA_RECV = (1 << 1),
    kHAL_SPI_TASK_MODE_DMA_BOTH = (kHAL_SPI_TASK_MODE_DMA_SEND | kHAL_SPI_TASK_MODE_DMA_RECV),
} eTypeHalSpiTaskMode;

typedef enum
{
    kHAL_SPI_DMA_MODE_TX = 0,
    kHAL_SPI_DMA_MODE_RX,
    kHAL_SPI_DMA_MODE_BOTH,
} eTypeHalSpiDmaMode;

typedef enum
{
    kHAL_SPI_TASK_DONE_NONE = 0,
    kHAL_SPI_TASK_DONE_DMA_SEND_DONE = (1 << 0),
    kHAL_SPI_TASK_DONE_DMA_SEND_TRIG = (1 << 1),
    kHAL_SPI_TASK_DONE_DMA_SEND_BOTH = (kHAL_SPI_TASK_DONE_DMA_SEND_DONE | kHAL_SPI_TASK_DONE_DMA_SEND_TRIG),
    kHAL_SPI_TASK_DONE_DMA_RECV_DONE = (1 << 2),
    kHAL_SPI_TASK_DONE_DMA_RECV_TRIG = (1 << 3),
    kHAL_SPI_TASK_DONE_DMA_RECV_BOTH = (kHAL_SPI_TASK_DONE_DMA_RECV_DONE | kHAL_SPI_TASK_DONE_DMA_RECV_TRIG),
    kHAL_SPI_TASK_DONE_DMA_SEND_RECV_DONE = (kHAL_SPI_TASK_DONE_DMA_SEND_DONE | kHAL_SPI_TASK_DONE_DMA_RECV_DONE),
    kHAL_SPI_TASK_DONE_DMA_SEND_RECV_TRIG = (kHAL_SPI_TASK_DONE_DMA_SEND_TRIG | kHAL_SPI_TASK_DONE_DMA_RECV_TRIG),
    kHAL_SPI_TASK_DONE_DMA_SEND_FINISH = (kHAL_SPI_TASK_DONE_DMA_SEND_DONE | kHAL_SPI_TASK_DONE_DMA_SEND_RECV_TRIG),
    kHAL_SPI_TASK_DONE_DMA_RECV_FINISH = (kHAL_SPI_TASK_DONE_DMA_RECV_DONE | kHAL_SPI_TASK_DONE_DMA_SEND_RECV_TRIG),
    kHAL_SPI_TASK_DONE_DMA_BOTH_FINISH = (kHAL_SPI_TASK_DONE_DMA_SEND_RECV_DONE | kHAL_SPI_TASK_DONE_DMA_SEND_RECV_TRIG),
} eTypeHalSpiTaskDone;

typedef enum
{
    kHAL_SPI_CHANNEL_0_IRQ = SPI0_INT_IRQn,
    kHAL_SPI_CHANNEL_1_IRQ = SPI1_INT_IRQn,
    kHAL_SPI_CHANNEL_2_IRQ = SPI2_INT_IRQn
} eTypeHalSpiChannelIrq;

typedef struct
{
    eTypeHalSpiClockSource eClockSource;
    eTypeHalSpiClockDivider eClockDivider;
} tHalSpiClockConfig;

typedef struct
{
    IOMUX_PINCM eIomuxPincm;
    uint32_t u32IomuxPf;
} tHalSpiPinIomuxValue;

typedef struct
{
    bool bIsValid;
    GPIO_Regs *pGpioRegs;
    uint32_t u32PinBit;
    IOMUX_PINCM eIomuxPincm;
} tHalSpiCsGpio;

typedef struct tHalSpiEventHandler
{
    void *pContext;
    uint8_t u8CsIndex;
    tfpHalSpiEventCallback fpCallback;
    struct tHalSpiEventHandler *pmNextHandler;
} tHalSpiEventHandler;

typedef struct
{
    tHalSpiEventHandler *pHandlerListHead;
    uint16_t u16HandlerCount;
} tHalSpiEventDispatcher;

typedef struct
{
    SPI_Regs *pmSpi;
    tHalSpiPinIomuxValue mPinIomuxValueMappings[kHAL_SPI_FUNCTION_PIN_COUNT];
    tHalSpiCsGpio mCsGpio[HAL_SPI_CHIP_SELECT_SIZE];
    eTypeBspDmaChannel eDmaTxChannel, eDmaRxChannel;
    uint8_t u8SelectCs;

    uint8_t u8DmaBuf[HAL_SPI_BUFFER_SIZE];
    uint8_t *pu8TwiceTxData, *pu8TwiceRxData;
    uint16_t u16TwiceTxSize, u16TwiceRxSize;

    volatile eTypeHalSpiTaskMode eTaskMode;
    volatile eTypeHalSpiTaskDone eTaskDone;

    tHalSpiEventDispatcher mDispatcher;
    tHalSpiEventHandler mHandlers[HAL_SPI_CALLBACK_HANDLER_SIZE];
} tHalSpiPrivate;
/* Local define -------------------------------------------------------------*/
#define HAL_SPI_BIT_RATE_SCR_MAX_VALUE                  (1023)
/* Local macro --------------------------------------------------------------*/
/* Local variables ----------------------------------------------------------*/
static bool bIsInitHalSpiResource = false;
static tHalSpi *pmHalSpiContext[kHAL_SPI_CHANNEL_COUNT];
static tHalSpiPrivate mHalSpiPrivateContext[kHAL_SPI_CHANNEL_COUNT] =
{
    [kHAL_SPI_CHANNEL_0] = 
    {
        .pmSpi = HAL_SPI_0,
        #if defined(BSP_SPI_0_SCLK_IOMUX) && defined(BSP_SPI_0_SCLK_IOMUX_PF) && defined(BSP_SPI_0_MOSI_IOMUX) && defined(BSP_SPI_0_MOSI_IOMUX_PF) && defined(BSP_SPI_0_MISO_IOMUX) && defined(BSP_SPI_0_MISO_IOMUX_PF)
        .mPinIomuxValueMappings =
        {
            [kHAL_SPI_FUNCTION_PIN_SCLK] =
            {
                .eIomuxPincm = BSP_SPI_0_SCLK_IOMUX,
                .u32IomuxPf = BSP_SPI_0_SCLK_IOMUX_PF,
            },
            [kHAL_SPI_FUNCTION_PIN_PICO] =
            {
                .eIomuxPincm = BSP_SPI_0_MOSI_IOMUX,
                .u32IomuxPf = BSP_SPI_0_MOSI_IOMUX_PF,
            },
            [kHAL_SPI_FUNCTION_PIN_POCI] =
            {
                .eIomuxPincm = BSP_SPI_0_MISO_IOMUX,
                .u32IomuxPf = BSP_SPI_0_MISO_IOMUX_PF,
            },
        },
        #endif
        .mCsGpio = 
        {
            #if HAL_SPI_CHIP_SELECT_SIZE >= 1
            #if defined(BSP_SPI_0_CS0_PORT) && defined(BSP_SPI_0_CS0_PIN) && defined(BSP_SPI_0_CS0_IOMUX)
            [0] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_0_CS0_PORT,
                .u32PinBit = BSP_SPI_0_CS0_PIN,
                .eIomuxPincm = BSP_SPI_0_CS0_IOMUX,
            },
            #else
            [0] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 2
            #if defined(BSP_SPI_0_CS1_PORT) && defined(BSP_SPI_0_CS1_PIN) && defined(BSP_SPI_0_CS1_IOMUX)
            [1] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_0_CS1_PORT,
                .u32PinBit = BSP_SPI_0_CS1_PIN,
                .eIomuxPincm = BSP_SPI_0_CS1_IOMUX,
            },
            #else
            [1] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 3
            #if defined(BSP_SPI_0_CS2_PORT) && defined(BSP_SPI_0_CS2_PIN) && defined(BSP_SPI_0_CS2_IOMUX)
            [2] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_0_CS2_PORT,
                .u32PinBit = BSP_SPI_0_CS2_PIN,
                .eIomuxPincm = BSP_SPI_0_CS2_IOMUX,
            },
            #else
            [2] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 4
            #if defined(BSP_SPI_0_CS3_PORT) && defined(BSP_SPI_0_CS3_PIN) && defined(BSP_SPI_0_CS3_IOMUX)
            [3] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_0_CS3_PORT,
                .u32PinBit = BSP_SPI_0_CS3_PIN,
                .eIomuxPincm = BSP_SPI_0_CS3_IOMUX,
            },
            #else
            [3] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
        },
        #if defined(BSP_SPI_0_TX_DMA_CHANNEL)
        .eDmaTxChannel = BSP_SPI_0_TX_DMA_CHANNEL,
        #else
        .eDmaTxChannel = kBSP_DMA_CHANNEL_NONE,
        #endif
        #if defined(BSP_SPI_0_RX_DMA_CHANNEL)
        .eDmaRxChannel = BSP_SPI_0_RX_DMA_CHANNEL,
        #else
        .eDmaRxChannel = kBSP_DMA_CHANNEL_NONE,
        #endif
        .u8SelectCs = 0xFF,
        .pu8TwiceTxData = NULL,
        .pu8TwiceRxData = NULL,
        .u16TwiceTxSize = 0,
        .u16TwiceRxSize = 0,
        .eTaskMode = kHAL_SPI_TASK_MODE_NONE,
        .eTaskDone = kHAL_SPI_TASK_DONE_NONE,
        .mDispatcher = 
        {
            .pHandlerListHead = NULL,
            .u16HandlerCount = 0
        },
    },
    [kHAL_SPI_CHANNEL_1] = 
    {
        .pmSpi = HAL_SPI_1,
        #if defined(BSP_SPI_1_SCLK_IOMUX) && defined(BSP_SPI_1_SCLK_IOMUX_PF) && defined(BSP_SPI_1_MOSI_IOMUX) && defined(BSP_SPI_1_MOSI_IOMUX_PF) && defined(BSP_SPI_1_MISO_IOMUX) && defined(BSP_SPI_1_MISO_IOMUX_PF)
        .mPinIomuxValueMappings =
        {
            [kHAL_SPI_FUNCTION_PIN_SCLK] =
            {
                .eIomuxPincm = BSP_SPI_1_SCLK_IOMUX,
                .u32IomuxPf = BSP_SPI_1_SCLK_IOMUX_PF,
            },
            [kHAL_SPI_FUNCTION_PIN_PICO] =
            {
                .eIomuxPincm = BSP_SPI_1_MOSI_IOMUX,
                .u32IomuxPf = BSP_SPI_1_MOSI_IOMUX_PF,
            },
            [kHAL_SPI_FUNCTION_PIN_POCI] =
            {
                .eIomuxPincm = BSP_SPI_1_MISO_IOMUX,
                .u32IomuxPf = BSP_SPI_1_MISO_IOMUX_PF,
            },
        },
        #endif
        .mCsGpio = 
        {
            #if HAL_SPI_CHIP_SELECT_SIZE >= 1
            #if defined(BSP_SPI_1_CS0_PORT) && defined(BSP_SPI_1_CS0_PIN) && defined(BSP_SPI_1_CS0_IOMUX)
            [0] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_1_CS0_PORT,
                .u32PinBit = BSP_SPI_1_CS0_PIN,
                .eIomuxPincm = BSP_SPI_1_CS0_IOMUX,
            },
            #else
            [0] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 2
            #if defined(BSP_SPI_1_CS1_PORT) && defined(BSP_SPI_1_CS1_PIN) && defined(BSP_SPI_1_CS1_IOMUX)
            [1] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_1_CS1_PORT,
                .u32PinBit = BSP_SPI_1_CS1_PIN,
                .eIomuxPincm = BSP_SPI_1_CS1_IOMUX,
            },
            #else
            [1] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 3
            #if defined(BSP_SPI_1_CS2_PORT) && defined(BSP_SPI_1_CS2_PIN) && defined(BSP_SPI_1_CS2_IOMUX)
            [2] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_1_CS2_PORT,
                .u32PinBit = BSP_SPI_1_CS2_PIN,
                .eIomuxPincm = BSP_SPI_1_CS2_IOMUX,
            },
            #else
            [2] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 4
            #if defined(BSP_SPI_1_CS3_PORT) && defined(BSP_SPI_1_CS3_PIN) && defined(BSP_SPI_1_CS3_IOMUX)
            [3] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_1_CS3_PORT,
                .u32PinBit = BSP_SPI_1_CS3_PIN,
                .eIomuxPincm = BSP_SPI_1_CS3_IOMUX,
            },
            #else
            [3] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
        },
        #if defined(BSP_SPI_1_TX_DMA_CHANNEL)
        .eDmaTxChannel = BSP_SPI_1_TX_DMA_CHANNEL,
        #else
        .eDmaTxChannel = kBSP_DMA_CHANNEL_NONE,
        #endif
        #if defined(BSP_SPI_1_RX_DMA_CHANNEL)
        .eDmaRxChannel = BSP_SPI_1_RX_DMA_CHANNEL,
        #else
        .eDmaRxChannel = kBSP_DMA_CHANNEL_NONE,
        #endif
        .u8SelectCs = 0xFF,
        .pu8TwiceTxData = NULL,
        .pu8TwiceRxData = NULL,
        .u16TwiceTxSize = 0,
        .u16TwiceRxSize = 0,
        .eTaskMode = kHAL_SPI_TASK_MODE_NONE,
        .eTaskDone = kHAL_SPI_TASK_DONE_NONE,
        .mDispatcher = 
        {
            .pHandlerListHead = NULL,
            .u16HandlerCount = 0
        },
    },
    [kHAL_SPI_CHANNEL_2] = 
    {
        .pmSpi = HAL_SPI_2,
        #if defined(BSP_SPI_2_SCLK_IOMUX) && defined(BSP_SPI_2_SCLK_IOMUX_PF) && defined(BSP_SPI_2_MOSI_IOMUX) && defined(BSP_SPI_2_MOSI_IOMUX_PF) && defined(BSP_SPI_2_MISO_IOMUX) && defined(BSP_SPI_2_MISO_IOMUX_PF)
        .mPinIomuxValueMappings =
        {
            [kHAL_SPI_FUNCTION_PIN_SCLK] =
            {
                .eIomuxPincm = BSP_SPI_2_SCLK_IOMUX,
                .u32IomuxPf = BSP_SPI_2_SCLK_IOMUX_PF,
            },
            [kHAL_SPI_FUNCTION_PIN_PICO] =
            {
                .eIomuxPincm = BSP_SPI_2_MOSI_IOMUX,
                .u32IomuxPf = BSP_SPI_2_MOSI_IOMUX_PF,
            },
            [kHAL_SPI_FUNCTION_PIN_POCI] =
            {
                .eIomuxPincm = BSP_SPI_2_MISO_IOMUX,
                .u32IomuxPf = BSP_SPI_2_MISO_IOMUX_PF,
            },
        },
        #endif
        .mCsGpio = 
        {
            #if HAL_SPI_CHIP_SELECT_SIZE >= 1
            #if defined(BSP_SPI_2_CS0_PORT) && defined(BSP_SPI_2_CS0_PIN) && defined(BSP_SPI_2_CS0_IOMUX)
            [0] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_2_CS0_PORT,
                .u32PinBit = BSP_SPI_2_CS0_PIN,
                .eIomuxPincm = BSP_SPI_2_CS0_IOMUX,
            },
            #else
            [0] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 2
            #if defined(BSP_SPI_2_CS1_PORT) && defined(BSP_SPI_2_CS1_PIN) && defined(BSP_SPI_2_CS1_IOMUX)
            [1] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_2_CS1_PORT,
                .u32PinBit = BSP_SPI_2_CS1_PIN,
                .eIomuxPincm = BSP_SPI_2_CS1_IOMUX,
            },
            #else
            [1] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 3
            #if defined(BSP_SPI_2_CS2_PORT) && defined(BSP_SPI_2_CS2_PIN) && defined(BSP_SPI_2_CS2_IOMUX)
            [2] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_2_CS2_PORT,
                .u32PinBit = BSP_SPI_2_CS2_PIN,
                .eIomuxPincm = BSP_SPI_2_CS2_IOMUX,
            },
            #else
            [2] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
            #if HAL_SPI_CHIP_SELECT_SIZE >= 4
            #if defined(BSP_SPI_2_CS3_PORT) && defined(BSP_SPI_2_CS3_PIN) && defined(BSP_SPI_2_CS3_IOMUX)
            [3] = 
            {
                .bIsValid = true,
                .pGpioRegs = BSP_SPI_2_CS3_PORT,
                .u32PinBit = BSP_SPI_2_CS3_PIN,
                .eIomuxPincm = BSP_SPI_2_CS3_IOMUX,
            },
            #else
            [3] = 
            {
                .bIsValid = false
            },
            #endif
            #endif
        },
        #if defined(BSP_SPI_2_TX_DMA_CHANNEL)
        .eDmaTxChannel = BSP_SPI_2_TX_DMA_CHANNEL,
        #else
        .eDmaTxChannel = kBSP_DMA_CHANNEL_NONE,
        #endif
        #if defined(BSP_SPI_2_RX_DMA_CHANNEL)
        .eDmaRxChannel = BSP_SPI_2_RX_DMA_CHANNEL,
        #else
        .eDmaRxChannel = kBSP_DMA_CHANNEL_NONE,
        #endif
        .u8SelectCs = 0xFF,
        .pu8TwiceTxData = NULL,
        .pu8TwiceRxData = NULL,
        .u16TwiceTxSize = 0,
        .u16TwiceRxSize = 0,
        .eTaskMode = kHAL_SPI_TASK_MODE_NONE,
        .eTaskDone = kHAL_SPI_TASK_DONE_NONE,
        .mDispatcher = 
        {
            .pHandlerListHead = NULL,
            .u16HandlerCount = 0
        },
    }
};
static const  tHalSpiClockConfig gmHalSpiClockConfig =
{
    .eClockSource = kHAL_SPI_CLOCK_SOURCE_BUSCLK,
    .eClockDivider = kHAL_SPI_CLOCK_DIVIDER_1
};
static const DL_DMA_Config mDmaDefaultConfig[kHAL_SPI_DMA_MODE_BOTH] =
{
    [kHAL_SPI_DMA_MODE_TX] =
    {
        .transferMode   = DL_DMA_SINGLE_TRANSFER_MODE,
        .extendedMode   = DL_DMA_NORMAL_MODE,
        .destIncrement  = DL_DMA_ADDR_UNCHANGED,
        .srcIncrement   = DL_DMA_ADDR_INCREMENT,
        .destWidth      = DL_DMA_WIDTH_BYTE,
        .srcWidth       = DL_DMA_WIDTH_BYTE,
        .triggerType    = DL_DMA_TRIGGER_TYPE_EXTERNAL,
    },
    [kHAL_SPI_DMA_MODE_RX] =
    {
        .transferMode   = DL_DMA_SINGLE_TRANSFER_MODE,
        .extendedMode   = DL_DMA_NORMAL_MODE,
        .destIncrement  = DL_DMA_ADDR_INCREMENT,
        .srcIncrement   = DL_DMA_ADDR_UNCHANGED,
        .destWidth      = DL_DMA_WIDTH_BYTE,
        .srcWidth       = DL_DMA_WIDTH_BYTE,
        .triggerType    = DL_DMA_TRIGGER_TYPE_EXTERNAL,
    }
};
/* Local function declare ---------------------------------------------------*/
static void HalSpiInit(void);
static bool HalSpiCheckSpiResoure(tHalSpi *pmHalSpi);
static void HalSpiInitGpio(tHalSpi *pmHalSpi);
static void HalSpiSetBitRate(tHalSpi *pmHalSpi, uint32_t u32BitRate);
static void HalSpiInitSyscfg(tHalSpi *pmHalSpi);

static uint8_t HalSpiGetLastChipSelect(tHalSpi *pmHalSpi);
static void HalSpiSetNextChipSelect(tHalSpi *pmHalSpi, uint8_t u8CsIndex);
static void HalSpiControlChipSelect(tHalSpi *pmHalSpi, bool bEnable);
static bool HalSpiHwIsBusy(tHalSpi *pmHalSpi);
static bool HalSpiCheckDmaChannelConfig(tHalSpi *pmHalSpi);
static int8_t HalSpiCheckDmaIsReady(tHalSpi *pmHalSpi);
static void HalSpiSetDmaTxEvent(tHalSpi *pmHalSpi, bool bEnable);
static uint32_t HalSpiGetDmaTxEvent(tHalSpi *pmHalSpi);
static void HalSpiSetDmaRxEvent(tHalSpi *pmHalSpi, bool bEnable, uint32_t u32Interrupt);
static uint32_t HalSpiGetDmaRxEvent(tHalSpi *pmHalSpi, uint32_t u32Interrupt);
static void HalSpiSetDmaEvent(tHalSpi *pmHalSpi, bool bEnable);
static void HalSpiSetInterrupt(tHalSpi *pmHalSpi, bool bEnable, uint32_t u32Interrupt);
static uint32_t HalSpiGetInterrupt(tHalSpi *pmHalSpi, uint32_t u32Interrupt);
static void HalSpiSetDmaInterrupt(tHalSpi *pmHalSpi, bool bEnable);
static void HalSpiInitDmaChannel(tHalSpi *pmHalSpi);
static void HalSpiSetDmaChannel(uint32_t u32DmaChannel, bool bEnable);
static bool HalSpiGetDmaChannelStatus(uint32_t u32DmaChannel);
static void HalSpiSetNvicIrq(tHalSpi *pmHalSpi, bool bEnable);
static uint32_t HalSpiGetNvicIrqStatus(tHalSpi *pmHalSpi);
static void HalSpiOpenDma(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint16_t u16TxSize, uint8_t *pu8RxData, uint16_t u16RxSize);
static void HalSpiIrqHandler(tHalSpi *pmHalSpi);
static void HalSpiTriggerEvent(tHalSpi *pmHalSpi, eTypeHalSpiEvent eEvent);
static void HalSpiCheckSpiTask(tHalSpi *pmHalSpi);
static tHalSpiEventHandler *HalSpiGetEventHandlerPointer(tHalSpi *pmHalSpi);
static void HalSpiReleaseEventHandlerPointer(tHalSpiEventHandler *pTarget);
/* Global variables ---------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
int8_t HalSpiOpen(tHalSpi *pmHalSpi)
{
    HalSpiInit();
    if (HalSpiCheckSpiResoure(pmHalSpi) == false)
    {
        return RES_ERROR_INVALID_STATE;
    }
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    pmHalSpiContext[pmHalSpi -> eChannel] = pmHalSpi;
    DL_SPI_enablePower(pmHalSpiPrivateContext -> pmSpi);
    HalSpiInitGpio(pmHalSpi);
    HalSpiInitSyscfg(pmHalSpi);
    for (uint8_t u8Index = 0; u8Index < HAL_SPI_CALLBACK_HANDLER_SIZE; u8Index++)
    {
        pmHalSpiPrivateContext -> mHandlers[u8Index] = (tHalSpiEventHandler)
        {
            .pContext = NULL,
            .u8CsIndex = 0xFF,
            .fpCallback = NULL,
            .pmNextHandler = NULL
        };
    }
    return RES_SUCCESS;
}

bool HalSpiIsReady(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (pmHalSpiPrivateContext -> eTaskMode != kHAL_SPI_TASK_MODE_NONE ||
        pmHalSpiPrivateContext -> eTaskDone != kHAL_SPI_TASK_DONE_NONE)
    {
        return false;
    }
    return true;
}

int8_t HalSpiSendRecvBlockingFullDuplex(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint8_t *pu8RxData, uint16_t u16Size, uint8_t u8CsIndex)
{
    int16_t i16Check = HalSpiCheckDmaIsReady(pmHalSpi);
    if (i16Check != RES_SUCCESS)
    {
        return i16Check;
    }
    HalSpiSetNextChipSelect(pmHalSpi, u8CsIndex);
    HalSpiControlChipSelect(pmHalSpi, true);
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    uint8_t u8TxData, u8RxData;
    u8TxData = 0xFF;
    while (u16Size > 0)
    {
        if (pu8TxData != NULL)
        {
            u8TxData = *pu8TxData++;
        }
        DL_SPI_transmitDataBlocking8(pmHalSpiPrivateContext -> pmSpi, u8TxData);
        u8RxData = DL_SPI_receiveDataBlocking8(pmHalSpiPrivateContext -> pmSpi);
        if (pu8RxData != NULL)
        {
            *pu8RxData++ = u8RxData;
        }
        u16Size--;
    }
    HalSpiControlChipSelect(pmHalSpi, false);
    return RES_SUCCESS;
}

int8_t HalSpiSendRecvBlockingHalfDuplex(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint16_t u16TxSize, uint8_t *pu8RxData, uint16_t u16RxSize, uint8_t u8CsIndex)
{
    int16_t i16Check = HalSpiCheckDmaIsReady(pmHalSpi);
    if (i16Check != RES_SUCCESS)
    {
        return i16Check;
    }
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    HalSpiSetNextChipSelect(pmHalSpi, u8CsIndex);
    HalSpiControlChipSelect(pmHalSpi, true);
    while (pu8TxData != NULL && u16TxSize > 0)
    {
        DL_SPI_transmitDataBlocking8(pmHalSpiPrivateContext -> pmSpi, *pu8TxData++);
        DL_SPI_receiveDataBlocking8(pmHalSpiPrivateContext -> pmSpi);
        u16TxSize--;
    }
    while (pu8RxData != NULL && u16RxSize > 0)
    {
        DL_SPI_transmitDataBlocking8(pmHalSpiPrivateContext -> pmSpi, 0xFF);
        *pu8RxData++ = DL_SPI_receiveDataBlocking8(pmHalSpiPrivateContext -> pmSpi);
        u16RxSize--;
    }
    HalSpiControlChipSelect(pmHalSpi, false);
    return RES_SUCCESS;
}

int8_t HalSpiSendRecvDmaFullDuplex(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint8_t *pu8RxData, uint16_t u16Size, uint8_t u8CsIndex)
{
    if (u16Size == 0)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    int16_t i16Check = HalSpiCheckDmaIsReady(pmHalSpi);
    if (i16Check != RES_SUCCESS)
    {
        return i16Check;
    }
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    HalSpiSetNextChipSelect(pmHalSpi, u8CsIndex);
    if (pu8TxData != NULL)
    {
        pmHalSpiPrivateContext -> eTaskMode |= kHAL_SPI_TASK_MODE_DMA_SEND;
    }
    if (pu8RxData != NULL)
    {
        pmHalSpiPrivateContext -> eTaskMode |= kHAL_SPI_TASK_MODE_DMA_RECV;
    }
    switch (pmHalSpiPrivateContext -> eTaskMode)
    {
    case kHAL_SPI_TASK_MODE_DMA_SEND:
        pmHalSpiPrivateContext -> eTaskDone |= kHAL_SPI_TASK_DONE_DMA_SEND_DONE;
        HalSpiOpenDma(pmHalSpi, pu8TxData, u16Size, pmHalSpiPrivateContext -> u8DmaBuf, u16Size);
        break;
    case kHAL_SPI_TASK_MODE_DMA_RECV:
        pmHalSpiPrivateContext -> eTaskDone |= kHAL_SPI_TASK_DONE_DMA_RECV_DONE;
        HalSpiOpenDma(pmHalSpi, pmHalSpiPrivateContext -> u8DmaBuf, u16Size, pu8RxData, u16Size);
        break;
    case kHAL_SPI_TASK_MODE_DMA_BOTH:
        pmHalSpiPrivateContext -> eTaskDone |= kHAL_SPI_TASK_DONE_DMA_SEND_RECV_DONE;
        HalSpiOpenDma(pmHalSpi, pu8TxData, u16Size, pu8RxData, u16Size);
        break;
    default:
        break;
    }
    return RES_SUCCESS;
}

int8_t HalSpiSendRecvDmaHalfDuplex(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint16_t u16TxSize, uint8_t *pu8RxData, uint16_t u16RxSize, uint8_t u8CsIndex)
{
    int16_t i16Check = HalSpiCheckDmaIsReady(pmHalSpi);
    if (i16Check != RES_SUCCESS)
    {
        return i16Check;
    }
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    HalSpiSetNextChipSelect(pmHalSpi, u8CsIndex);
    if (pu8TxData != NULL && u16TxSize != 0)
    {
        pmHalSpiPrivateContext -> eTaskMode |= kHAL_SPI_TASK_MODE_DMA_SEND;
    }
    if (pu8RxData != NULL && u16RxSize != 0)
    {
        pmHalSpiPrivateContext -> eTaskMode |= kHAL_SPI_TASK_MODE_DMA_RECV;
        pmHalSpiPrivateContext -> pu8TwiceRxData = &pu8RxData[0];
        pmHalSpiPrivateContext -> u16TwiceRxSize = u16RxSize;
    }
    if ((pmHalSpiPrivateContext -> eTaskMode & kHAL_SPI_TASK_MODE_DMA_SEND) == kHAL_SPI_TASK_MODE_DMA_SEND)
    {
        pmHalSpiPrivateContext -> eTaskDone |= kHAL_SPI_TASK_DONE_DMA_SEND_DONE;
        HalSpiOpenDma(pmHalSpi, pu8TxData, u16TxSize, pmHalSpiPrivateContext -> u8DmaBuf, u16TxSize);
    }
    else if ((pmHalSpiPrivateContext -> eTaskMode & kHAL_SPI_TASK_MODE_DMA_RECV) == kHAL_SPI_TASK_MODE_DMA_RECV)
    {
        pmHalSpiPrivateContext -> eTaskDone |= kHAL_SPI_TASK_DONE_DMA_RECV_DONE;
        HalSpiOpenDma(pmHalSpi, pmHalSpiPrivateContext -> u8DmaBuf, u16RxSize, pu8RxData, u16RxSize);
    }
    return RES_SUCCESS;
}

int8_t HalSpiSendBlockingRecvDma(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint16_t u16TxSize, uint8_t *pu8RxData, uint16_t u16RxSize, uint8_t u8CsIndex)
{
    int16_t i16Check = HalSpiCheckDmaIsReady(pmHalSpi);
    if (i16Check != RES_SUCCESS)
    {
        return i16Check;
    }
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    HalSpiSetNextChipSelect(pmHalSpi, u8CsIndex);
    HalSpiControlChipSelect(pmHalSpi, true);
    while (pu8TxData != NULL && u16TxSize > 0)
    {
        DL_SPI_transmitDataBlocking8(pmHalSpiPrivateContext -> pmSpi, *pu8TxData++);
        DL_SPI_receiveDataBlocking8(pmHalSpiPrivateContext -> pmSpi);
        u16TxSize--;
    }
    if (pu8RxData != NULL &&
        u16RxSize != 0)
    {
        pmHalSpiPrivateContext -> eTaskMode |= kHAL_SPI_TASK_MODE_DMA_RECV;
        pmHalSpiPrivateContext -> eTaskDone |= kHAL_SPI_TASK_DONE_DMA_RECV_DONE;
        for (uint16_t u16Index = 0; u16Index < u16RxSize; u16Index++)
        {
            pmHalSpiPrivateContext -> u8DmaBuf[u16Index] = 0xFF;
        }
        HalSpiOpenDma(pmHalSpi, pmHalSpiPrivateContext -> u8DmaBuf, u16RxSize, pu8RxData, u16RxSize);
    }
    else
    {
        HalSpiControlChipSelect(pmHalSpi, false);
    }
    return RES_SUCCESS;
}

int8_t HalSpiRegisterEventHandler(tHalSpi *pmHalSpi, void *pContext, uint8_t u8CsIndex, tfpHalSpiEventCallback fpCallback)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (pmHalSpiPrivateContext -> mDispatcher.u16HandlerCount >= HAL_SPI_CALLBACK_HANDLER_SIZE)
    {
        return RES_ERROR_FULL;
    }
    uint16_t u16HandlerOffset = 0;
    tHalSpiEventHandler *pNowHandler, *pNextHandler;
    pNowHandler = NULL;
    pNextHandler = pmHalSpiPrivateContext -> mDispatcher.pHandlerListHead;
    while (pNextHandler != NULL)
    {
        if (pNextHandler -> pContext == pContext &&
            pNextHandler -> u8CsIndex == u8CsIndex &&
            pNextHandler -> fpCallback == fpCallback)
        {
            return RES_ERROR_INVALID_STATE;
        }
        pNowHandler = pNextHandler;
        pNextHandler = pNextHandler -> pmNextHandler;
        u16HandlerOffset++;
    }
    if (pmHalSpiPrivateContext -> mDispatcher.u16HandlerCount != u16HandlerOffset)
    {
        return RES_ERROR_NOT_FOUND;
    }
    pNextHandler = HalSpiGetEventHandlerPointer(pmHalSpi);
    pNextHandler -> pContext = pContext;
    pNextHandler -> u8CsIndex = u8CsIndex;
    pNextHandler -> fpCallback = fpCallback;
    pNextHandler -> pmNextHandler = NULL;
    if (pNowHandler == NULL)
    {
        pmHalSpiPrivateContext -> mDispatcher.pHandlerListHead = pNextHandler;
    }
    else
    {
        pNowHandler -> pmNextHandler = pNextHandler;
    }
    pmHalSpiPrivateContext -> mDispatcher.u16HandlerCount++;
    return RES_SUCCESS;
}

int8_t HalSpiUnregisterEventHandler(tHalSpi *pmHalSpi, void *pContext, uint8_t u8CsIndex, tfpHalSpiEventCallback fpCallback)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (pmHalSpiPrivateContext -> mDispatcher.u16HandlerCount == 0 ||
        pmHalSpiPrivateContext -> mDispatcher.pHandlerListHead == NULL)
    {
        return RES_ERROR_EMPTY;
    }
    if (fpCallback == NULL)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    tHalSpiEventHandler *pNowHandler, *pNextHandler;
    pNowHandler = NULL;
    pNextHandler = pmHalSpiPrivateContext -> mDispatcher.pHandlerListHead;
    while (pNextHandler != NULL)
    {
        if (pNextHandler -> pContext == pContext &&
            pNextHandler -> u8CsIndex == u8CsIndex &&
            pNextHandler -> fpCallback == fpCallback)
        {
            break;
        }
        pNowHandler = pNextHandler;
        pNextHandler = pNextHandler -> pmNextHandler;
    }
    if (pNextHandler == NULL)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    else
    {
        if (pNowHandler == NULL)
        {
            pmHalSpiPrivateContext -> mDispatcher.pHandlerListHead = pNextHandler -> pmNextHandler;
        }
        else
        {
            pNowHandler -> pmNextHandler = pNextHandler -> pmNextHandler;
        }
        HalSpiReleaseEventHandlerPointer(pNextHandler);
    }
    pmHalSpiPrivateContext -> mDispatcher.u16HandlerCount--;
    return RES_SUCCESS;
}

void HAL_SPI_0_IRQHandler(void)
{
    if (pmHalSpiContext[kHAL_SPI_CHANNEL_0] != NULL)
    {
        HalSpiIrqHandler(pmHalSpiContext[kHAL_SPI_CHANNEL_0]);
    }
    return;
}

void HAL_SPI_1_IRQHandler(void)
{
    if (pmHalSpiContext[kHAL_SPI_CHANNEL_1] != NULL)
    {
        HalSpiIrqHandler(pmHalSpiContext[kHAL_SPI_CHANNEL_1]);
    }
    return;
}

void HAL_SPI_2_IRQHandler(void)
{
    if (pmHalSpiContext[kHAL_SPI_CHANNEL_2] != NULL)
    {
        HalSpiIrqHandler(pmHalSpiContext[kHAL_SPI_CHANNEL_2]);
    }
    return;
}

/* Local function prototypes ------------------------------------------------*/
static void HalSpiInit(void)
{
    if (bIsInitHalSpiResource == true)
    {
        return;
    }
    uint16_t u16Index;
    for (u16Index = 0; u16Index < kHAL_SPI_CHANNEL_COUNT; u16Index++)
    {
        pmHalSpiContext[u16Index] = NULL;
    }
    bIsInitHalSpiResource = true;
    DL_SPI_disable(HAL_SPI_0);
    DL_SPI_disable(HAL_SPI_1);
    DL_SPI_disable(HAL_SPI_2);
    DL_SPI_reset(HAL_SPI_0);
    DL_SPI_reset(HAL_SPI_1);
    DL_SPI_reset(HAL_SPI_2);
    delay_cycles(HAL_SPI_POWER_STARTUP_DELAY);
    return;
}

static bool HalSpiCheckSpiResoure(tHalSpi *pmHalSpi)
{
    if (bIsInitHalSpiResource == false ||
        pmHalSpi -> eChannel == kHAL_SPI_CHANNEL_COUNT ||
        pmHalSpiContext[pmHalSpi -> eChannel] != NULL)
    {
        return false;
    }
    return true;
}

static void HalSpiInitGpio(tHalSpi *pmHalSpi)
{
    uint8_t u8Index;
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    tHalSpiPinIomuxValue *pPinIomuxValue;
    tHalSpiCsGpio *pCsGpio;
    for (u8Index = 0; u8Index < kHAL_SPI_FUNCTION_PIN_COUNT; u8Index++)
    {
        pPinIomuxValue = &(pmHalSpiPrivateContext -> mPinIomuxValueMappings[u8Index]);
        if (pPinIomuxValue -> eIomuxPincm == 0 || pPinIomuxValue -> u32IomuxPf == 0)
        {
            continue;
        }
        if ((pmHalSpi -> mConfig.eMode == kHAL_SPI_MODE_MASTER && u8Index == kHAL_SPI_FUNCTION_PIN_POCI) ||
            (pmHalSpi -> mConfig.eMode != kHAL_SPI_MODE_MASTER && u8Index != kHAL_SPI_FUNCTION_PIN_POCI))
        {
            DL_GPIO_initPeripheralInputFunction(pPinIomuxValue -> eIomuxPincm, pPinIomuxValue -> u32IomuxPf);
        }
        else
        {
            DL_GPIO_initPeripheralOutputFunction(pPinIomuxValue -> eIomuxPincm, pPinIomuxValue -> u32IomuxPf);
        }
    }
    for (u8Index = 0; u8Index < HAL_SPI_CHIP_SELECT_SIZE; u8Index++)
    {
        if (pmHalSpiPrivateContext -> mCsGpio[u8Index].bIsValid == true)
        {
            pCsGpio = &(pmHalSpiPrivateContext -> mCsGpio[u8Index]);
            if (pmHalSpi -> mConfig.eMode == kHAL_SPI_MODE_MASTER)
            {
                DL_GPIO_initDigitalOutputFeatures(pCsGpio -> eIomuxPincm, DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_NONE, DL_GPIO_DRIVE_STRENGTH_LOW, DL_GPIO_HIZ_DISABLE);
                DL_GPIO_enableOutput(pCsGpio -> pGpioRegs, pCsGpio -> u32PinBit); 
                DL_GPIO_setPins(pCsGpio -> pGpioRegs, pCsGpio -> u32PinBit);
            }
            else
            {
                DL_GPIO_initDigitalInputFeatures(pCsGpio -> eIomuxPincm, DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_NONE, DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);
            }
        }
    }
    return;
}

static void HalSpiSetBitRate(tHalSpi *pmHalSpi, uint32_t u32BitRate)
{
    if (pmHalSpi -> mConfig.eMode == kHAL_SPI_MODE_MASTER)
    {
        tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
        uint32_t u32HalSpiClockFrequency, u32Scr;
        switch (gmHalSpiClockConfig.eClockSource)
        {
        case kHAL_SPI_CLOCK_SOURCE_BUSCLK:
            u32HalSpiClockFrequency = HAL_SPI_FREQ_MCLK;
            break;
        case kHAL_SPI_CLOCK_SOURCE_MFCLK:
            u32HalSpiClockFrequency = HAL_SPI_FREQ_MFCLK;
            break;
        case kHAL_SPI_CLOCK_SOURCE_LFCLK:
            u32HalSpiClockFrequency = HAL_SPI_FREQ_LFCLK;
            break;
        }
        u32Scr = ((uint32_t) ((double) u32HalSpiClockFrequency / (2 * u32BitRate))) - 1;
        if (u32Scr > HAL_SPI_BIT_RATE_SCR_MAX_VALUE)
        {
            u32Scr = HAL_SPI_BIT_RATE_SCR_MAX_VALUE;
        }
        //uint32_t u32SpiClock = u32HalSpiClockFrequency / ((1 + u32Scr) * 2);
        DL_SPI_setBitRateSerialClockDivider(pmHalSpiPrivateContext -> pmSpi, u32Scr);
    }
    return;
}

static void HalSpiInitSyscfg(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    DL_SPI_setClockConfig(pmHalSpiPrivateContext -> pmSpi, (DL_SPI_ClockConfig *) &gmHalSpiClockConfig);
    DL_SPI_init(pmHalSpiPrivateContext -> pmSpi, (DL_SPI_Config *) &(pmHalSpi -> mConfig));
    HalSpiSetBitRate(pmHalSpi, pmHalSpi -> u32BitRate);
    /* Set RX and TX FIFO threshold levels */
    DL_SPI_setFIFOThreshold(pmHalSpiPrivateContext -> pmSpi, DL_SPI_RX_FIFO_LEVEL_ONE_FRAME, DL_SPI_TX_FIFO_LEVEL_ONE_FRAME);
    DL_SPI_enable(pmHalSpiPrivateContext -> pmSpi);
    return;
}

static uint8_t HalSpiGetLastChipSelect(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    return pmHalSpiPrivateContext -> u8SelectCs;
}

static void HalSpiSetNextChipSelect(tHalSpi *pmHalSpi, uint8_t u8CsIndex)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    pmHalSpiPrivateContext -> u8SelectCs = u8CsIndex;
    return;
}

static void HalSpiControlChipSelect(tHalSpi *pmHalSpi, bool bEnable)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (pmHalSpi -> mConfig.eMode == kHAL_SPI_MODE_MASTER &&
        pmHalSpiPrivateContext -> u8SelectCs < HAL_SPI_CHIP_SELECT_SIZE)
    {
        tHalSpiCsGpio *pCsGpio = &(pmHalSpiPrivateContext -> mCsGpio[pmHalSpiPrivateContext -> u8SelectCs]);
        if (bEnable == false)
        {
            DL_GPIO_setPins(pCsGpio -> pGpioRegs, pCsGpio -> u32PinBit);
        }
        else
        {
            DL_GPIO_clearPins(pCsGpio -> pGpioRegs, pCsGpio -> u32PinBit); 
        }
    }
    if (bEnable == true)
    {
        HalSpiTriggerEvent(pmHalSpi, kHAL_SPI_EVENT_BUSY);
    }
    else
    {
        HalSpiTriggerEvent(pmHalSpi, kHAL_SPI_EVENT_DONE);
    }
    return;
}

static bool HalSpiHwIsBusy(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    return DL_SPI_isBusy(pmHalSpiPrivateContext -> pmSpi);
}

static bool HalSpiCheckDmaChannelConfig(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (pmHalSpiPrivateContext -> eDmaTxChannel == kBSP_DMA_CHANNEL_NONE ||
        pmHalSpiPrivateContext -> eDmaRxChannel == kBSP_DMA_CHANNEL_NONE)
    {
        return false;
    }
    return true;
}

static int8_t HalSpiCheckDmaIsReady(tHalSpi *pmHalSpi)
{
    if (HalSpiIsReady(pmHalSpi) == false)
    {
        return RES_ERROR_BUSY;
    }
    if (HalSpiCheckDmaChannelConfig(pmHalSpi) == false)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    return RES_SUCCESS;
}

static void HalSpiSetDmaTxEvent(tHalSpi *pmHalSpi, bool bEnable)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (bEnable == true)
    {
        DL_SPI_enableDMATransmitEvent(pmHalSpiPrivateContext -> pmSpi);
    }
    else
    {
        DL_SPI_disableDMATransmitEvent(pmHalSpiPrivateContext -> pmSpi);
    }
    return;
}

static uint32_t HalSpiGetDmaTxEvent(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    return DL_SPI_getEnabledDMATransmitEvent(pmHalSpiPrivateContext -> pmSpi);
}

static void HalSpiSetDmaRxEvent(tHalSpi *pmHalSpi, bool bEnable, uint32_t u32Interrupt)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (bEnable == true)
    {
        DL_SPI_enableDMAReceiveEvent(pmHalSpiPrivateContext -> pmSpi, u32Interrupt);
    }
    else
    {
        DL_SPI_disableDMAReceiveEvent(pmHalSpiPrivateContext -> pmSpi, u32Interrupt);
    }
    return;
}

static uint32_t HalSpiGetDmaRxEvent(tHalSpi *pmHalSpi, uint32_t u32Interrupt)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    return DL_SPI_getEnabledDMAReceiveEvent(pmHalSpiPrivateContext -> pmSpi, u32Interrupt);
}

static void HalSpiSetDmaEvent(tHalSpi *pmHalSpi, bool bEnable)
{
    uint32_t u32DmaEvent;
    u32DmaEvent = HalSpiGetDmaTxEvent(pmHalSpi);
    if ((bEnable == true && u32DmaEvent == 0) ||
        (bEnable == false && u32DmaEvent != 0))
    {
        HalSpiSetDmaTxEvent(pmHalSpi, bEnable);
    }
    u32DmaEvent = HalSpiGetDmaRxEvent(pmHalSpi, HAL_SPI_DMA_INTERRUPT_RX_EVENT);
    if ((bEnable == true && u32DmaEvent == 0) ||
        (bEnable == false && u32DmaEvent != 0))
    {
        HalSpiSetDmaRxEvent(pmHalSpi, bEnable, HAL_SPI_DMA_INTERRUPT_RX_EVENT);
    }
    return;
}

static void HalSpiSetInterrupt(tHalSpi *pmHalSpi, bool bEnable, uint32_t u32Interrupt)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (bEnable == true)
    {
        DL_SPI_enableInterrupt(pmHalSpiPrivateContext -> pmSpi, u32Interrupt);
    }
    else
    {
        DL_SPI_disableInterrupt(pmHalSpiPrivateContext -> pmSpi, u32Interrupt);
    }
    return;
}

static uint32_t HalSpiGetInterrupt(tHalSpi *pmHalSpi, uint32_t u32Interrupt)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    return DL_SPI_getEnabledInterrupts(pmHalSpiPrivateContext -> pmSpi, u32Interrupt);
}

static void HalSpiSetDmaInterrupt(tHalSpi *pmHalSpi, bool bEnable)
{
    uint32_t u32DmaInterrupt = HalSpiGetInterrupt(pmHalSpi, HAL_SPI_DMA_INTERRUPT_TXRX_VALUE);
    if ((bEnable == true && (u32DmaInterrupt & HAL_SPI_DMA_INTERRUPT_TXRX_VALUE) != HAL_SPI_DMA_INTERRUPT_TXRX_VALUE) ||
        (bEnable == false && (u32DmaInterrupt & HAL_SPI_DMA_INTERRUPT_TXRX_VALUE) == HAL_SPI_DMA_INTERRUPT_TXRX_VALUE))
    {
        HalSpiSetInterrupt(pmHalSpi, bEnable, HAL_SPI_DMA_INTERRUPT_TXRX_VALUE);
    }
    return;
}

static void HalSpiInitDmaChannel(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (DL_DMA_isChannelEnabled(DMA, pmHalSpiPrivateContext -> eDmaTxChannel) == false ||
        DL_DMA_isChannelEnabled(DMA, pmHalSpiPrivateContext -> eDmaRxChannel) == false)
    {
        uint8_t u8TxTrigger, u8RxTrigger;
        switch (pmHalSpi -> eChannel)
        {
        case kHAL_SPI_CHANNEL_0:
            u8TxTrigger = HAL_SPI_0_DMA_TX_TRIGGER;
            u8RxTrigger = HAL_SPI_0_DMA_RX_TRIGGER;
            break;
        case kHAL_SPI_CHANNEL_1:
            u8TxTrigger = HAL_SPI_1_DMA_TX_TRIGGER;
            u8RxTrigger = HAL_SPI_1_DMA_RX_TRIGGER;
            break;
        case kHAL_SPI_CHANNEL_2:
            u8TxTrigger = HAL_SPI_2_DMA_TX_TRIGGER;
            u8RxTrigger = HAL_SPI_2_DMA_RX_TRIGGER;
            break;
        default:
            return;
        }
        DL_DMA_Config mDmaConfig;
        mDmaConfig = mDmaDefaultConfig[kHAL_SPI_DMA_MODE_TX];
        mDmaConfig.trigger = u8TxTrigger;
        DL_DMA_initChannel(DMA, pmHalSpiPrivateContext -> eDmaTxChannel, &mDmaConfig);
        mDmaConfig = mDmaDefaultConfig[kHAL_SPI_DMA_MODE_RX];
        mDmaConfig.trigger = u8RxTrigger;
        DL_DMA_initChannel(DMA, pmHalSpiPrivateContext -> eDmaRxChannel, &mDmaConfig);
    }
    return;
}

static void HalSpiSetDmaChannel(uint32_t u32DmaChannel, bool bEnable)
{
    if (u32DmaChannel != kBSP_DMA_CHANNEL_NONE &&
        bEnable != HalSpiGetDmaChannelStatus(u32DmaChannel))
    {
        if (bEnable)
        {
            DL_DMA_enableChannel(DMA, u32DmaChannel);
        }
        else
        {
            DL_DMA_disableChannel(DMA, u32DmaChannel);
        }
    }
    return;
}

static bool HalSpiGetDmaChannelStatus(uint32_t u32DmaChannel)
{
    if (u32DmaChannel != kBSP_DMA_CHANNEL_NONE)
    {
        return DL_DMA_isChannelEnabled(DMA, u32DmaChannel);
    }
    return false;
}

static void HalSpiSetNvicIrq(tHalSpi *pmHalSpi, bool bEnable)
{
    int32_t i32IntIrqN;
    switch (pmHalSpi -> eChannel)
    {
    case kHAL_SPI_CHANNEL_0:
        i32IntIrqN = kHAL_SPI_CHANNEL_0_IRQ;
        break;
    case kHAL_SPI_CHANNEL_1:
        i32IntIrqN = kHAL_SPI_CHANNEL_1_IRQ;
        break;
    case kHAL_SPI_CHANNEL_2:
        i32IntIrqN = kHAL_SPI_CHANNEL_2_IRQ;
        break;
    default:
        i32IntIrqN = 0;
        return;
    }
    if (bEnable == HalSpiGetNvicIrqStatus(pmHalSpi))
    {
        return;
    }
    if (bEnable == true)
    {
        NVIC_EnableIRQ(i32IntIrqN);
    }
    else
    {
        NVIC_DisableIRQ(i32IntIrqN);
    }
    return;
}

static uint32_t HalSpiGetNvicIrqStatus(tHalSpi *pmHalSpi)
{
    int32_t i32IntIrqN;
    switch (pmHalSpi -> eChannel)
    {
    case kHAL_SPI_CHANNEL_0:
        i32IntIrqN = NVIC_GetEnableIRQ(kHAL_SPI_CHANNEL_0_IRQ);
        break;
    case kHAL_SPI_CHANNEL_1:
        i32IntIrqN = NVIC_GetEnableIRQ(kHAL_SPI_CHANNEL_1_IRQ);
        break;
    case kHAL_SPI_CHANNEL_2:
        i32IntIrqN = NVIC_GetEnableIRQ(kHAL_SPI_CHANNEL_2_IRQ);
        break;
    default:
        i32IntIrqN = 0;
    }
    return i32IntIrqN;
}

static void HalSpiOpenDma(tHalSpi *pmHalSpi, uint8_t *pu8TxData, uint16_t u16TxSize, uint8_t *pu8RxData, uint16_t u16RxSize)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    HalSpiSetDmaEvent(pmHalSpi, true);
    HalSpiSetDmaInterrupt(pmHalSpi, true);
    HalSpiInitDmaChannel(pmHalSpi);
    DL_DMA_setSrcAddr(DMA, pmHalSpiPrivateContext -> eDmaRxChannel, (uint32_t) (&(pmHalSpiPrivateContext -> pmSpi -> RXDATA)));
    DL_DMA_setDestAddr(DMA, pmHalSpiPrivateContext -> eDmaRxChannel, (uint32_t) pu8RxData);
    DL_DMA_setTransferSize(DMA, pmHalSpiPrivateContext -> eDmaRxChannel, u16RxSize);
    DL_DMA_setSrcAddr(DMA, pmHalSpiPrivateContext -> eDmaTxChannel, (uint32_t) pu8TxData);
    DL_DMA_setDestAddr(DMA, pmHalSpiPrivateContext -> eDmaTxChannel, (uint32_t) (&(pmHalSpiPrivateContext -> pmSpi -> TXDATA)));
    DL_DMA_setTransferSize(DMA, pmHalSpiPrivateContext -> eDmaTxChannel, u16TxSize);
    HalSpiSetDmaChannel(pmHalSpiPrivateContext -> eDmaRxChannel, true);
    HalSpiSetNvicIrq(pmHalSpi, true);
    HalSpiControlChipSelect(pmHalSpi, true);
    HalSpiSetDmaChannel(pmHalSpiPrivateContext -> eDmaTxChannel, true);
    return;
}

static void HalSpiIrqHandler(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    //eTypeHalGpioPin ePin;
    switch (DL_SPI_getPendingInterrupt(pmHalSpiPrivateContext -> pmSpi))
    {
    case DL_SPI_IIDX_DMA_DONE_TX:
        //ePin = kHAL_GPIO_PIN_22;
        //HalGpioTogglePin(kHAL_GPIOB, ePin);
        pmHalSpiPrivateContext -> eTaskDone |= kHAL_SPI_TASK_DONE_DMA_SEND_TRIG;
        break;
    case DL_SPI_IIDX_TX_EMPTY:
        break;
    case DL_SPI_IIDX_DMA_DONE_RX:
        //ePin = kHAL_GPIO_PIN_20;
        //HalGpioTogglePin(kHAL_GPIOB, ePin);
        pmHalSpiPrivateContext -> eTaskDone |= kHAL_SPI_TASK_DONE_DMA_RECV_TRIG;
        break;
    default:
        break;
    }
    if ((pmHalSpiPrivateContext -> eTaskMode == kHAL_SPI_TASK_MODE_DMA_SEND && pmHalSpiPrivateContext -> eTaskDone == kHAL_SPI_TASK_DONE_DMA_SEND_FINISH) ||
        (pmHalSpiPrivateContext -> eTaskMode == kHAL_SPI_TASK_MODE_DMA_RECV && pmHalSpiPrivateContext -> eTaskDone == kHAL_SPI_TASK_DONE_DMA_RECV_FINISH) ||
        (pmHalSpiPrivateContext -> eTaskMode == kHAL_SPI_TASK_MODE_DMA_BOTH && pmHalSpiPrivateContext -> eTaskDone == kHAL_SPI_TASK_DONE_DMA_BOTH_FINISH))
    {
        pmHalSpiPrivateContext -> pu8TwiceRxData = NULL;
        pmHalSpiPrivateContext -> u16TwiceRxSize = 0;
        HalSpiControlChipSelect(pmHalSpi, false);
        pmHalSpiPrivateContext -> eTaskMode = kHAL_SPI_TASK_MODE_NONE;
        pmHalSpiPrivateContext -> eTaskDone = kHAL_SPI_TASK_DONE_NONE;
    }
    //HalGpioTogglePin(kHAL_GPIOB, ePin);
    return;
}

static void HalSpiTriggerEvent(tHalSpi *pmHalSpi, eTypeHalSpiEvent eEvent)
{
    if (pmHalSpi -> fpSpiEvent != NULL) {
        pmHalSpi -> fpSpiEvent(eEvent);
    }
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    uint8_t u8Index = HalSpiGetLastChipSelect(pmHalSpi);
    tHalSpiEventHandler *pHandler = pmHalSpiPrivateContext -> mDispatcher.pHandlerListHead;
    while (pHandler != NULL)
    {
        if (pHandler -> u8CsIndex == u8Index && pHandler -> fpCallback != NULL)
        {
            pHandler -> fpCallback(pHandler -> pContext, HalSpiGetLastChipSelect(pmHalSpi), eEvent);
        }
        pHandler = pHandler -> pmNextHandler;
    }
    return;
}

static void HalSpiCheckSpiTask(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    if (pmHalSpiPrivateContext -> eTaskMode == kHAL_SPI_TASK_MODE_DMA_BOTH &&
        pmHalSpiPrivateContext -> eTaskDone == kHAL_SPI_TASK_DONE_DMA_SEND_FINISH)
    {
        pmHalSpiPrivateContext -> eTaskDone = kHAL_SPI_TASK_DONE_DMA_SEND_RECV_DONE;
        if (pmHalSpiPrivateContext -> pu8TwiceTxData != NULL && pmHalSpiPrivateContext -> u16TwiceTxSize != 0)
        {
            HalSpiOpenDma(pmHalSpi, pmHalSpiPrivateContext -> pu8TwiceTxData, pmHalSpiPrivateContext -> u16TwiceTxSize, pmHalSpiPrivateContext -> pu8TwiceRxData, pmHalSpiPrivateContext -> u16TwiceRxSize);
        }
        else
        {
            for (uint16_t u16Index = 0; u16Index < pmHalSpiPrivateContext -> u16TwiceRxSize; u16Index++)
            {
                pmHalSpiPrivateContext -> u8DmaBuf[u16Index] = 0xFF;
            }
            HalSpiOpenDma(pmHalSpi, pmHalSpiPrivateContext -> u8DmaBuf, pmHalSpiPrivateContext -> u16TwiceRxSize, pmHalSpiPrivateContext -> pu8TwiceRxData, pmHalSpiPrivateContext -> u16TwiceRxSize);
        }
    }
    return;
}

static tHalSpiEventHandler *HalSpiGetEventHandlerPointer(tHalSpi *pmHalSpi)
{
    tHalSpiPrivate *pmHalSpiPrivateContext = &mHalSpiPrivateContext[pmHalSpi -> eChannel];
    for (uint8_t u8Index = 0; u8Index < HAL_SPI_CALLBACK_HANDLER_SIZE; u8Index++)
    {
        if (pmHalSpiPrivateContext -> mHandlers[u8Index].pContext == NULL &&
            pmHalSpiPrivateContext -> mHandlers[u8Index].u8CsIndex == 0xFF &&
            pmHalSpiPrivateContext -> mHandlers[u8Index].fpCallback == NULL &&
            pmHalSpiPrivateContext -> mHandlers[u8Index].pmNextHandler == NULL)
        {
            return &(pmHalSpiPrivateContext -> mHandlers[u8Index]);
        }
    }
    return NULL;
}

static void HalSpiReleaseEventHandlerPointer(tHalSpiEventHandler *pTarget)
{
    pTarget -> pContext = NULL;
    pTarget -> u8CsIndex = 0xFF;
    pTarget -> fpCallback = NULL;
    pTarget -> pmNextHandler = NULL;
    return;
}

/* Global test function prototypes ------------------------------------------*/
#ifdef HAL_SPI_ENABLE_SPI_EXAMPLE
static tHalSpi gmHalSpi0Config;
static uint8_t gmu8RxPacket[12] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
static uint8_t gmu8TxWCFGA[12] = {0x00, 0x01, 0x3D, 0x6E, 0x80, 0x00, 0x00, 0x55, 0x00, 0x00, 0x00, 0x7B};
static uint8_t gmu8TxRCCFGA[12] = {0x00, 0x02, 0x2B, 0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF};
static uint8_t gmu8TxWCFGB[12] = {0x00, 0x24, 0xB1, 0x9E, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x7D};
static uint8_t gmu8TxRCCFGB[12] = {0x00, 0x26, 0x2C, 0xC8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF};
static uint8_t gmu8TxRGA[4] = {0x00, 0x04, 0x07, 0xC2};
static uint8_t gmu8TxRGB[4] = {0x00, 0x06, 0x9A, 0x94};
static uint8_t gmu8TxRGC[4] = {0x00, 0x08, 0x5E, 0x52};
static void HalSpiExampleEvent(eTypeHalSpiEvent eEvent)
{
    switch (eEvent)
    {
    case kHAL_SPI_EVENT_DONE:
        break;
    case kHAL_SPI_EVENT_BUSY:
        break;
    case kHAL_SPI_EVENT_ERROR:
        break;
    }
    return;
}
void HalSpiExampleInitSpi(void)
{
    UART_INIT();
    gmHalSpi0Config.eChannel = kHAL_SPI_CHANNEL_0;
    gmHalSpi0Config.mConfig.eMode = kHAL_SPI_MODE_MASTER;
    gmHalSpi0Config.mConfig.eFrameFormat = kHAL_SPI_FRAME_FORMAT_3WIRE_SPO0_SPH0;
    gmHalSpi0Config.mConfig.eParity = kHAL_SPI_PARITY_NONE;
    gmHalSpi0Config.mConfig.eFrameSize = kHAL_SPI_FRAME_SIZE_BITS_8;
    gmHalSpi0Config.mConfig.eBitOrder = kHAL_SPI_BIT_ORDER_MSB_FIRST;
    gmHalSpi0Config.mConfig.eChipSelect = kHAL_SPI_CHIP_SELECT_NONE;
    gmHalSpi0Config.u32BitRate = 1000000;
    gmHalSpi0Config.fpSpiEvent = &HalSpiExampleEvent;
    HalSpiOpen(&gmHalSpi0Config);
    return;
}
void HalSpiExampleTransmit(void)
{
    HalSpiSendRecvBlockingFullDuplex(&gmHalSpi0Config, gmu8TxWCFGA, NULL, 12, 0);
    HalSpiSendRecvBlockingFullDuplex(&gmHalSpi0Config, gmu8TxRCCFGA, gmu8RxPacket, 12, 0);
    HalSpiSendRecvBlockingFullDuplex(&gmHalSpi0Config, gmu8TxWCFGB, NULL, 12, 0);
    HalSpiSendRecvBlockingFullDuplex(&gmHalSpi0Config, gmu8TxRCCFGB, gmu8RxPacket, 12, 0);

    HalSpiSendRecvBlockingHalfDuplex(&gmHalSpi0Config, gmu8TxWCFGA, 12, NULL, 0, 0);
    HalSpiSendRecvBlockingHalfDuplex(&gmHalSpi0Config, gmu8TxRCCFGA, 4, gmu8RxPacket, 8, 0);
    HalSpiSendRecvBlockingHalfDuplex(&gmHalSpi0Config, gmu8TxWCFGB, 12, NULL, 0, 0);
    HalSpiSendRecvBlockingHalfDuplex(&gmHalSpi0Config, gmu8TxRCCFGB, 4, gmu8RxPacket, 8, 0);

    HalSpiSendRecvDmaFullDuplex(&gmHalSpi0Config, gmu8TxWCFGA, NULL, 12, 0);
    while (HalSpiIsReady(&gmHalSpi0Config) == false);
    HalSpiSendRecvDmaFullDuplex(&gmHalSpi0Config, gmu8TxRCCFGA, gmu8RxPacket, 12, 0);
    while (HalSpiIsReady(&gmHalSpi0Config) == false);
    HalSpiSendRecvDmaFullDuplex(&gmHalSpi0Config, gmu8TxWCFGB, NULL, 12, 0);
    while (HalSpiIsReady(&gmHalSpi0Config) == false);
    HalSpiSendRecvDmaFullDuplex(&gmHalSpi0Config, gmu8TxRCCFGB, gmu8RxPacket, 12, 0);
    while (HalSpiIsReady(&gmHalSpi0Config) == false);

    HalSpiSendBlockingRecvDma(&gmHalSpi0Config, gmu8TxWCFGA, 12, NULL, 0, 0);
    while (HalSpiIsReady(&gmHalSpi0Config) == false);
    HalSpiSendBlockingRecvDma(&gmHalSpi0Config, gmu8TxRCCFGA, 4, gmu8RxPacket, 8, 0);
    while (HalSpiIsReady(&gmHalSpi0Config) == false);
    HalSpiSendBlockingRecvDma(&gmHalSpi0Config, gmu8TxWCFGB, 12, NULL, 0, 0);
    while (HalSpiIsReady(&gmHalSpi0Config) == false);
    HalSpiSendBlockingRecvDma(&gmHalSpi0Config, gmu8TxRCCFGB, 4, gmu8RxPacket, 8, 0);
    while (HalSpiIsReady(&gmHalSpi0Config) == false);
    return;
}
#endif

#ifdef HAL_SPI_ENABLE_UART
#define UART_IBRD_40_MHZ_9600_BAUD               (260)
#define UART_FBRD_40_MHZ_9600_BAUD               (11)
#define UART_IBRD_32_MHZ_9600_BAUD               (208)
#define UART_FBRD_32_MHZ_9600_BAUD               (21)
static const DL_UART_ClockConfig mUart0ClockConfig = {
    .clockSel    = DL_UART_CLOCK_BUSCLK,
    .divideRatio = DL_UART_CLOCK_DIVIDE_RATIO_1
};
static const DL_UART_Config mUart0Config = {
    .mode        = DL_UART_MODE_NORMAL,
    //.direction   = DL_UART_DIRECTION_TX_RX,
    .direction   = DL_UART_DIRECTION_TX,
    .flowControl = DL_UART_FLOW_CONTROL_NONE,
    .parity      = DL_UART_PARITY_NONE,
    .wordLength  = DL_UART_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_STOP_BITS_ONE
};
void DrvSyscfgDlUart0Init(void)
{
    DL_UART_reset(UART0);
    DL_UART_enablePower(UART0);
    DL_GPIO_initPeripheralOutputFunction(IOMUX_PINCM1, IOMUX_PINCM1_PF_UART0_TX);

    DL_UART_setClockConfig(UART0, (DL_UART_ClockConfig *) &mUart0ClockConfig);
    DL_UART_init(UART0, (DL_UART_Config *) &mUart0Config);
    DL_UART_setOversampling(UART0, DL_UART_OVERSAMPLING_RATE_16X);
    DL_UART_setBaudRateDivisor(UART0, UART_IBRD_40_MHZ_9600_BAUD, UART_FBRD_40_MHZ_9600_BAUD);
    //DL_UART_setBaudRateDivisor(UART0, UART_IBRD_32_MHZ_9600_BAUD, UART_FBRD_32_MHZ_9600_BAUD);
    DL_UART_enable(UART0);
    return;
}
void DrvUart0SendMsg(const char *strFormatMsg, ...)
{
    char c8Buf[128];
    va_list args;
    va_start(args, strFormatMsg);
    vsnprintf(c8Buf, sizeof(c8Buf), strFormatMsg, args);
    va_end(args);
    char *pPtr = c8Buf;
    while (*pPtr)
    {
        while (DL_UART_Extend_isBusy(UART0) || DL_UART_isTXFIFOFull(UART0));
        //DL_UART_transmitData(UART0, *pPtr++);
        DL_UART_transmitDataBlocking(UART0, *pPtr++);
        while (DL_UART_isTXFIFOEmpty(UART0) == false);
    }
    return;
}
#endif