/**
  ******************************************************************************
  * @file        AppSerialCanDavinciParameter.c
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2022/03/07
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

#include "HalAfe.h"

#include "HalCan.h"

#include "LibSoftwareTimerHandler.h"
#include "AppSerialCanDavinci.h"
#if MAO_DISSABLE
#include "ApiSysPar.h"
#include "ApiEkfPar.h"
#include "ApiFu.h"
#include "AppGauge.h"
#include "ApiPackSnPar.h"
#endif


void appSerialCanDavinciSendTextMessage(char *str);
#define	appSerialCanDavinciParDebugMsg(str)	//appSerialCanDavinciSendTextMessage(str)

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define	canParScuId()	appProjectGetScuId()

/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint8_t	IdleCount = 0;

static uint8_t	StringMessageBuffer[MAX_NOTE_MESSAGE_STRING_ITEM + 2];


/* Private function prototypes -----------------------------------------------*/
static uint8_t isParWritable(void)
{
	return IdleCount;
}

static void magicCodeIdleSwTimerHandler(__far void *dest, uint16_t evt, void *vDataPtr)
{
	if(evt & kLIB_SW_TIMER_EVT_1_MS)
		return;
	else if(evt & kLIB_SW_TIMER_EVT_1_S)
	{
		if(IdleCount == 0)
			LibSwTimerClose(magicCodeIdleSwTimerHandler, 0);
		else
		{
			IdleCount--;
			if(IdleCount == 0)
			{
				LibSwTimerClose(magicCodeIdleSwTimerHandler, 0);
				appSerialCanDavinciParDebugMsg("Close Magic Time");
			}
		}
	}
}

static void DavinciParameterMagicCode(tHalCanFrame *pCanPkg){
	tHalCanFrame	CanPkg;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_MAGIC_CODE);
		CanPkg.u8Dlc = 1;
		CanPkg.tUnionData.u8Data[0] = IdleCount;
		
		appSerialCanDavinciParDebugMsg("Read Magic Code Idle Time");
	}
	else
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_MAGIC_CODE);
		CanPkg.u8Dlc = 1;
		CanPkg.tUnionData.u8Data[0] = 0;
		if(memcmp(&pCanPkg->tUnionData.u8Data ,"EssWrPar", 8) == 0)
		{
			if(IdleCount == 0)
			{
				LibSwTimerOpen(magicCodeIdleSwTimerHandler, 0);
				appSerialCanDavinciParDebugMsg("Open Magic Time");
			}
			IdleCount = 20;
			CanPkg.tUnionData.u8Data[0] = 20;
		}
	}		
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterHwVersion(tHalCanFrame *pCanPkg){
	tHalCanFrame	CanPkg;
	tUnion32Bits	version;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_HW_VERSION);
		CanPkg.u8Dlc = 4;
		version.u32 = apiSysParGetHwVersion();
		CanPkg.tUnionData.u8Data[0] = version.u8[0];
		CanPkg.tUnionData.u8Data[1] = version.u8[1];
		CanPkg.tUnionData.u8Data[2] = version.u8[2];
		CanPkg.tUnionData.u8Data[3] = version.u8[3];
		
		//appSerialCanDavinciParDebugMsg("Read Hw Version");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_HW_VERSION);
		CanPkg.u8Dlc = 0;

		version.u32 =  GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
		#if MAO_DISSABLE
		apiSysParSetHwVersion(version.u32);
		#endif
		appSerialCanDavinciParDebugMsg("Write Hw Version");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterFwVersion(tHalCanFrame *pCanPkg){
	tHalCanFrame	CanPkg;
	tUnion32Bits	version;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_FW_VERSION);
		CanPkg.u8Dlc = 3;
		version.u32 = apiFuGetFwVersion();
		CanPkg.tUnionData.u8Data[0] = version.u8[0];
		CanPkg.tUnionData.u8Data[1] = version.u8[1];
		CanPkg.tUnionData.u8Data[2] = version.u8[2];
		
		appSerialCanDavinciParDebugMsg("Read Fw Version");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_FW_VERSION);
		CanPkg.u8Dlc = 0;
		appSerialCanDavinciParDebugMsg("Can't write Fw Version");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);

}



static void DavinciCanParameterFwInternalVersion(tHalCanFrame *pCanPkg){
	tHalCanFrame	CanPkg;
	tUnion32Bits	version;
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_FW_INTERNAL_VERSION);
		
		CanPkg.u8Dlc = 4;
		version.u32 = apiFuGetFwInternalVersion();
		CanPkg.tUnionData.u8Data[0] = version.u8[0];
		CanPkg.tUnionData.u8Data[1] = version.u8[1];
		CanPkg.tUnionData.u8Data[2] = version.u8[2];
		CanPkg.tUnionData.u8Data[3] = version.u8[3];

		appSerialCanDavinciParDebugMsg("Read Fw Internal Version");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_FW_INTERNAL_VERSION);
		CanPkg.u8Dlc = 0;
		appSerialCanDavinciParDebugMsg("Can't write Fw Version");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);

}

static void DavinciCanParameterFwBuildDateTime(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	BuildDate,BuildTime;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_FW_BUILD_DATE_TIME);
		CanPkg.u8Dlc = 7;
//		uint32_t apiFuGetFwVersion(void);
#if MAO_DISSABLE
		BuildDate.u32 = apiFuGetFwBuildDate();
		BuildTime.u32 = apiFuGetFwBuildTime();
#endif

		CanPkg.tUnionData.u8Data[3] = BuildDate.u8[3];
		CanPkg.tUnionData.u8Data[2] = BuildDate.u8[2];
		CanPkg.tUnionData.u8Data[1] = BuildDate.u8[1];
		CanPkg.tUnionData.u8Data[0] = BuildDate.u8[0];
		
		CanPkg.tUnionData.u8Data[6] = BuildTime.u8[2];
		CanPkg.tUnionData.u8Data[5] = BuildTime.u8[1];
		CanPkg.tUnionData.u8Data[4] = BuildTime.u8[0];
		
		appSerialCanDavinciParDebugMsg("Read Fw date time");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_FW_BUILD_DATE_TIME);
		CanPkg.u8Dlc = 0;
		appSerialCanDavinciParDebugMsg("Can't write date time");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);

}

static void DavinciCanParameterZeroCurrent(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion16Bits	current;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_ZERO_CURRENT);
		CanPkg.u8Dlc = 4;
		current.u16 = apiSysParGetZeroCurrentValue();
		CanPkg.tUnionData.u8Data[0] = current.u8[0];
		CanPkg.tUnionData.u8Data[1] = current.u8[1];
		current.u16 = apiSysParGetMinChargeCurrentValue();
		CanPkg.tUnionData.u8Data[2] = current.u8[0];
		CanPkg.tUnionData.u8Data[3] = current.u8[1];

		appSerialCanDavinciParDebugMsg("Read zero current");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_ZERO_CURRENT);
		CanPkg.u8Dlc = 0;
		current.u8[0] = pCanPkg->tUnionData.u8Data[0];
		current.u8[1] = pCanPkg->tUnionData.u8Data[1];
		#if MAO_DISSABLE
		apiSysParSetZeroCurrentValue(current.u16);
		#endif
		
		current.u8[0] = pCanPkg->tUnionData.u8Data[2];
		current.u8[1] = pCanPkg->tUnionData.u8Data[3];
		#if MAO_DISSABLE
		apiSysParSetMinChargeCurrentValue(current.u16);
		#endif
		
		appSerialCanDavinciParDebugMsg("write zero current");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}


static void DavinciCanParameterPreDischargeTime(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion16Bits	time,voltage;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_PRE_DHG_TIME);
		CanPkg.u8Dlc = 4;
		time.u16 = apiSysParGetPreDischargeTime();
		voltage.u16 = apiSysParGetPreDischargeThreshold();
			
		CanPkg.tUnionData.u8Data[0] = time.u8[0];
		CanPkg.tUnionData.u8Data[1] = time.u8[1];
		CanPkg.tUnionData.u8Data[2] = voltage.u8[0];
		CanPkg.tUnionData.u8Data[3] = voltage.u8[1];
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_PRE_DHG_TIME);
									
		CanPkg.u8Dlc = 0;
		time.u8[0] = pCanPkg->tUnionData.u8Data[0];
		time.u8[1] = pCanPkg->tUnionData.u8Data[1];
		voltage.u8[0] = pCanPkg->tUnionData.u8Data[2];
		voltage.u8[1] = pCanPkg->tUnionData.u8Data[3];
		#if MAO_DISSABLE
		apiSysParSetPreDischargeTime(time.u16);
		apiSysParSetPreDischargeThreshold(voltage.u16);
		#endif
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterRelayOnDiffVoltage(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion16Bits	voltage;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_MOS_ON_DIFF_VOLTAGE);
		CanPkg.u8Dlc = 2;
		#if MAO_DISSABLE
		voltage.u16 = apiSysParGetRelayOnDiffVoltage();
		#endif
		CanPkg.tUnionData.u8Data[0] = voltage.u8[0];
		CanPkg.tUnionData.u8Data[1] = voltage.u8[1];
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_MOS_ON_DIFF_VOLTAGE);
		CanPkg.u8Dlc = 0;
		voltage.u8[0] = pCanPkg->tUnionData.u8Data[0];
		voltage.u8[1] = pCanPkg->tUnionData.u8Data[1];
		#if MAO_DISSABLE
		apiSysParSetRelayOnDiffVoltage(voltage.u16);
		#endif
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}
                   			
static void DavinciCanParameterDesignedCapacity(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_DESIGNED_CAPACITY);
		CanPkg.u8Dlc = 4;
		#if MAO_DISSABLE
		Lbyte.u32 = apiSysParGetDesignedCapacity();
		#endif
		
		CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
		CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
		CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
		CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];

		appSerialCanDavinciParDebugMsg("Read designed capacity");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_DESIGNED_CAPACITY);
		CanPkg.u8Dlc = 0;
		Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
		#if MAO_DISSABLE
		apiSysParSetDesignedCapacity(Lbyte.u32);
		#endif
		appSerialCanDavinciParDebugMsg("write designed capacity");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterRellayActiveFlag(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion16Bits	Ibyte;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_RELAY_ACTIVE_FLAG);
		CanPkg.u8Dlc = 2;
		Ibyte.u16 = apiSysParGetSystemActiveFlag();
		CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
		CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];

		appSerialCanDavinciParDebugMsg("Read Relay Active Flag");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_RELAY_ACTIVE_FLAG);
		CanPkg.u8Dlc = 0;		
		Ibyte.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		#if MAO_DISSABLE
		apiSysParSetSystemActiveFlag(Ibyte.u16);
		#endif
		appSerialCanDavinciParDebugMsg("write Relay Active Flag");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}       

static void DavinciCanParameterMaxCurrent(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_MAX_CHG_DHG_CURRENT);
		CanPkg.u8Dlc = 4;
		#if MAO_DISSABLE
		apiSysParGetMaxCurrentValue(&ProtectPar);

		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[1];
		#endif

		appSerialCanDavinciParDebugMsg("Read Max Current");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_MAX_CHG_DHG_CURRENT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);

		apiSysParSetMaxCurrentValue(&ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("write max current");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);		
}
static void DavinciCanParameterPeakCurrent(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_PEAK_CURRENT);
		CanPkg.u8Dlc = 6;
		#if MAO_DISSABLE
		apiSysParGetMaxPeakCurrentValue(&ProtectPar);

		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Peak Current");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_PEAK_CURRENT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[3]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[5];

		apiSysParSetMaxPeakCurrentValue(&ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Peak Current");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void DavinciCanParameterRateVoltage(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_RATE_VOLTAGE);
		CanPkg.u8Dlc = 6;
		#if MAO_DISSABLE
		apiSysParGetRateVoltage(&ProtectPar);

		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[1];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		#endif

		appSerialCanDavinciParDebugMsg("Read Rate Voltae");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_RATE_VOLTAGE);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);

		apiSysParSetRateVoltage(&ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Rate Voltage");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterFullChargeCondition(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_TAPER_CURRENT);
		CanPkg.u8Dlc = 5;
		#if MAO_DISSABLE
		apiSysParGetFullChargeCondition(&ProtectPar);

		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[1];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];

		#endif
		appSerialCanDavinciParDebugMsg("Read full charge condition");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_TAPER_CURRENT);																		
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[4];

		apiSysParSetFullChargeCondition(&ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("write full charge condition");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}            

static void DavinciCanParameterFlatVoltage(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_FLAT_VOLTAGE);
		CanPkg.u8Dlc = 4;
		#if MAO_DISSABLE
		apiSysParGetFlatVoltage(&ProtectPar);

		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[1];
		#endif
		appSerialCanDavinciParDebugMsg("Read flat voltage");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_FLAT_VOLTAGE);																		
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);

		apiSysParSetFlatVoltage(&ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("write flat voltage");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}           
  
static void DavinciCanParameterTerminateVoltage(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion16Bits	voltage;
		
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_TERMINATE_VOLTAGE);
		CanPkg.u8Dlc = 2;
		voltage.u16 = apiSysParGetTerminateVoltage();

		CanPkg.tUnionData.u8Data[0] = voltage.u8[0];
		CanPkg.tUnionData.u8Data[1] = voltage.u8[1];
		appSerialCanDavinciParDebugMsg("Read terminate voltage");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_TERMINATE_VOLTAGE);																		
		CanPkg.u8Dlc = 0;
		voltage.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		apiSysParSetTerminateVoltage(voltage.u16);
		appSerialCanDavinciParDebugMsg("write  terminate voltage");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
} 

static void DavinciCanParameterAfeCommunication(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
		
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_COMMUNICATION);
		CanPkg.u8Dlc = 3;

		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		if(pCanPkg->tUnionData.u8Data[0] == 0x00)
		{
			#if MAO_DISSABLE
			apiSysParGetAfeCommTime(&ProtectPar);
			appSerialCanDavinciParDebugMsg("Read AFE comm.");			
			CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
			CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
			#endif
		}
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_COMMUNICATION);																		
		CanPkg.u8Dlc = 0;
		if(pCanPkg->tUnionData.u8Data[0] == 0x00)
		{
			#if MAO_DISSABLE
			ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[1];
			ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
			apiSysParSetAfeCommTime(&ProtectPar);
			#endif
			
			appSerialCanDavinciParDebugMsg("Write AFE comm.");			
		}
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
} 

static void DavinciCanParameterInsulationResistance(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
		
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_INSULATION_RESISTANCE);
		CanPkg.u8Dlc = 8;
		#if MAO_DISSABLE
		apiSysParGetInsulationResistance(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];

		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[1];
		
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		CanPkg.tUnionData.u8Data[7] = ProtectPar.RTime.u8[1];
		#endif
		appSerialCanDavinciParDebugMsg("Read I.R.");			
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_INSULATION_RESISTANCE);																		
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[6]);
		
		apiSysParSetInsulationResistance(&ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write I.R.");			
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
} 



static void DavinciCanParameterQmax(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
		
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_QMAX);
		CanPkg.u8Dlc = 4;
		Lbyte.u32 = appGaugeGetQmax();
		CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
		CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
		CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
		CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
		appSerialCanDavinciParDebugMsg("Read Qmax");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_QMAX);																		
		CanPkg.u8Dlc = 0;
		Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
		appGaugeSetQmax(Lbyte.u32);
		apiSysParSetQmax(Lbyte.u32);

		appSerialCanDavinciParDebugMsg("write  Qmax");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}                   			          

static void DavinciCanParameterRM(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
		
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_RM);
		CanPkg.u8Dlc = 4;
		Lbyte.u32 = appGaugeGetRM();
		CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
		CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
		CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
		CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
		appSerialCanDavinciParDebugMsg("Read Rm");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_RM);																		
		CanPkg.u8Dlc = 0;
		Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
		appGaugeSetRM(Lbyte.u32);

		appSerialCanDavinciParDebugMsg("write Rm");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}   

                    			
static void DavinciCanParameterOcvTable(tHalCanFrame *pCanPkg)
{
	
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tOcvRaTable			OcvTable;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_OCV_TABLE);
		CanPkg.u8Dlc = 5;
		#if MAO_DISSABLE
		apiSysParGetOcvTable(pCanPkg->tUnionData.u8Data[1], &OcvTable);
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];
		CanPkg.tUnionData.u8Data[2] = OcvTable.Level;
		CanPkg.tUnionData.u8Data[3] = OcvTable.Value % 0x100;
		CanPkg.tUnionData.u8Data[4] = OcvTable.Value / 0x100;		
		#endif
		appSerialCanDavinciParDebugMsg("Read Ocv Table");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_OCV_TABLE);
		CanPkg.u8Dlc = 0;


		#if MAO_DISSABLE
		OcvTable.Level = pCanPkg->tUnionData.u8Data[2];
		OcvTable.Value = GET_WORD(&pCanPkg->tUnionData.u8Data[3]);
		apiSysParSetOcvTable(pCanPkg->tUnionData.u8Data[1], &OcvTable);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write Ocv Table");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterRaTable(tHalCanFrame *pCanPkg){
	
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tOcvRaTable			OcvTable;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_RA_TABLE);
		CanPkg.u8Dlc = 5;
		#if MAO_DISSABLE
		apiSysParGetRaTable(pCanPkg->tUnionData.u8Data[1], &OcvTable);
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];
		CanPkg.tUnionData.u8Data[2] = OcvTable.Level;
		CanPkg.tUnionData.u8Data[3] = OcvTable.Value % 0x100;
		CanPkg.tUnionData.u8Data[4] = OcvTable.Value / 0x100;
		#endif
		
		appSerialCanDavinciParDebugMsg("Read Ra Table");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_RA_TABLE);
		CanPkg.u8Dlc = 0;

		#if MAO_DISSABLE
		OcvTable.Level = pCanPkg->tUnionData.u8Data[2];
		OcvTable.Value = GET_WORD(&pCanPkg->tUnionData.u8Data[3]);
		apiSysParSetRaTable(pCanPkg->tUnionData.u8Data[1], &OcvTable);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write Ra Table");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}
//---------------------------------
//Protect
static void DavinciCanParameterOvp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_OVP_PROTECT);
				
		CanPkg.u8Dlc = 7;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParGetOvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Ovp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_OVP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];

		apiSysParSetOvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Ovp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void DavinciCanParameterUvp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_UVP_PROTECT);
				
		CanPkg.u8Dlc = 7;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParGetUvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Uvp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_UVP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];

		apiSysParSetUvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Uvp");
	}
	else
		return;

	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterDvp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_DVP_PROTECT);
		if(pCanPkg->tUnionData.u8Data[0] >= 0x11)
		{
			CanPkg.u8Dlc = 7;
			CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
			#if MAO_DISSABLE
			apiSysParGetDvpPfCellVoltagePar(&ProtectPar);
			CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
			CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
			CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
			CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
			CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
			CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
			#endif
		}							
		else if(pCanPkg->tUnionData.u8Data[0] >= 0x10)
		{
			CanPkg.u8Dlc = 4;
			CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
			#if MAO_DISSABLE
			apiSysParGetDvpPfPar(&ProtectPar);
			CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
			CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
			CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
			#endif
		}
		else
		{
			CanPkg.u8Dlc = 7;
			CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
			#if MAO_DISSABLE
			apiSysParGetDvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
			CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
			CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
			CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
			CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
			CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
			CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
			#endif
		}
		appSerialCanDavinciParDebugMsg("Read Dvp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_DVP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];
		if(pCanPkg->tUnionData.u8Data[0] >= 0x11)
			apiSysParSetDvpPfCellVoltagePar(&ProtectPar);
		else if(pCanPkg->tUnionData.u8Data[0] >= 0x10)
			apiSysParSetDvpPfPar(&ProtectPar);
		else
			apiSysParSetDvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Dvp");
	}
	else
		return;

	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterCotp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_COTP_PROTECT);
				
		CanPkg.u8Dlc = 5;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParGetCotpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Cotp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_COTP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[4];

		apiSysParSetCotpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Cotp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void DavinciCanParameterCutp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_CUTP_PROTECT);
				
		CanPkg.u8Dlc = 5;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParGetCutpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Cutp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_CUTP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[4];

		apiSysParSetCutpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Cutp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterDotp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_DOTP_PROTECT);
				
		CanPkg.u8Dlc = 5;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParGetDotpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Dotp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_DOTP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[4];

		apiSysParSetDotpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Dotp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}
static void DavinciCanParameterDutp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_DUTP_PROTECT);
				
		CanPkg.u8Dlc = 5;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParGetDutpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Dutp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_DUTP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[4];

		apiSysParSetDutpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Dutp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void DavinciCanParameterDtp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_DTP_PROTECT);
				
		CanPkg.u8Dlc = 5;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];		
		
		#if MAO_DISSABLE
		apiSysParGetDtpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Dtp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_DTP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[4];

		apiSysParSetDtpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Dtp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
}

static void DavinciCanParameterCocp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_COCP_PROTECT);
				
		CanPkg.u8Dlc = 7;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParGetCocpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Cocp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_COCP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];

		apiSysParSetCocpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Cocp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterDocp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_DOCP_PROTECT);
				
		CanPkg.u8Dlc = 7;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParGetDocpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Docp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_DOCP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];

		apiSysParSetDocpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Docp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterPF(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_PF_PROTECT);				
		CanPkg.u8Dlc = 4;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		if(CanPkg.tUnionData.u8Data[0] == 0)
		{
			apiSysParGetOvpPfPar(&ProtectPar);
			appSerialCanDavinciParDebugMsg("Read OVF PF");
		}
		else if(CanPkg.tUnionData.u8Data[0] == 1)
		{
			apiSysParGetUvpPfPar(&ProtectPar);
			appSerialCanDavinciParDebugMsg("Read UVF PF");
		}
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_PF_PROTECT);
		CanPkg.u8Dlc = 1;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];

		if(pCanPkg->tUnionData.u8Data[0] == 0)
		{
			apiSysParSetOvpPfPar(&ProtectPar);
			appSerialCanDavinciParDebugMsg("Write OVF PF");
		}
		else if(pCanPkg->tUnionData.u8Data[0] == 1)
		{
			apiSysParSetUvpPfPar(&ProtectPar);
			appSerialCanDavinciParDebugMsg("Write UVF PF");
		}
		#endif
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
//---------------------------------
#if	0
static void DavinciParameterBmuNumInModule(tHalCanFrame *pCanPkg){
	
	tHalCanFrame	CanPkg;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_BMU_NUMBER_IN_MODULE);
		CanPkg.u8Dlc = 1;
		CanPkg.tUnionData.u8Data[0] = apiSysParGetAfeNumInModule();
		appSerialCanDavinciParDebugMsg("Read BMU Num in Module");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_AFE_NUMBER);
		CanPkg.u8Dlc = 0;

		apiSysParSetBmuNumInModule(pCanPkg->tUnionData.u8Data[0]);

		appSerialCanDavinciParDebugMsg("Write BMU Num In Module");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}
#endif
//---------------------------------

static void DavinciParameterAfeNumber(tHalCanFrame *pCanPkg){
	
	tHalCanFrame	CanPkg;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_AFE_NUMBER);
		CanPkg.u8Dlc = 2;		
		CanPkg.tUnionData.u8Data[0] = apiSysParGetAfeNumInModule();
		CanPkg.tUnionData.u8Data[1] = apiSysParGetTotalAfeNumber();
		//appSerialCanDavinciParDebugMsg("Read BMU Number");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_AFE_NUMBER);
		CanPkg.u8Dlc = 0;
		
		if(pCanPkg->tUnionData.u8Data[1] > MAX_BMU_NUM){
			;//appSerialCanDavinciParDebugMsg("Invalid AFE Number");
		}else{
			#if MAO_DISSABLE
			apiSysParSetAfeNumber(pCanPkg->tUnionData.u8Data[0] , pCanPkg->tUnionData.u8Data[1]);
			#endif
			//appSerialCanDavinciParDebugMsg("Write BMU Number");
		}
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}
static void DavinciParameterCellNtcFlag(tHalCanFrame *pCanPkg)
{
	tUnion16Bits	Ibyte;
	tUnion32Bits	Lbyte;
	tHalCanFrame	CanPkg;
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		//appSerialCanDavinciParDebugMsg("Read Cell Ntc Flag");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_CELL_NTC_FLAG);
									
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];									
		if(pCanPkg->tUnionData.u8Data[0] == 0x80)
		{			
			CanPkg.u8Dlc = 8;
			#if MAO_DISSABLE
			apiSysParGetLineLossPar(CanPkg.tUnionData.u8Data[1], &Ibyte.u16, &Lbyte.u32);
			#endif
			CanPkg.tUnionData.u8Data[2] = Ibyte.u8[0];
			CanPkg.tUnionData.u8Data[3] = Ibyte.u8[1];
			
			CanPkg.tUnionData.u8Data[4] = Lbyte.u8[0];
			CanPkg.tUnionData.u8Data[5] = Lbyte.u8[1];
			CanPkg.tUnionData.u8Data[6] = Lbyte.u8[2];
			CanPkg.tUnionData.u8Data[7] = Lbyte.u8[3];
			//appSerialCanDavinciSendTextMessage("Read Line Loss");
		}
		else if(pCanPkg->tUnionData.u8Data[0] == 0x90)
		{
			CanPkg.u8Dlc = 6;
			#if MAO_DISSABLE
			Lbyte.u32 = apiSysParGetCellBusBarFlag(pCanPkg->tUnionData.u8Data[1]);
			#endif
			CanPkg.tUnionData.u8Data[2] = Lbyte.u8[0];
			CanPkg.tUnionData.u8Data[3] = Lbyte.u8[1];
			CanPkg.tUnionData.u8Data[4] = Lbyte.u8[2];
			CanPkg.tUnionData.u8Data[5] = Lbyte.u8[3];
		}
		else if(pCanPkg->tUnionData.u8Data[0] == 0x91)
		{
			CanPkg.u8Dlc = 6;
			#if MAO_DISSABLE
			Lbyte.u32 = apiSysParGetNtcAmbientFlag(pCanPkg->tUnionData.u8Data[1]);
			#endif
			CanPkg.tUnionData.u8Data[2] = Lbyte.u8[0];
			CanPkg.tUnionData.u8Data[3] = Lbyte.u8[1];
			CanPkg.tUnionData.u8Data[4] = Lbyte.u8[2];
			CanPkg.tUnionData.u8Data[5] = Lbyte.u8[3];
			
			//appSerialCanDavinciSendTextMessage("Read Ntc ambient  Flag");
		}
		else if(pCanPkg->tUnionData.u8Data[0] == 0x92)
		{
			CanPkg.u8Dlc = 6;
			#if MAO_DISSABLE
			Lbyte.u32 = apiSysParGetNtcBusBarFlag(pCanPkg->tUnionData.u8Data[1]);
			#endif
			CanPkg.tUnionData.u8Data[2] = Lbyte.u8[0];
			CanPkg.tUnionData.u8Data[3] = Lbyte.u8[1];
			CanPkg.tUnionData.u8Data[4] = Lbyte.u8[2];
			CanPkg.tUnionData.u8Data[5] = Lbyte.u8[3];
			
			//appSerialCanDavinciSendTextMessage("Read Ntc busbar Flag");
		}
		
		
		else if(pCanPkg->tUnionData.u8Data[0] == 0x93)
		{
			CanPkg.u8Dlc = 6;
			#if MAO_DISSABLE
			Lbyte.u32 = apiSysParGetNtcOtherFlag(pCanPkg->tUnionData.u8Data[1]);
			#endif
			CanPkg.tUnionData.u8Data[2] = Lbyte.u8[0];
			CanPkg.tUnionData.u8Data[3] = Lbyte.u8[1];
			CanPkg.tUnionData.u8Data[4] = Lbyte.u8[2];
			CanPkg.tUnionData.u8Data[5] = Lbyte.u8[3];
			
			//appSerialCanDavinciSendTextMessage("Read Ntc other Flag");
		}
		else
		{
			CanPkg.u8Dlc = 8;
			#if MAO_DISSABLE
			Lbyte.u32 = apiSysParGetCellFlag(pCanPkg->tUnionData.u8Data[0]);
			#endif
			CanPkg.tUnionData.u8Data[1] = Lbyte.u8[0];
			CanPkg.tUnionData.u8Data[2] = Lbyte.u8[1];
			CanPkg.tUnionData.u8Data[3] = Lbyte.u8[2];
			CanPkg.tUnionData.u8Data[4] = Lbyte.u8[3];
			#if MAO_DISSABLE
			Lbyte.u32 = apiSysParGetNtcFlag(pCanPkg->tUnionData.u8Data[0]);
			#endif
			CanPkg.tUnionData.u8Data[5] = Lbyte.u8[0];
			CanPkg.tUnionData.u8Data[6] = Lbyte.u8[1];
			CanPkg.tUnionData.u8Data[7] = Lbyte.u8[2];
			
			//appSerialCanDavinciSendTextMessage("Read Cell / NTC Flag");
		}
	}
	else if(isParWritable())
	{
		CanPkg.u8Dlc = 2;
		//appSerialCanDavinciParDebugMsg("Write Cell Ntc Flag");
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];
			
		if(pCanPkg->tUnionData.u8Data[0] == 0x80)
		{
			CanPkg.u8Dlc = 2;
			Ibyte.u16 = GET_DWORD(&pCanPkg->tUnionData.u8Data[2]);
			Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[4]);
			#if MAO_DISSABLE
			apiSysParSetLineLossPar(pCanPkg->tUnionData.u8Data[1], Ibyte.u16, Lbyte.u32);
			#endif
		}
		else if(pCanPkg->tUnionData.u8Data[0] == 0x90)
		{			
			Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[2]);
			#if MAO_DISSABLE
			apiSysParSetCellBusBarFlag(pCanPkg->tUnionData.u8Data[1], Lbyte.u32);
			#endif
			//appSerialCanDavinciSendTextMessage("Write Cell usage Flag");
		}
		else if(pCanPkg->tUnionData.u8Data[0] == 0x91)
		{
			Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[2]);
			#if MAO_DISSABLE
			apiSysParSetNtcAmbientFlag(pCanPkg->tUnionData.u8Data[1], Lbyte.u32);
			#endif
			
			//appSerialCanDavinciSendTextMessage("Write Ntc ambient Flag");
		}
		else if(pCanPkg->tUnionData.u8Data[0] == 0x92)
		{
			Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[2]);
			#if MAO_DISSABLE
			apiSysParSetNtcBusBarFlag(pCanPkg->tUnionData.u8Data[1], Lbyte.u32);
			#endif
			
			//appSerialCanDavinciSendTextMessage("Write Ntc busbar Flag");
		}
	
		else if(pCanPkg->tUnionData.u8Data[0] == 0x93)
		{
			Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[2]);
			#if MAO_DISSABLE
			apiSysParSetNtcOtherFlag(pCanPkg->tUnionData.u8Data[1], Lbyte.u32);
			#endif
			
			//appSerialCanDavinciSendTextMessage("Write Ntc other Flag");		
		}		
		else
		{
			CanPkg.u8Dlc = 1;
			Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[1]);
			#if MAO_DISSABLE
			apiSysParSetCellFlag(pCanPkg->tUnionData.u8Data[0], Lbyte.u32);
			#endif
		
			Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[5]);
			Lbyte.u8[3] = 0;
			#if MAO_DISSABLE
			apiSysParSetNtcFlag(pCanPkg->tUnionData.u8Data[0], Lbyte.u32);
			#endif
		}		
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_CELL_NTC_FLAG);
		
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterBalanceDuty(tHalCanFrame *pCanPkg)
{
//	tUnion32Bits	Lbyte;
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read Balance Duty");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_BALANCE_DUTY);
		CanPkg.u8Dlc = 4;
		#if MAO_DISSABLE
		apiSysParGetBalanceDuty(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RTime.u8[0];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_BALANCE_DUTY);

		CanPkg.u8Dlc = 0;
		
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[0];
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[3];
		apiSysParSetBalanceDuty(&ProtectPar);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write Balance Duty");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterBalanceChg(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read Balance Chg");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_BALANCE_CHG);
		CanPkg.u8Dlc = 8;
		#if MAO_DISSABLE
		apiSysParGetBalanceChg(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[1];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		CanPkg.tUnionData.u8Data[7] = ProtectPar.RTime.u8[1];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_BALANCE_CHG);

		CanPkg.u8Dlc = 0;
		
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[6]);
		apiSysParSetBalanceChg(&ProtectPar);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write Balance Chg");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
}
static void DavinciCanParameterBalanceDhg(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read Balance Dhg");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_BALANCE_DHG);
		CanPkg.u8Dlc = 8;
		#if MAO_DISSABLE
		apiSysParGetBalanceDhg(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[1];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		CanPkg.tUnionData.u8Data[7] = ProtectPar.RTime.u8[1];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_BALANCE_DHG);

		CanPkg.u8Dlc = 0;
		
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[6]);
		apiSysParSetBalanceDhg(&ProtectPar);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write Balance Dhg");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}
static void DavinciCanParameterBalanceRlx(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read Balance Rlx");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_BALANCE_RLX);
		CanPkg.u8Dlc = 8;
		#if MAO_DISSABLE
		apiSysParGetBalanceRlx(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[1];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		CanPkg.tUnionData.u8Data[7] = ProtectPar.RTime.u8[1];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_BALANCE_RLX);

		CanPkg.u8Dlc = 0;
		
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[6]);
		apiSysParSetBalanceRlx(&ProtectPar);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write Balance Rlx");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}


static void DavinciCanParameterNoteMessage(tHalCanFrame *pCanPkg)
{
//	uint32_t		canid;	
//	uint8_t			cansendbf[8];
	uint8_t			canDatLeng = 0;
	uint8_t			i,index;
	uint8_t			NoteLen;
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read Note");
		
		#if MAO_DISSABLE
		apiSysParGetNotwMessageString(StringMessageBuffer);
		#endif
		NoteLen = strlen((char *)StringMessageBuffer);		
		CanPkg.tUnionData.u8Data[0] = (NoteLen/6);
		if(NoteLen % 6)
			CanPkg.tUnionData.u8Data[0]++;
		CanPkg.tUnionData.u8Data[1] = 1;
		
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_NOTE_MESSAGE);
		index = 0;
		do{
			canDatLeng = NoteLen - index;	
			if(canDatLeng >6)
				canDatLeng = 6;
			for(i=0; i<canDatLeng; i++) 
				CanPkg.tUnionData.u8Data[2 + i] = StringMessageBuffer[index++];
			CanPkg.u8Dlc = canDatLeng + 2;	
			appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
			CanPkg.tUnionData.u8Data[1] ++;
		}while(index < NoteLen);				
	}
	else if(isParWritable())
	{
		appSerialCanDavinciParDebugMsg("Write Note Message");
		if(pCanPkg->tUnionData.u8Data[1] == 0x00)
			return;
		if(pCanPkg->tUnionData.u8Data[1] ==0x01)
		{
			for(i=0; i<MAX_NOTE_MESSAGE_STRING_ITEM; i++)
				StringMessageBuffer[i] = 0;	
		}
		index = pCanPkg->tUnionData.u8Data[1] - 1;
		index *= 6;
		for(i=2; i<pCanPkg -> u8Dlc; i++)
		{
			if(index >= MAX_NOTE_MESSAGE_STRING_ITEM)
				break;
			StringMessageBuffer[index++] = pCanPkg->tUnionData.u8Data[i];
		} 		
		if(pCanPkg->tUnionData.u8Data[1] >= pCanPkg->tUnionData.u8Data[0])
		{
			#if MAO_DISSABLE
			apiSysParSetNotwMessageString(StringMessageBuffer);
			#endif
			CanPkg.tUnionData.u8Data[0] = 0;
		}
		else
			CanPkg.tUnionData.u8Data[0] = 1;
		CanPkg.u8Dlc = 1;
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_NOTE_MESSAGE);

		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}
	else
		return;
}

static void DavinciCanParameterPackSn(tHalCanFrame *pCanPkg)
{
	uint8_t			i;
	uint8_t			*ptr;
	tHalCanFrame	CanPkg;
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		//appSerialCanDavinciParDebugMsg("Read Pack SN");
		
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_PACK_SN);

		#if MAO_DISSABLE
		ptr = apiPackSnGetSnData(pCanPkg->tUnionData.u8Data[0]);
		#else
		ptr = NULL;
		#endif
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		if(ptr)
		{
			CanPkg.u8Dlc = 8;
			for(i=0; i<7; i++)
				CanPkg.tUnionData.u8Data[i+1] = ptr[i];
		}
		else
			CanPkg.u8Dlc = 1;
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);			
	}
	else if(isParWritable())
	{
		//appSerialCanDavinciParDebugMsg("Write Pack SN");
		#if MAO_DISSABLE
		apiPackSnSetSnData(pCanPkg->tUnionData.u8Data[0], &pCanPkg->tUnionData.u8Data[1]);
		#endif
		CanPkg.u8Dlc = 1;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_PACK_SN);
									
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}
}

static int32_t Get3BytesInt32(uint8_t *pDat)
{
	tUnion32Bits	value;
	value.u8[0] = pDat[0];
	value.u8[1] = pDat[1];
	value.u8[2] = pDat[2];
	if(value.u8[2] &0x80)
		value.u8[3] = 0xff;
	else
		value.u8[3] = 0;
	return value.i32;
}

static void DavinciCanParameterCurrentCalibration(tHalCanFrame *pCanPkg)
{
//	uint8_t			cansendbf[8];
	tHalCanFrame	CanPkg;
	tUnion32Bits			Value;
	tUnion32Bits			Adc;
	char			str[100];

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_CALIB_CURR);
		CanPkg.u8Dlc = 8;
		#if MAO_DISSABLE
		apiCaliParGetCurrentValue(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1],
									&Value.i32, &Adc.i32);
		#endif
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];	//current index
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];	//point 0 or 1
		CanPkg.tUnionData.u8Data[2] = Value.u8[0];	
		CanPkg.tUnionData.u8Data[3] = Value.u8[1];
		CanPkg.tUnionData.u8Data[4] = Value.u8[2];
		CanPkg.tUnionData.u8Data[5] = Adc.u8[0];
		CanPkg.tUnionData.u8Data[6] = Adc.u8[1];
		CanPkg.tUnionData.u8Data[7] = Adc.u8[2];	

		appSerialCanDavinciParDebugMsg("Read Curr Cali");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_CALIB_CURR);
		CanPkg.u8Dlc = 0;

		Value.i32 = Get3BytesInt32(&pCanPkg->tUnionData.u8Data[2]);
		Adc.i32 = Get3BytesInt32(&pCanPkg->tUnionData.u8Data[5]);
		#if MAO_DISSABLE
		apiCaliParSetCurrentValue(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1],
									Value.i32, Adc.i32);
		#endif
									
		sprintf(str,"Wr Curr %d %d",Value.i32, Adc.i32);
		appSerialCanDavinciParDebugMsg(str);//"Write Curr Cali");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void DavinciCanParameterVBatCalibration(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits				Value;
	tUnion32Bits				Adc;
	char				str[100];

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_CALIB_VB);
		CanPkg.u8Dlc = 8;
		#if MAO_DISSABLE
		apiCaliParGetVbatValue(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1],
									&Value.i32, &Adc.i32);
		#endif
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];	//vbat index
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];	//point 0 or 1
		CanPkg.tUnionData.u8Data[2] = Value.u8[0];	
		CanPkg.tUnionData.u8Data[3] = Value.u8[1];
		CanPkg.tUnionData.u8Data[4] = Value.u8[2];
		CanPkg.tUnionData.u8Data[5] = Adc.u8[0];
		CanPkg.tUnionData.u8Data[6] = Adc.u8[1];
		CanPkg.tUnionData.u8Data[7] = Adc.u8[2];	

		appSerialCanDavinciParDebugMsg("Read Vbat Cali");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_CALIB_VB);
		CanPkg.u8Dlc = 0;

		Value.i32 = Get3BytesInt32(&pCanPkg->tUnionData.u8Data[2]);
		Adc.i32 = Get3BytesInt32(&pCanPkg->tUnionData.u8Data[5]);
		#if MAO_DISSABLE
		apiCaliParSetVbatValue(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1],
									Value.i32, Adc.i32);
		#endif
									
		sprintf(str,"Wr Vbat %d %d",Value.i32, Adc.i32);
		appSerialCanDavinciParDebugMsg(str);//"Write vbat Cali");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void DavinciCanParameterScuTemperter(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_SCU_TEMP_PROTECT);
		CanPkg.u8Dlc = 6;
		#if MAO_DISSABLE
		apiSysParGetScuOtPar(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1], &ProtectPar);		
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RTime.u8[0];
		#endif
		
		appSerialCanDavinciParDebugMsg("Read Scu Temp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_SCU_TEMP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[4];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[5];
		apiSysParSetScuOtPar(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1], &ProtectPar);
		#endif

		appSerialCanDavinciParDebugMsg("Write Scu Temp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterModuleDvp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_MODULE_DVP_PROTECT);
		if(pCanPkg->tUnionData.u8Data[0] >= 0x10)
		{
			CanPkg.u8Dlc = 4;
			CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
			#if MAO_DISSABLE
			apiSysParGetMDvpPfPar(&ProtectPar);
			CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
			CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
			CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
			#endif
		}
		else
		{
			CanPkg.u8Dlc = 7;
			CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
			#if MAO_DISSABLE
			apiSysParGetModuleDvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
			CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
			CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
			CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
			CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
			CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
			CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
			#endif
		}
		appSerialCanDavinciParDebugMsg("Read Module Dvp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_MODULE_DVP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];
		if(pCanPkg->tUnionData.u8Data[0] >= 0x10)
		{
			apiSysParSetMDvpPfPar(&ProtectPar);
		}
		else
		{
			apiSysParSetModuleDvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		}
		#endif
		appSerialCanDavinciParDebugMsg("Write Module Dvp");
	}
	else
		return;

	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterModuleDtp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_MODULE_DTP_PROTECT);
				
		CanPkg.u8Dlc = 5;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];		
		
		#if MAO_DISSABLE
		apiSysParGetModuleDtpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RTime.u8[0];
		#endif

		appSerialCanDavinciParDebugMsg("Read Module Dtp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_MODULE_DTP_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RelValue.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[4];

		apiSysParSetModuleDtpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Module Dtp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterIrUrp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_IR_URP_PROTECT);
	
		CanPkg.u8Dlc = 7;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];		
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];
		#if MAO_DISSABLE
		if((CanPkg.tUnionData.u8Data[1]&0x01) == 0)	//0,2,4,
		{
			apiSysParGetIrUrpSetPar(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1]/2, &ProtectPar);
			CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[0];
			CanPkg.tUnionData.u8Data[3] = ProtectPar.SetValue.u8[1];
			CanPkg.tUnionData.u8Data[4] = ProtectPar.SetValue.u8[2];
			CanPkg.tUnionData.u8Data[5] = ProtectPar.SetValue.u8[3];	
			CanPkg.tUnionData.u8Data[6] = ProtectPar.STime.u8[0];
		}		
		else		//1,3,5
		{
			apiSysParGetIrUrpRlxPar(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1]/2, &ProtectPar);
			CanPkg.tUnionData.u8Data[2] = ProtectPar.RelValue.u8[0];
			CanPkg.tUnionData.u8Data[3] = ProtectPar.RelValue.u8[1];
			CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[2];
			CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[3];	
			CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		}
		#endif

		appSerialCanDavinciParDebugMsg("Read IR Urp");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_IR_URP_PROTECT);
									
		CanPkg.u8Dlc = 2;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];		
		CanPkg.tUnionData.u8Data[1] = pCanPkg->tUnionData.u8Data[1];
		#if MAO_DISSABLE
		if((pCanPkg->tUnionData.u8Data[1] & 0x01) == 0)
		{
			ProtectPar.SetValue.l = GET_DWORD(&pCanPkg->tUnionData.u8Data[2]);
			ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[6];
			apiSysParSetIrUrpSetPar(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1]/2, &ProtectPar);
		}
		else
		{
			ProtectPar.RelValue.l = GET_DWORD(&pCanPkg->tUnionData.u8Data[2]);
			ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];
			apiSysParSetIrUrpRlxPar(pCanPkg->tUnionData.u8Data[0], pCanPkg->tUnionData.u8Data[1]/2, &ProtectPar);
		}
		#endif
		appSerialCanDavinciParDebugMsg("Write IR Urp");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanParameterCurrentDip(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read DIP");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_DIP_PROTECT);
		CanPkg.u8Dlc = 7;
		
		#if MAO_DISSABLE
		apiSysParGetDipPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_DIP_PROTECT);

		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];
		apiSysParSetDipPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write Dip ");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}


static void DavinciCanParameterVbDvp(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read VbDvp");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_VBDVP_PROTECT);
		CanPkg.u8Dlc = 7;
		
		#if MAO_DISSABLE
		apiSysParGetVbDvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_VBDVP_PROTECT);

		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[1]);
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[3];
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[6];
		apiSysParSetVbDvpPar(pCanPkg->tUnionData.u8Data[0], &ProtectPar);
		#endif
		appSerialCanDavinciParDebugMsg("Write VbVbp ");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}



static void DavinciCanParameterOpenWire(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read OpenWire");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_OPEN_WIRE_PROTECT);
		CanPkg.u8Dlc = 4;
		
		#if MAO_DISSABLE
		apiSysParGetCellOWpPar(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.RTime.u8[0];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_OPEN_WIRE_PROTECT);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		//ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);	
		ProtectPar.STime.l = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.RTime.l = pCanPkg->tUnionData.u8Data[3];
		apiSysParSetCellOWpPar(&ProtectPar);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write OpenWire");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterBMUPassiveBalR(tHalCanFrame *pCanPkg){
	tHalCanFrame	CanPkg;
	tUnion16Bits setVal;
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read BMUPassiveR");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_BMU_PASSIVE_BAL_R);
		CanPkg.u8Dlc = 2;
		
		setVal.u16 = apiSysParGetBMUPassiveBalR();
		CanPkg.tUnionData.u8Data[0] = setVal.u8[0];
		CanPkg.tUnionData.u8Data[1] = setVal.u8[1];
	}else if(isParWritable()){
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_BMU_PASSIVE_BAL_R);
		CanPkg.u8Dlc = 0;
		setVal.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		#if MAO_DISSABLE
		apiSysParSetBMUPassiveBalR(setVal.u16);
		#endif
		appSerialCanDavinciParDebugMsg("Write BMUPassiveR");
	}else{
		return;
	}
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}


static void DavinciCanParameterBmuType(tHalCanFrame *pCanPkg){
	
	tHalCanFrame	CanPkg;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_BMU_TYPE);
		CanPkg.u8Dlc = 1;
		CanPkg.tUnionData.u8Data[0] = apiSysParGetBmuType();
		appSerialCanDavinciParDebugMsg("Read BMU Type");
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_BMU_TYPE);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		apiSysParSetBmuType(pCanPkg->tUnionData.u8Data[0]);
		#endif
		appSerialCanDavinciParDebugMsg("Write BMU Type");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterScuID(tHalCanFrame *pCanPkg){
	tHalCanFrame	CanPkg;
	tUnion32Bits	version;
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_SCU_ID);
		
		CanPkg.u8Dlc = 1;
		CanPkg.tUnionData.u8Data[0] = canParScuId();		
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_SCU_ID);
		CanPkg.u8Dlc = 1;
		CanPkg.tUnionData.u8Data[0] = pCanPkg->tUnionData.u8Data[0];
		#if MAO_DISSABLE
		apiSysParSetScuId(pCanPkg->tUnionData.u8Data[0]);	
		#endif
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);

}
static void DavinciCanParameterInternetMac(tHalCanFrame *pCanPkg){
	
}

static void DavinciCanParameterInternetIpSubMask(tHalCanFrame *pCanPkg){
	
	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Get Ip submask");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_INTERNET_IP_SUBMASK);
		CanPkg.u8Dlc = 8;
		
		#if MAO_DISSABLE
		apiSysParGetIpAddrSubMask(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[2];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.SetValue.u8[3];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.STime.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.STime.u8[2];
		CanPkg.tUnionData.u8Data[7] = ProtectPar.STime.u8[3];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_INTERNET_IP_SUBMASK);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.u8[0] = pCanPkg->tUnionData.u8Data[0];
		ProtectPar.SetValue.u8[1] = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.SetValue.u8[2] = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.SetValue.u8[3] = pCanPkg->tUnionData.u8Data[3];
		
		ProtectPar.STime.u8[0] = pCanPkg->tUnionData.u8Data[4];
		ProtectPar.STime.u8[1] = pCanPkg->tUnionData.u8Data[5];
		ProtectPar.STime.u8[2] = pCanPkg->tUnionData.u8Data[6];
		ProtectPar.STime.u8[3] = pCanPkg->tUnionData.u8Data[7];

		apiSysParSetIpAddrSubMask(&ProtectPar);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write Ip Mask");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void DavinciCanParameterInternetGateway(tHalCanFrame *pCanPkg){

	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Get Gateway");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_INTERNET_GATEWAY);
		CanPkg.u8Dlc = 4;

		#if MAO_DISSABLE
		apiSysParGetGateway(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.SetValue.u8[2];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.SetValue.u8[3];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_INTERNET_GATEWAY);
		CanPkg.u8Dlc = 0;
		#if MAO_DISSABLE
		ProtectPar.SetValue.u8[0] = pCanPkg->tUnionData.u8Data[0];
		ProtectPar.SetValue.u8[1] = pCanPkg->tUnionData.u8Data[1];
		ProtectPar.SetValue.u8[2] = pCanPkg->tUnionData.u8Data[2];
		ProtectPar.SetValue.u8[3] = pCanPkg->tUnionData.u8Data[3];	
		apiSysParSetGateway(&ProtectPar);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write Gateway");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
}

static void DavinciCanParameterSocBalance(tHalCanFrame *pCanPkg){

	tHalCanFrame	CanPkg;
	#if MAO_DISSABLE
	tScuProtectPar		ProtectPar;
	#endif
	
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX)
	{
		appSerialCanDavinciParDebugMsg("Read SocBalance");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_SOCBAL_PAR);
		CanPkg.u8Dlc = 8;
		
		#if MAO_DISSABLE
		apiSysParGetSocBalancePar(&ProtectPar);
		CanPkg.tUnionData.u8Data[0] = ProtectPar.SetValue.u8[0];
		CanPkg.tUnionData.u8Data[1] = ProtectPar.SetValue.u8[1];
		CanPkg.tUnionData.u8Data[2] = ProtectPar.STime.u8[0];
		CanPkg.tUnionData.u8Data[3] = ProtectPar.STime.u8[1];
		CanPkg.tUnionData.u8Data[4] = ProtectPar.RelValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = ProtectPar.RelValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = ProtectPar.RTime.u8[0];
		CanPkg.tUnionData.u8Data[7] = ProtectPar.RTime.u8[1];
		#endif
	}
	else if(isParWritable())
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_SOCBAL_PAR);
		CanPkg.u8Dlc = 0;

		#if MAO_DISSABLE
		ProtectPar.SetValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		ProtectPar.STime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);
		ProtectPar.RelValue.l = GET_WORD(&pCanPkg->tUnionData.u8Data[4]);
		ProtectPar.RTime.l = GET_WORD(&pCanPkg->tUnionData.u8Data[6]);

		apiSysParSetSocBalancePar(&ProtectPar);
		#endif
		
		appSerialCanDavinciParDebugMsg("Write ScoBalance");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterCellFcc(tHalCanFrame *pCanPkg){
	tUnion16Bits mCellFcc;
	tUnion16Bits mStartCellIndex;
	tHalCanFrame	CanPkg;
	uint8_t j;
	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX){
		appSerialCanDavinciParDebugMsg("Read CellFcc");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_CELL_FCC);
		CanPkg.u8Dlc = 8;
		mStartCellIndex.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		
		CanPkg.tUnionData.u8Data[0] = mStartCellIndex.u8[0];
		CanPkg.tUnionData.u8Data[1] = mStartCellIndex.u8[1];
		for(j = 0; j < 3; j++){
			mCellFcc.u16 = apiEkfParGetCellFcc(mStartCellIndex.u16 + j);
			CanPkg.tUnionData.u8Data[2 + 2*j] = mCellFcc.u8[0];  
			CanPkg.tUnionData.u8Data[2 + 2*j + 1] = mCellFcc.u8[1]; 
		}

	}else if(isParWritable()){
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_CELL_FCC);
		CanPkg.u8Dlc = 0;
		mStartCellIndex.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		for(j = 1; j <= 3 ; j++){
			mCellFcc.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[2*j]); 
			apiEkfParSetCellFcc(mStartCellIndex.u16 + j - 1, mCellFcc.u16);
		}
	}else{
		return;
	}
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
}

static void DavinciCanParameterEcmPar(tHalCanFrame *pCanPkg){
	enum{
	    ECM_PAR_PERCENTAGE_COL_INDEX = 0,
	    ECM_PAR_OCV_COL_INDEX,
	    ECM_PAR_RP0_COL_INDEX,
	    ECM_PAR_RP1_COL_INDEX,
	    ECM_PAR_RP2_COL_INDEX,
	    ECM_PAR_CP1_COL_INDEX,
	    ECM_PAR_CP2_COL_INDEX,
	    ECM_PAR_LENGTH_INDEX = 0x10
	};

	uint8_t		_u8ParType;
	tUnion16Bits		_u16ParIndex;
	tUnion16Bits		_u16ParValue;
	tUnion32Bits		_u32ParValue;
	tHalCanFrame	CanPkg;

	if(SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id) == SMP_CMD_PAR_RD_OBJ_INDEX){
		appSerialCanDavinciParDebugMsg("Read Ecm Par");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_RD_OBJ_INDEX,
									SMP_PAR_ID_ECM_PAR);
		
		_u8ParType = pCanPkg->tUnionData.u8Data[0];
		_u16ParIndex.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);
		if(_u8ParType == ECM_PAR_LENGTH_INDEX)
		{
			_u32ParValue.u32 = apiEkfParGetEcmParLength();
		}
		else if(_u8ParType == ECM_PAR_PERCENTAGE_COL_INDEX)
		{
			_u32ParValue.u32 = apiEkfParGetEcmParPercentage(_u16ParIndex.u16);
		}
		else if(_u8ParType == ECM_PAR_OCV_COL_INDEX)
		{
			_u32ParValue.u32 = apiEkfParGetEcmParVocv(_u16ParIndex.u16);
		}
		else if(_u8ParType == ECM_PAR_RP0_COL_INDEX)
		{
			_u32ParValue.u32 = apiEkfParGetEcmParRp0(_u16ParIndex.u16);
		}
		else if(_u8ParType == ECM_PAR_RP1_COL_INDEX)
		{
			_u32ParValue.u32 = apiEkfParGetEcmParRp1(_u16ParIndex.u16);
		}
		else if(_u8ParType == ECM_PAR_RP2_COL_INDEX)
		{
			_u32ParValue.u32 = apiEkfParGetEcmParRp2(_u16ParIndex.u16);
		}
		else if(_u8ParType == ECM_PAR_CP1_COL_INDEX)
		{
			_u32ParValue.u32 = apiEkfParGetEcmParCp1(_u16ParIndex.u16);
		}		
		else if(_u8ParType == ECM_PAR_CP2_COL_INDEX)
		{
			_u32ParValue.u32 = apiEkfParGetEcmParCp2(_u16ParIndex.u16);
		}
		else
			return;
		CanPkg.u8Dlc = 8;
		CanPkg.tUnionData.u8Data[0] = _u8ParType;
		CanPkg.tUnionData.u8Data[1] = 0;
		CanPkg.tUnionData.u8Data[2] = _u16ParIndex.u8[0];
		CanPkg.tUnionData.u8Data[3] = _u16ParIndex.u8[1];
		CanPkg.tUnionData.u8Data[4] = _u32ParValue.u8[0];
		CanPkg.tUnionData.u8Data[5] = _u32ParValue.u8[1];
		CanPkg.tUnionData.u8Data[6] = _u32ParValue.u8[2];
		CanPkg.tUnionData.u8Data[7] = _u32ParValue.u8[3];
	}else if(isParWritable()){
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canParScuId(),
									SMP_CMD_PAR_WR_OBJ_INDEX,
									SMP_PAR_ID_ECM_PAR);
		
		_u8ParType = pCanPkg->tUnionData.u8Data[0];
		_u16ParIndex.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[2]);	
		_u32ParValue.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[4]);
		
		if(_u8ParType == ECM_PAR_LENGTH_INDEX)
		{
			apiEkfParSetEcmParLength(_u32ParValue.u32);
		}
		else if(_u8ParType == ECM_PAR_PERCENTAGE_COL_INDEX)
		{
			apiEkfParSetEcmParPercentage(_u16ParIndex.u16, _u32ParValue.u32);
		}
		else if(_u8ParType == ECM_PAR_OCV_COL_INDEX)
		{
			apiEkfParSetEcmParVocv(_u16ParIndex.u16, _u32ParValue.u32);
		}
		else if(_u8ParType == ECM_PAR_RP0_COL_INDEX)
		{
			apiEkfParSetEcmParRp0(_u16ParIndex.u16, _u32ParValue.u32);
		}
		else if(_u8ParType == ECM_PAR_RP1_COL_INDEX)
		{
			apiEkfParSetEcmParRp1(_u16ParIndex.u16, _u32ParValue.u32);
		}
		else if(_u8ParType == ECM_PAR_RP2_COL_INDEX)
		{
			apiEkfParSetEcmParRp2(_u16ParIndex.u16, _u32ParValue.u32);
		}
		else if(_u8ParType == ECM_PAR_CP1_COL_INDEX)
		{
			apiEkfParSetEcmParCp1(_u16ParIndex.u16, _u32ParValue.u32);
		}
		else if(_u8ParType == ECM_PAR_CP2_COL_INDEX)
		{
			apiEkfParSetEcmParCp2(_u16ParIndex.u16, _u32ParValue.u32);
		}	
		else
			return;
		CanPkg.u8Dlc = 4;
		CanPkg.tUnionData.u8Data[0] = _u8ParType;
		CanPkg.tUnionData.u8Data[1] = 0;
		CanPkg.tUnionData.u8Data[2] = _u16ParIndex.u8[0];
		CanPkg.tUnionData.u8Data[3] = _u16ParIndex.u8[1];
		
	}else{
		return;
	}
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	

}


//----------------------------------------------------------
SMP_CAN_DECODE_CMD_START(mDavinciParameterCanDecodeTab)
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_MAGIC_CODE),
								CHECK_SMP_CAN_SUB,
								DavinciParameterMagicCode)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_HW_VERSION),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterHwVersion)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_FW_VERSION),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterFwVersion)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_FW_INTERNAL_VERSION),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterFwInternalVersion)

	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_FW_BUILD_DATE_TIME), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterFwBuildDateTime)


	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_ZERO_CURRENT), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterZeroCurrent)
                    			
 	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_PRE_DHG_TIME), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterPreDischargeTime)
                   			
 	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_MOS_ON_DIFF_VOLTAGE), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterRelayOnDiffVoltage)
                    			

	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_DESIGNED_CAPACITY), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterDesignedCapacity)

	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_RELAY_ACTIVE_FLAG), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterRellayActiveFlag)

	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_MAX_CHG_DHG_CURRENT), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterMaxCurrent)
                    			
	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_PEAK_CURRENT), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterPeakCurrent)

	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_RATE_VOLTAGE), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterRateVoltage)
  
	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_TAPER_CURRENT), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterFullChargeCondition)
                    			
	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_FLAT_VOLTAGE), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterFlatVoltage)

	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_TERMINATE_VOLTAGE), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterTerminateVoltage)

	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_COMMUNICATION), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterAfeCommunication)

	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_INSULATION_RESISTANCE), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterInsulationResistance)


	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_QMAX), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterQmax)
	SMP_CAN_DECODE_CMD_CONTENT( MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0, 
									0,
             						SMP_PAR_ID_RM), 
             					CHECK_SMP_CAN_SUB,
                    			DavinciCanParameterRM)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_NOTE_MESSAGE),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterNoteMessage)
	
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_PACK_SN),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterPackSn)

#if 0 
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_BMU_NUMBER_IN_MODULE),
								CHECK_SMP_CAN_SUB,
								DavinciParameterBmuNumInModule)
#endif //jjjjj


	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_AFE_NUMBER),
								CHECK_SMP_CAN_SUB,
								DavinciParameterAfeNumber)
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_CELL_NTC_FLAG),
								CHECK_SMP_CAN_SUB,
								DavinciParameterCellNtcFlag)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_OCV_TABLE),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterOcvTable)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_RA_TABLE),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterRaTable)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_OVP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterOvp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_UVP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterUvp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_DVP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterDvp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_COTP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterCotp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_CUTP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterCutp)
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_DOTP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterDotp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_DUTP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterDutp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_DTP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterDtp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_COCP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterCocp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_DOCP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterDocp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_PF_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterPF)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_BALANCE_DUTY),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterBalanceDuty)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_BALANCE_CHG),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterBalanceChg)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_BALANCE_DHG),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterBalanceDhg)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_BALANCE_RLX),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterBalanceRlx)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_CALIB_CURR),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterCurrentCalibration)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_CALIB_VB),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterVBatCalibration)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_SCU_TEMP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterScuTemperter)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_MODULE_DVP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterModuleDvp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_MODULE_DTP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterModuleDtp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_IR_URP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterIrUrp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_DIP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterCurrentDip)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_BMU_TYPE),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterBmuType)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_VBDVP_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterVbDvp)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_OPEN_WIRE_PROTECT),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterOpenWire)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
								0,
								SMP_PAR_ID_BMU_PASSIVE_BAL_R),
							CHECK_SMP_CAN_SUB,
							DavinciCanParameterBMUPassiveBalR)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_SCU_ID),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterScuID)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_INTERNET_MAC),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterInternetMac)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_INTERNET_IP_SUBMASK),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterInternetIpSubMask)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_INTERNET_GATEWAY),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterInternetGateway)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_SOCBAL_PAR),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterSocBalance)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_CELL_FCC),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterCellFcc)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									0,
									SMP_PAR_ID_ECM_PAR),
								CHECK_SMP_CAN_SUB,
								DavinciCanParameterEcmPar)



SMP_CAN_DECODE_CMD_END();



void DavinciCanParameterRdWrite(tHalCanFrame *pCanPkg){
	
//	uint8_t	i,n;
	uint8_t cmdIndex;
	
//	char	str[100];
//	char	str1[100];
 	cmdIndex = 0;
	//appSerialCanDavinciParDebugMsg("Decode RW par ID");

	for(cmdIndex = 0; mDavinciParameterCanDecodeTab[cmdIndex].fun != 0; cmdIndex++)
	{
		if((mDavinciParameterCanDecodeTab[cmdIndex].canid & mDavinciParameterCanDecodeTab[cmdIndex].mask) == 
		   (mDavinciParameterCanDecodeTab[cmdIndex].mask & pCanPkg -> u32Id) &&
		    appSerialCanDavinciIsCorrectScuId(pCanPkg))
		{
			mDavinciParameterCanDecodeTab[cmdIndex].fun(pCanPkg);
			break; 		
 		}
	}
}


/* Public function prototypes -----------------------------------------------*/

/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    

