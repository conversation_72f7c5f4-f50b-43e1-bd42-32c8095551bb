/*
******************************************************************************
* @file     HalGeneralTimer.c
* <AUTHOR>
* @brief    This file include MSPM0G Timer Hardwarre Abstraction Layer Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include "HalGeneralTimer.h"
/* Local typedef -----------------------------------------------------------*/
/* Local define ------------------------------------------------------------*/
#define HAL_TIMERG_TARGET_FREQUENCY (1)   //Unit MHz
#define HAL_TIMERG0_DEFAULT_DEVIDE_RATIO (4)
#define HAL_TIMERG6_DEFAULT_DEVIDE_RATIO (8)

#define HAL_TIMERG_DEFAULT_PERIODCNT (999)
#define HAL_TIMERG_PERIOD_CNT_OFFSET (1)
/* Local macro -------------------------------------------------------------*/
/* Local function declare --------------------------------------------------*/
static bool TimerGInit(GPTIMER_Regs* pTimer, tLibFuncPtrRegEvtHandler mEvtHandler);
/* Global variables ---------------------------------------------------------*/
static tLibFuncPtrRegEvtHandler gmTimerG0EvtHandler = 0;
static tLibFuncPtrRegEvtHandler gmTimerG6EvtHandler = 0;

/*
    TIMG0 belong PD0 Power domain and Souce From ULPCLK 
    Freq Timer = Freq Source / ((devideRatio + 1)*(prescale + 1 ))
             = 40 Mhz/ (4*10) = 1MHz
*/
static const DL_TimerG_ClockConfig gTIMER_0ClockConfig = 
{
    /*BUSCLK will be MCLK or ULPCLK according to Operating Mode (RUN, Sleep)*/
    .clockSel    = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_4,
    .prescale    = (HAL_MCU_ULPCLK_CLOCK_FREQUENCY / HAL_TIMERG0_DEFAULT_DEVIDE_RATIO / HAL_TIMERG_TARGET_FREQUENCY) - 1, // -1  Due To Formula
};

/*Freq Timer * PeriodCount --> 1kHz Trigger Once */
static DL_TimerG_TimerConfig gTIMER_0TimerConfig = 
{
    .period     = HAL_TIMERG_DEFAULT_PERIODCNT,
    .timerMode  = DL_TIMER_TIMER_MODE_PERIODIC,
    .startTimer = DL_TIMER_STOP,
};

static const DL_TimerG_ClockConfig gTIMER_6ClockConfig = 
{
    /*BUSCLK will be MCLK or ULPCLK according to Operating Mode (RUN, Sleep)*/
    .clockSel    = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_8,
    .prescale    = (HAL_MCU_MAIN_CLOCK_FREQUENCY / HAL_TIMERG6_DEFAULT_DEVIDE_RATIO / HAL_TIMERG_TARGET_FREQUENCY) - 1, // -1  Due To Formula
};

/*Freq Timer * PeriodCount --> 1kHz Trigger Once */
static DL_TimerG_TimerConfig gTIMER_6TimerConfig = 
{
    .period     = HAL_TIMERG_DEFAULT_PERIODCNT,
    .timerMode  = DL_TIMER_TIMER_MODE_PERIODIC,
    .startTimer = DL_TIMER_STOP,
};


/* Local function prototypes -----------------------------------------------*/
static bool TimerGInit(GPTIMER_Regs* pTimer, tLibFuncPtrRegEvtHandler mEvtHandler) 
{
    DL_TimerG_reset(pTimer);
    DL_TimerG_enablePower(pTimer);
    delay_cycles(HAL_MCU_POWER_STARTUP_DELAY_CYCLE_CNT);
    
    if (pTimer == TIMG0)
    {
        gmTimerG0EvtHandler = mEvtHandler;
        DL_TimerG_setClockConfig(pTimer, (DL_TimerG_ClockConfig *) &gTIMER_0ClockConfig);
       
    }
    else if (pTimer == TIMG6)
    {
        gmTimerG6EvtHandler = mEvtHandler;
        DL_TimerG_setClockConfig(pTimer, (DL_TimerG_ClockConfig *) &gTIMER_6ClockConfig);
    }
    else
    {
        return false;
    }

    DL_TimerG_initTimerMode(pTimer, (DL_TimerG_TimerConfig *) &gTIMER_0TimerConfig);
    DL_TimerG_enableInterrupt(pTimer , DL_TIMERG_EVENT_ZERO_EVENT);
    DL_TimerG_enableClock(pTimer);
    DL_TimerG_startCounter(pTimer);
    return true;
}
/* Global function prototypes ------------------------------------------------*/
tFunRetunCode HalGeneralTimerLowPowerOpenAndStart(uint16_t u16PeriodCntBaseOn1MHz, tLibFuncPtrRegEvtHandler mEvtHandler)
{
    if (u16PeriodCntBaseOn1MHz < HAL_TIMERG_PERIOD_CNT_OFFSET)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    gTIMER_0TimerConfig.period = u16PeriodCntBaseOn1MHz - HAL_TIMERG_PERIOD_CNT_OFFSET;
    if (TimerGInit(TIMG0, mEvtHandler) == false)
    {
        return RES_ERROR_NOT_SUPPORTED;
    }
    
    NVIC_EnableIRQ(TIMG0_INT_IRQn);
    return RES_SUCCESS;
}

tFunRetunCode HalGeneralTimerHighPowerOpenAndStart(uint16_t u16PeriodCntBaseOn1MHz, tLibFuncPtrRegEvtHandler mEvtHandler)
{
    if (u16PeriodCntBaseOn1MHz < HAL_TIMERG_PERIOD_CNT_OFFSET)
    {
        return RES_ERROR_INVALID_PARAM;
    }

    gmTimerG6EvtHandler = mEvtHandler;
    gTIMER_6TimerConfig.period = u16PeriodCntBaseOn1MHz - HAL_TIMERG_PERIOD_CNT_OFFSET;
    if (TimerGInit(TIMG6, mEvtHandler) == false)
    {
        return RES_ERROR_NOT_SUPPORTED;
    }
    NVIC_EnableIRQ(TIMG6_INT_IRQn);
    return RES_SUCCESS;
}

tFunRetunCode HalGeneralTimerLowPowerStop(void)
{
    DL_TimerG_stopCounter(TIMG0);
    if (DL_TimerG_isRunning(TIMG0) == true)
    {
        return RES_ERROR_NOT_FOUND;
    }
    return RES_SUCCESS;
}

tFunRetunCode HalGeneralTimerHighPowerStop(void)
{
    DL_TimerG_stopCounter(TIMG6);
    if (DL_TimerG_isRunning(TIMG6) == true)
    {
        return RES_ERROR_NOT_FOUND;
    }
    return RES_SUCCESS;
}

tFunRetunCode HalGeneralTimerLowPowerStart(void)
{
    DL_TimerG_startCounter(TIMG0);
    if (DL_TimerG_isRunning(TIMG0) == false)
    {
        return RES_ERROR_NOT_FOUND;
    }
    return RES_SUCCESS;
}

tFunRetunCode HalGeneralTimerHighPowerStart(void)
{
    DL_TimerG_startCounter(TIMG6);
    if (DL_TimerG_isRunning(TIMG6) == false)
    {
        return RES_ERROR_NOT_FOUND;
    }
    return RES_SUCCESS;
}

tFunRetunCode HalGeneralTimerLowPowerClose(void)
{
    DL_TimerG_reset(TIMG0);
    DL_TimerG_disablePower(TIMG0);
    if (DL_TimerG_isPowerEnabled(TIMG0) == true)
    {
        return RES_ERROR_NOT_FOUND;
    }
    return RES_SUCCESS;
}

tFunRetunCode HalGeneralTimerHighPowerClose(void)
{
    DL_TimerG_reset(TIMG6);
    DL_TimerG_disablePower(TIMG6);
    if (DL_TimerG_isPowerEnabled(TIMG6) == true)
    {
        return RES_ERROR_NOT_FOUND;
    }
    return RES_SUCCESS;
}


void TIMG0_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMG0)) 
    {
      case DL_TIMERG_IIDX_ZERO:
        if (gmTimerG0EvtHandler)
        {
            gmTimerG0EvtHandler(0,0,0);
        }
        break;
      default:
        break;
    }
  
}

void TIMG6_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMG6)) 
    {
      case DL_TIMERG_IIDX_ZERO:
        if (gmTimerG6EvtHandler)
        {
            gmTimerG6EvtHandler(0,0,0);
        }
        break;
      default:
        break;
    }
  
}
