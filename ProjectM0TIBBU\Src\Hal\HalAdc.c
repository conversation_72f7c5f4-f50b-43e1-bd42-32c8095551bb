/*
******************************************************************************
* @file     HalAdc.c
* <AUTHOR>
* @brief    This file is the HAL common function of ADC internal with MSPM0GX51 Driverlib

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "HalAdc.h"
/* Local typedef ------------------------------------------------------------*/
typedef struct
{
    GPIO_Regs* pmGpioPort;
    uint32_t   u32GpioPin;
    uint32_t   u32TiAdcChanSelect;
}tHalAdcPinSelect; 

typedef struct 
{
    GPIO_Regs*   pmGpioPort;
    uint32_t     u32GpioPin;
    uint8_t      u8MemIdx;
    uint32_t     u32InputChan;
    ADC12_Regs*  pAdcInst;
    bool         bInUse;
}tHalAdcPinRegisterMap;

/* Local define -------------------------------------------------------------*/
#define HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT    (4)

#if 0
#define HAL_ADC_TIMER_IRQ_DEBUG_PIN_ENABLE
#endif

#if 0 
#define HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
#endif

#if 0 
#define HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
#endif

#if 1
#define HAL_ADC_TRIG_SRC_TIMER
#endif

#ifdef  HAL_ADC_TRIG_SRC_TIMER
#define HAL_ADC_TRIG_SRC                           (DL_ADC12_TRIG_SRC_EVENT)
#define HAL_ADC_SAMPLING_SRC                       (DL_ADC12_SAMPLING_SOURCE_MANUAL)

#define HAL_ADC_SR_CNT_1MS                         16     //16   = 1ms   * 16384Hz rounding
#define HAL_ADC_SR_CNT_2MS                         33     //33   = 2ms   * 16384Hz rounding
#define HAL_ADC_SR_CNT_5MS                         82     //82   = 5ms   * 16384Hz rounding      
#define HAL_ADC_SR_CNT_10MS                        164    //165  = 10ms  * 16384Hz rounding 
#define HAL_ADC_SR_CNT_50MS                        819    //819  = 50ms  * 16384Hz rounding     
#define HAL_ADC_SR_CNT_100MS                       1638   //1638 = 100ms * 16384Hz rounding         
#else
#define HAL_ADC_TRIG_SRC                           (DL_ADC12_TRIG_SRC_SOFTWARE)
#define HAL_ADC_SAMPLING_SRC                       (DL_ADC12_SAMPLING_SOURCE_AUTO)

#define HAL_ADC_SR_CNT_1MS                         1000
#define HAL_ADC_SR_CNT_2MS                         1000   //Not supported, because SCOMP only has 10 bits(0~1023)
#define HAL_ADC_SR_CNT_5MS                         1000   //Not supported, because SCOMP only has 10 bits(0~1023)
#define HAL_ADC_SR_CNT_10MS                        1000   //Not supported, because SCOMP only has 10 bits(0~1023)
#define HAL_ADC_SR_CNT_50MS                        1000   //Not supported, because SCOMP only has 10 bits(0~1023)    
#define HAL_ADC_SR_CNT_100MS                       1000   //Not supported, because SCOMP only has 10 bits(0~1023)   
#endif

#define HAL_ADC0_TOTAL_PIN_AMOUNT                  (13)
#define HAL_ADC1_TOTAL_PIN_AMOUNT                  (14)
#define HAL_ADC_PORTA_TOTAL_PIN_AMOUNT             (16)
#define HAL_ADC_PORTB_TOTAL_PIN_AMOUNT             (11)

#define HAL_ADC_MAX_ADC_MEM_SIZE                   (12)
#define HAL_ADC_GET_DATA_INVALID_VALUE             (0xFFFF)


#define ADC12_REF                                  (DL_ADC12_REFERENCE_VOLTAGE_VDDA_VSSA)
#define ADC12_REF_VOLTAGE_V                        (3.3)

/* Defines for TIMER_2 as HAL ADC clock source*/
#define HAL_ADCTIMER_INST                          (TIMG8)
#define HAL_ADCTIMER_INST_IRQHandler               (TIMG8_IRQHandler)
#define HAL_ADCTIMER_INST_INT_IRQN                 (TIMG8_INT_IRQn)

#define HAL_ADCTIMER_INST_LOAD_DEFAULT_VALUE       (164U)     //One channel sampling time = 10ms

#define HAL_ADCTIMER_INST_PUB_0_CH                 (1)
#define HAL_ADCTIMER_INST_PUB_1_CH                 (2)

/* Defines for ADC12_0 */
/*----------------------------------------------------------------------------*/
#define ADC12_0_INST                               (ADC0)
#define ADC12_0_INST_IRQHandler                    (ADC0_IRQHandler)
#define ADC12_0_INST_INT_IRQN                      (ADC0_INT_IRQn)
#define ADC12_0_INST_SUB_CH                        (1)

/* Defines for ADC12_1 */
/*----------------------------------------------------------------------------*/
#define ADC12_1_INST                               (ADC1)
#define ADC12_1_INST_IRQHandler                    (ADC1_IRQHandler)
#define ADC12_1_INST_INT_IRQN                      (ADC1_INT_IRQn)
#define ADC12_1_INST_SUB_CH                        (2)

/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
static bool HalAdcIsPortAPinAvailable(uint32_t u32GpioPin);
static bool HalAdcIsPortBPinAvailable(uint32_t u32GpioPin);
static uint32_t HalAdcGetEndAddValue(uint32_t u32MemIdxCnt);
static bool HalAdcFindInputChannel(GPIO_Regs* pmGpioPort, uint32_t u32GpioPin, uint32_t* u32InputChan, ADC12_Regs** pmAdcInst);

static void ToggleDebugIOPA9Delay(uint32_t u32Times, uint32_t u32delaycnt);
static void ToggleDebugIOPA14Delay(uint32_t u32Times, uint32_t u32delaycnt);

/* Global variables ---------------------------------------------------------*/
static const tHalAdcPinSelect gmMcuAdcPinMap0[HAL_ADC0_TOTAL_PIN_AMOUNT] = 
{
    {GPIOA, DL_GPIO_PIN_27, DL_ADC12_INPUT_CHAN_0}, // A0_0
    {GPIOA, DL_GPIO_PIN_26, DL_ADC12_INPUT_CHAN_1}, // A0_1
    {GPIOA, DL_GPIO_PIN_25, DL_ADC12_INPUT_CHAN_2}, // A0_2
    {GPIOA, DL_GPIO_PIN_24, DL_ADC12_INPUT_CHAN_3}, // A0_3
    {GPIOB, DL_GPIO_PIN_25, DL_ADC12_INPUT_CHAN_4}, // A0_4
    {GPIOB, DL_GPIO_PIN_24, DL_ADC12_INPUT_CHAN_5}, // A0_5
    {GPIOB, DL_GPIO_PIN_20, DL_ADC12_INPUT_CHAN_6}, // A0_6
    {GPIOA, DL_GPIO_PIN_22, DL_ADC12_INPUT_CHAN_7}, // A0_7
    {GPIOA, DL_GPIO_PIN_12, DL_ADC12_INPUT_CHAN_8}, // A0_8
    {GPIOA, DL_GPIO_PIN_13, DL_ADC12_INPUT_CHAN_9}, // A0_9
    {GPIOA, DL_GPIO_PIN_14, DL_ADC12_INPUT_CHAN_12}, // A0_12
    {GPIOA, DL_GPIO_PIN_19, DL_ADC12_INPUT_CHAN_13}, // A0_13
    {GPIOA, DL_GPIO_PIN_20, DL_ADC12_INPUT_CHAN_14}, // A0_14
};

static const tHalAdcPinSelect gmMcuAdcPinMap1[HAL_ADC1_TOTAL_PIN_AMOUNT] =
{
    {GPIOA, DL_GPIO_PIN_15, DL_ADC12_INPUT_CHAN_0}, // A1_0
    {GPIOA, DL_GPIO_PIN_16, DL_ADC12_INPUT_CHAN_1}, // A1_1
    {GPIOA, DL_GPIO_PIN_17, DL_ADC12_INPUT_CHAN_2}, // A1_2
    {GPIOA, DL_GPIO_PIN_18, DL_ADC12_INPUT_CHAN_3}, // A1_3
    {GPIOB, DL_GPIO_PIN_17, DL_ADC12_INPUT_CHAN_4}, // A1_4
    {GPIOB, DL_GPIO_PIN_18, DL_ADC12_INPUT_CHAN_5}, // A1_5
    {GPIOB, DL_GPIO_PIN_19, DL_ADC12_INPUT_CHAN_6}, // A1_6
    {GPIOA, DL_GPIO_PIN_21, DL_ADC12_INPUT_CHAN_7}, // A1_7
    {GPIOB, DL_GPIO_PIN_21, DL_ADC12_INPUT_CHAN_8}, // A1_8
    {GPIOB, DL_GPIO_PIN_22, DL_ADC12_INPUT_CHAN_10}, // A1_10
    {GPIOB, DL_GPIO_PIN_23, DL_ADC12_INPUT_CHAN_11}, // A1_11
    {GPIOA, DL_GPIO_PIN_23, DL_ADC12_INPUT_CHAN_12}, // A1_12
    {GPIOB, DL_GPIO_PIN_26, DL_ADC12_INPUT_CHAN_13}, // A1_13
    {GPIOB, DL_GPIO_PIN_27, DL_ADC12_INPUT_CHAN_14}  // A1_14
};

static const uint32_t gePortAPinMap[HAL_ADC_PORTA_TOTAL_PIN_AMOUNT] = 
{
    DL_GPIO_PIN_27,
    DL_GPIO_PIN_26,
    DL_GPIO_PIN_25,
    DL_GPIO_PIN_24,
    DL_GPIO_PIN_22,
    DL_GPIO_PIN_12,
    DL_GPIO_PIN_13,
    DL_GPIO_PIN_14,
    DL_GPIO_PIN_19,
    DL_GPIO_PIN_20,
    DL_GPIO_PIN_15,
    DL_GPIO_PIN_16,
    DL_GPIO_PIN_17,
    DL_GPIO_PIN_18,
    DL_GPIO_PIN_21,
    DL_GPIO_PIN_23
};

static const uint32_t gePortBPinMap[HAL_ADC_PORTB_TOTAL_PIN_AMOUNT] = 
{
    DL_GPIO_PIN_25,
    DL_GPIO_PIN_24,
    DL_GPIO_PIN_20,
    DL_GPIO_PIN_17,
    DL_GPIO_PIN_18,
    DL_GPIO_PIN_19,
    DL_GPIO_PIN_21,
    DL_GPIO_PIN_22,
    DL_GPIO_PIN_23,
    DL_GPIO_PIN_26,
    DL_GPIO_PIN_27
};

/*
 * Timer clock configuration to be sourced by LFCLK /  (32768 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   16384 Hz = 32768 Hz / (1 * (1 + 1))
 */
static const DL_TimerG_ClockConfig gmAdcTimerClockConfig = 
{
    .clockSel    = DL_TIMER_CLOCK_LFCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_1,
    .prescale    = 1U,
};

/*
 * Timer load value (where the counter starts from) is calculated as (timerPeriod * timerClockFreq) - 1
 * HAL_ADCTIMER_INST_LOAD_DEFAULT_VALUE = (10ms * 16384 Hz) - 1
 */
static const DL_TimerG_TimerConfig gmAdcTimerTimerConfig = 
{
    .period     = HAL_ADCTIMER_INST_LOAD_DEFAULT_VALUE,
    .timerMode  = DL_TIMER_TIMER_MODE_PERIODIC,
    .startTimer = DL_TIMER_STOP,
};

// ADC12_0 Initialization
static const DL_ADC12_ClockConfig gmAdc0ClockConfig = 
{
    .clockSel       = ADC12_CLKCFG_SAMPCLK_SYSOSC,        // Setting 32MHz
    .divideRatio    = DL_ADC12_CLOCK_DIVIDE_32,           // 32MHz / 32 = 1MHz= SAMPCLK
    .freqRange      = DL_ADC12_CLOCK_FREQ_RANGE_32_TO_40, // To avoid error, You must select same ADCCLK freq.
};

// ADC12_1 Initialization 
static const DL_ADC12_ClockConfig gmAdc1ClockConfig = 
{
    .clockSel       = ADC12_CLKCFG_SAMPCLK_SYSOSC,        // Setting 32MHz
    .divideRatio    = DL_ADC12_CLOCK_DIVIDE_32,           // 32MHz / 32 = 1MHz= SAMPCLK
    .freqRange      = DL_ADC12_CLOCK_FREQ_RANGE_32_TO_40, // To avoid error, You must select same ADCCLK freq.
};

static tHalAdcPinRegisterMap gmAdcPinRegisterMap[HAL_ADC_MAX_ADC_MEM_SIZE * 2] = {0};
static uint8_t gu8AdcUseChannels   = 0;    //Total (ADC0 + ADC1) Register channel Cnt
static uint8_t gu8Adc0UseMemIdxCnt = 0;    //ADC0 Register Mem Cnt       
static uint8_t gu8Adc1UseMemIdxCnt = 0;    //ADC1 Register Mem Cnt       

/* Local function prototypes ------------------------------------------------*/
static bool HalAdcIsPortAPinAvailable(uint32_t u32GpioPin)
{
    uint8_t u8Id = 0;

    for (u8Id = 0; u8Id < HAL_ADC_PORTA_TOTAL_PIN_AMOUNT; u8Id++)
    {
        if (u32GpioPin == gePortAPinMap[u8Id])
        {
            return true;
        }
    }
    return false;
}

static bool HalAdcIsPortBPinAvailable(uint32_t u32GpioPin)
{
    uint8_t u8Id = 0;
    for (u8Id = 0; u8Id < HAL_ADC_PORTB_TOTAL_PIN_AMOUNT; u8Id++)
    {
        if (u32GpioPin == gePortBPinMap[u8Id])
        {
            return true;
        }
    }
    return false;
}

static tFunRetunCode HalAdcPinEnable(GPIO_Regs *pmGpioPort, uint32_t u32GpioPin)
{
    if (pmGpioPort == GPIOA)
    {
        if (HalAdcIsPortAPinAvailable(u32GpioPin) == false)
        {
            return RES_ERROR_NOT_SUPPORTED;
        }
    }
    else if (pmGpioPort == GPIOB)
    {
        if (HalAdcIsPortBPinAvailable(u32GpioPin) == false)
        {
            return RES_ERROR_NOT_SUPPORTED;
        }
    }
    else
    {
        return RES_ERROR_NOT_SUPPORTED;
    }

    return RES_SUCCESS;
}

static uint32_t HalAdcGetEndAddValue(uint32_t u32MemIdxCnt)
{
    uint32_t u32MemIdxSet;
    if (u32MemIdxCnt > HAL_ADC_MAX_ADC_MEM_SIZE)
    {  
        u32MemIdxCnt = HAL_ADC_MAX_ADC_MEM_SIZE;
    }

    u32MemIdxSet = ((u32MemIdxCnt << ADC12_CTL2_ENDADD_OFS) & ADC12_CTL2_ENDADD_MASK);

    return u32MemIdxSet;
}

/* Internal function to find input channel and ADC instance*/
static bool HalAdcFindInputChannel(GPIO_Regs* pmGpioPort, uint32_t u32GpioPin, uint32_t* u32InputChan, ADC12_Regs** pmAdcInst)
{
    for (uint8_t i = 0; i < HAL_ADC0_TOTAL_PIN_AMOUNT; i++)
    {
        if ((gmMcuAdcPinMap0[i].pmGpioPort == pmGpioPort) && (gmMcuAdcPinMap0[i].u32GpioPin == u32GpioPin))
        {
            *u32InputChan = gmMcuAdcPinMap0[i].u32TiAdcChanSelect;
            *pmAdcInst = ADC12_0_INST;
            return true;
        }
    }
    for (uint8_t i = 0; i < HAL_ADC1_TOTAL_PIN_AMOUNT; i++)
    {
        if ((gmMcuAdcPinMap1[i].pmGpioPort == pmGpioPort) && (gmMcuAdcPinMap1[i].u32GpioPin == u32GpioPin))
        {
            *u32InputChan = gmMcuAdcPinMap1[i].u32TiAdcChanSelect;
            *pmAdcInst = ADC12_1_INST;
            return true;
        }
    }
    return false;
}

static void ToggleDebugIOPA9Delay(uint32_t u32Times, uint32_t u32delaycnt)
{
    for(int32_t i = 0 ; i < u32Times ; i++)
    {
        DL_GPIO_togglePins(BSP_DEBUG2_PORT, BSP_DEBUG2_PIN);
        delay_cycles(u32delaycnt);
    }
}

static void ToggleDebugIOPA14Delay(uint32_t u32Times, uint32_t u32delaycnt)
{
    for(int32_t i = 0 ; i < u32Times ; i++)
    {
        DL_GPIO_togglePins(BSP_DEBUG0_PORT, BSP_DEBUG0_PIN);
        delay_cycles(u32delaycnt);
    }
}

/* Global function prototypes -----------------------------------------------*/   

// Open ADC Rest and enable Power before ADC setting(Must first use)
//-----------------------------------------------------------------------------
void HalAdcOpen(void)
{
    DL_ADC12_reset(ADC12_0_INST);
    DL_ADC12_enablePower(ADC12_0_INST);    
    
    DL_ADC12_reset(ADC12_1_INST);
    DL_ADC12_enablePower(ADC12_1_INST);
    delay_cycles(HAL_ADC_POWER_STARTUP_DELAY); 

    //Timer clock config
    //-------------------------------------------
    DL_TimerG_reset(HAL_ADCTIMER_INST);
    DL_TimerG_enablePower(HAL_ADCTIMER_INST);
    delay_cycles(HAL_ADC_POWER_STARTUP_DELAY); 

    DL_TimerG_setClockConfig(HAL_ADCTIMER_INST, (DL_TimerG_ClockConfig *) &gmAdcTimerClockConfig);
    DL_TimerG_initTimerMode(HAL_ADCTIMER_INST , (DL_TimerG_TimerConfig *) &gmAdcTimerTimerConfig);
    DL_TimerG_enableClock(HAL_ADCTIMER_INST);

    //Timer as Publisher(route 1), ADC12_0 as Subscriber
    //----------------------------------------
    DL_TimerG_enableEvent(HAL_ADCTIMER_INST, DL_TIMERG_EVENT_ROUTE_1, (DL_TIMERG_EVENT_ZERO_EVENT));
    DL_TimerG_setPublisherChanID(HAL_ADCTIMER_INST, DL_TIMERG_PUBLISHER_INDEX_0, HAL_ADCTIMER_INST_PUB_0_CH);
    //----------------------------------------
    
    //Timer as Publisher(route 2), ADC12_1 as Subscriber
    //----------------------------------------
    DL_TimerG_enableEvent(HAL_ADCTIMER_INST, DL_TIMERG_EVENT_ROUTE_2, (DL_TIMERG_EVENT_ZERO_EVENT));
    DL_TimerG_setPublisherChanID(HAL_ADCTIMER_INST, DL_TIMERG_PUBLISHER_INDEX_1, HAL_ADCTIMER_INST_PUB_1_CH);
    //----------------------------------------

    NVIC_EnableIRQ(HAL_ADCTIMER_INST_INT_IRQN);
    //-------------------------------------------

    delay_cycles(HAL_ADC_POWER_STARTUP_DELAY); 
}
//-----------------------------------------------------------------------------

// Close ADC disable Power setting
//-----------------------------------------------------------------------------
void HalAdcClose(void)
{
    DL_ADC12_disablePower(ADC12_0_INST);    
    DL_ADC12_disablePower(ADC12_1_INST);
    DL_TimerG_disablePower(HAL_ADCTIMER_INST);
}
//-----------------------------------------------------------------------------

// Register ADC input Port and Pin
//-----------------------------------------------------------------------------
tFunRetunCode HalAdcSetPinRegister(GPIO_Regs* pmGpioPort, uint32_t u32GpioPin)
{
    uint32_t u32InputChan = 0;
    ADC12_Regs* pmAdcInst = NULL;
    uint32_t _gu8AdcUseMemIdxCntTemp = 0;

    if (gu8AdcUseChannels >= (HAL_ADC_MAX_ADC_MEM_SIZE * 2))
    {
        return RES_ERROR_FULL;
    }

    if (HalAdcPinEnable(pmGpioPort, u32GpioPin) != RES_SUCCESS)
    {
        return RES_ERROR_NOT_SUPPORTED;
    }

    if (HalAdcFindInputChannel(pmGpioPort, u32GpioPin, &u32InputChan, &pmAdcInst) == false)
    {
        return RES_ERROR_NOT_FOUND;
    }

    if(pmAdcInst == ADC12_0_INST)
    {
        _gu8AdcUseMemIdxCntTemp = gu8Adc0UseMemIdxCnt;
    }
    else
    {        
        _gu8AdcUseMemIdxCntTemp = gu8Adc1UseMemIdxCnt;  
    }

    DL_ADC12_configConversionMem(pmAdcInst,
        (DL_ADC12_MEM_IDX)(_gu8AdcUseMemIdxCntTemp % HAL_ADC_MAX_ADC_MEM_SIZE),
        u32InputChan,
        ADC12_REF,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0,
        DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED,
        DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

    gmAdcPinRegisterMap[gu8AdcUseChannels].pmGpioPort    = pmGpioPort;
    gmAdcPinRegisterMap[gu8AdcUseChannels].u32GpioPin    = u32GpioPin;
    gmAdcPinRegisterMap[gu8AdcUseChannels].u32InputChan  = u32InputChan;
    gmAdcPinRegisterMap[gu8AdcUseChannels].u8MemIdx      = (_gu8AdcUseMemIdxCntTemp % HAL_ADC_MAX_ADC_MEM_SIZE);
    gmAdcPinRegisterMap[gu8AdcUseChannels].pAdcInst      = pmAdcInst;
    gmAdcPinRegisterMap[gu8AdcUseChannels].bInUse        = true;

    if(pmAdcInst == ADC12_0_INST)
    {
        gu8Adc0UseMemIdxCnt++;            
    }
    else
    {
        gu8Adc1UseMemIdxCnt++;           
    }

    gu8AdcUseChannels++;
    return RES_SUCCESS;
}
//-----------------------------------------------------------------------------

// Clear all pin register
//-----------------------------------------------------------------------------
void HalAdcClearAllPinRegister(void)
{
    for (uint8_t i = 0; i < (HAL_ADC_MAX_ADC_MEM_SIZE * 2); i++) 
    {
        gmAdcPinRegisterMap[i].bInUse = false;
    }
    gu8AdcUseChannels   = 0;
    gu8Adc0UseMemIdxCnt = 0;
    gu8Adc1UseMemIdxCnt = 0;
}
//-----------------------------------------------------------------------------

// Start ADC, setting ADC0 and ADC1 condfig enable
//-----------------------------------------------------------------------------
tFunRetunCode HalAdcStart(eTypeHalAdcSampleTime eHalAdcSampleTime)
{
    uint32_t u32SampleTimeClocks;
    uint8_t _u8MaxSampleCnt = 0;

    DL_TimerG_TimerConfig gmSetAdcTimerTimerConfig = {0};

    if (gu8AdcUseChannels == 0)
    {
        return RES_ERROR_NOT_OPEN;
    }

    switch (eHalAdcSampleTime)
    {
        case kHAL_ADC_SAMPLETIME_1MS:
            u32SampleTimeClocks = HAL_ADC_SR_CNT_1MS;
            break;
        case kHAL_ADC_SAMPLETIME_2MS:
            u32SampleTimeClocks = HAL_ADC_SR_CNT_2MS;
            break;
        case kHAL_ADC_SAMPLETIME_5MS:
            u32SampleTimeClocks = HAL_ADC_SR_CNT_5MS;
            break;
        case kHAL_ADC_SAMPLETIME_10MS:
            u32SampleTimeClocks = HAL_ADC_SR_CNT_10MS;
            break;
        case kHAL_ADC_SAMPLETIME_50MS:
            u32SampleTimeClocks = HAL_ADC_SR_CNT_50MS;            
            break;
        case kHAL_ADC_SAMPLETIME_100MS:
            u32SampleTimeClocks = HAL_ADC_SR_CNT_100MS;            
            break;
        default:
            return RES_ERROR_INVALID_PARAM;
    }

    //Because they share the same TIMER trigger source, the maximum number of channels should be selected.
    //-----------------------------------------------
    if(gu8Adc0UseMemIdxCnt > gu8Adc1UseMemIdxCnt)
    {
        _u8MaxSampleCnt = gu8Adc0UseMemIdxCnt;
    }
    else
    {
        _u8MaxSampleCnt = gu8Adc1UseMemIdxCnt;    
    }

    //For Timer trigger as ADC0 and ADC1 same trigger source.
    //--------------------------------------------------
    gmSetAdcTimerTimerConfig.timerMode  = DL_TIMER_TIMER_MODE_PERIODIC;
    gmSetAdcTimerTimerConfig.startTimer = DL_TIMER_STOP;
    gmSetAdcTimerTimerConfig.period     = (u32SampleTimeClocks / _u8MaxSampleCnt); 
    DL_TimerG_initTimerMode(HAL_ADCTIMER_INST , (DL_TimerG_TimerConfig *) &gmSetAdcTimerTimerConfig);
    DL_TimerG_enableClock(HAL_ADCTIMER_INST);
    //---------------------------------------------------

    /*ADC0 sample time and init seq setting */
    //-------------------------------------------------------------------------------
    if(gu8Adc0UseMemIdxCnt > 0)
    {
        uint8_t _u8MemIdxCnt = 0;

        DL_ADC12_setClockConfig(ADC12_0_INST, (DL_ADC12_ClockConfig *) &gmAdc0ClockConfig);
        
        DL_ADC12_clearInterruptStatus(ADC12_0_INST,
                                     (DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM1_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM2_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM3_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM4_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM5_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM6_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM7_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM8_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM9_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM10_RESULT_LOADED|
                                      DL_ADC12_INTERRUPT_MEM11_RESULT_LOADED));
        DL_ADC12_enableInterrupt(ADC12_0_INST,
                                     (DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM1_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM2_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM3_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM4_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM5_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM6_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM7_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM8_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM9_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM10_RESULT_LOADED|
                                      DL_ADC12_INTERRUPT_MEM11_RESULT_LOADED));

        //ADC0 subscrib TIMER event as trigger
        DL_ADC12_setSubscriberChanID(ADC12_0_INST,ADC12_0_INST_SUB_CH);                                         

        //If it is triggered by TIMER, this setting is invalid.
        DL_ADC12_setSampleTime0(ADC12_0_INST, u32SampleTimeClocks / gu8Adc0UseMemIdxCnt);

        if(gu8Adc0UseMemIdxCnt > HAL_ADC_MAX_ADC_MEM_SIZE)
        {
            _u8MemIdxCnt = HAL_ADC_MAX_ADC_MEM_SIZE - 1;
        }
        else
        {
            _u8MemIdxCnt = gu8Adc0UseMemIdxCnt - 1;
        }

        DL_ADC12_initSeqSample( ADC12_0_INST, 
                                DL_ADC12_REPEAT_MODE_ENABLED, 
                                HAL_ADC_SAMPLING_SRC, 
                                HAL_ADC_TRIG_SRC,
                                DL_ADC12_SEQ_START_ADDR_00, 
                                HalAdcGetEndAddValue(_u8MemIdxCnt),
                                DL_ADC12_SAMP_CONV_RES_12_BIT, 
                                DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);
        DL_ADC12_enableConversions(ADC12_0_INST);
        NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);    
    } 
    //-------------------------------------------------------------------------------

    /*ADC1 sample time and init seq setting */
    //-------------------------------------------------------------------------------
    if(gu8Adc1UseMemIdxCnt > 0)
    {
        uint8_t _u8MemIdxCnt = 0;

        DL_ADC12_setClockConfig(ADC12_1_INST, (DL_ADC12_ClockConfig *) &gmAdc1ClockConfig);
        
        DL_ADC12_clearInterruptStatus(ADC12_1_INST,
                                     (DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM1_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM2_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM3_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM4_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM5_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM6_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM7_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM8_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM9_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM10_RESULT_LOADED|
                                      DL_ADC12_INTERRUPT_MEM11_RESULT_LOADED));
        DL_ADC12_enableInterrupt(ADC12_1_INST,
                                     (DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM1_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM2_RESULT_LOADED | 
                                      DL_ADC12_INTERRUPT_MEM3_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM4_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM5_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM6_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM7_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM8_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM9_RESULT_LOADED |
                                      DL_ADC12_INTERRUPT_MEM10_RESULT_LOADED|
                                      DL_ADC12_INTERRUPT_MEM11_RESULT_LOADED));

        //ADC1 subscrib TIMER event as trigger 
        DL_ADC12_setSubscriberChanID(ADC12_1_INST,ADC12_1_INST_SUB_CH);   

        //If it is triggered by TIMER, this setting is invalid.
        DL_ADC12_setSampleTime0(ADC12_1_INST, u32SampleTimeClocks / gu8Adc1UseMemIdxCnt);

        if(gu8Adc1UseMemIdxCnt > HAL_ADC_MAX_ADC_MEM_SIZE)
        {
            _u8MemIdxCnt = HAL_ADC_MAX_ADC_MEM_SIZE - 1;
        }
        else
        {
            _u8MemIdxCnt = gu8Adc1UseMemIdxCnt - 1;
        }

        DL_ADC12_initSeqSample(ADC12_1_INST, 
                               DL_ADC12_REPEAT_MODE_ENABLED, 
                               HAL_ADC_SAMPLING_SRC, 
                               HAL_ADC_TRIG_SRC,
                               DL_ADC12_SEQ_START_ADDR_00, 
                               HalAdcGetEndAddValue(_u8MemIdxCnt),
                               DL_ADC12_SAMP_CONV_RES_12_BIT, 
                               DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);
        DL_ADC12_enableConversions(ADC12_1_INST);

        NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN); 
    } 
    //-------------------------------------------------------------------------------
    DL_TimerG_startCounter(HAL_ADCTIMER_INST);

    //Start ADC
    //----------------------------------------------------
    if(gu8Adc0UseMemIdxCnt > 0)
    {
        DL_ADC12_startConversion(ADC12_0_INST);   
    }

    if(gu8Adc1UseMemIdxCnt > 0)
    {
        DL_ADC12_startConversion(ADC12_1_INST);   
    }
    //----------------------------------------------------

    return RES_SUCCESS;
}
//-----------------------------------------------------------------------------

// Get ADC data
//-----------------------------------------------------------------------------
uint16_t HalAdcGetData(GPIO_Regs* pmGpioPort, uint32_t u32GpioPin)
{
    for (uint8_t i = 0; i < gu8AdcUseChannels; i++) 
    {
        if (gmAdcPinRegisterMap[i].bInUse &&
            gmAdcPinRegisterMap[i].pmGpioPort == pmGpioPort &&
            gmAdcPinRegisterMap[i].u32GpioPin  == u32GpioPin)
        {
            return DL_ADC12_getMemResult(gmAdcPinRegisterMap[i].pAdcInst, (DL_ADC12_MEM_IDX)gmAdcPinRegisterMap[i].u8MemIdx);
        }
    }
    return HAL_ADC_GET_DATA_INVALID_VALUE;  // Invalid value
}
//-----------------------------------------------------------------------------

/* ADC12_0  and ADC12_1 IRQ Handler     */
//-----------------------------------------------------------------------
void ADC12_0_INST_IRQHandler(void)
{
    switch (DL_ADC12_getPendingInterrupt(ADC12_0_INST))
    {
        case DL_ADC12_IIDX_MEM0_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(2,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM1_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(4,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM2_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(6,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM3_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(8,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM4_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(10,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM5_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(12,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM6_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(14,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM7_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(16,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM8_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(18,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM9_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(20,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM10_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(22,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM11_RESULT_LOADED:
            #ifdef HAL_ADC_ADC0_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA14Delay(24,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        default:
            break;
    }
}

void ADC12_1_INST_IRQHandler(void)
{
    switch (DL_ADC12_getPendingInterrupt(ADC12_1_INST))
    {
        case DL_ADC12_IIDX_MEM0_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(2,8);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM1_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(4,8);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM2_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(6,8);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM3_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(8,8);  
            #endif 
            break;
        case DL_ADC12_IIDX_MEM4_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(10,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM5_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(12,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM6_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(14,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM7_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(16,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM8_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(18,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM9_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(20,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM10_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(22,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        case DL_ADC12_IIDX_MEM11_RESULT_LOADED:
            #ifdef HAL_ADC_ADC1_SEQ_CONVERT_DEBUG_PIN_ENABLE
            ToggleDebugIOPA9Delay(24,HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);  
            #endif
            break;
        default:
            break;
    }
}

void HAL_ADCTIMER_INST_IRQHandler(void)
{
    #ifdef HAL_ADC_TIMER_IRQ_DEBUG_PIN_ENABLE 
    ToggleDebugIOPA9Delay(2, HAL_ADC_SEQ_CONVERT_DEBUG_PIN_DELAY_CNT);           
    #endif
}
//----------------------------------------------------------------------------
