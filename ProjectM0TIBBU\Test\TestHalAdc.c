/*
******************************************************************************
* @file     TestHalAdc.c
* <AUTHOR>
* @brief    This file is Test HalAdc function

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include <stdio.h>
#include "Main.h"
#include "LibSoftwareTimerHandler.h"
#include "LibHardwareTimerHandler.h"
#include "TestHalAdc.h"
#include "TestApp.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
#if 1
#define TEST_HALADC_DEBUG_IO
#define TEST_HALADC_DEBUG0_PORT                    (GPIOB)
#define TEST_HALADC_DEBUG0_PIN                     (DL_GPIO_PIN_10)
#define TEST_HALADC_DEBUG0_IOMUX                   (IOMUX_PINCM27) 
#endif

#define TEST_HALADC_RESULT_SIZE                    (30)

#define TEST_HALADC_SENDPACKET_HEAD1               (0x55)
#define TEST_HALADC_SENDPACKET_HEAD2               (0xAA)

#if 0
#define TEST_HALADC_FILL_PATTERN_DATA 
#endif 

/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
static void TestHalAdcExecutionFun(void);
static void TestHalAdcAppSwTimerHandler(__far void *pvDest, uint16_t u16Event, void *pvData);
static void TestHalAdcAppOpen(void);
static void TestHalAdcSendPacket(uint16_t *pu16AdcValues, uint8_t u8NumChannels);
/* Global variables ---------------------------------------------------------*/
uint16_t gu16HalAdcResult[TEST_HALADC_RESULT_SIZE];

/* Local function prototypes ------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/

//Test HalAdc
//---------------------------------------------------------
#define  TEST_HAL_ADC_PIN_CONFIG_MAX_NUM           (4)   

//Note: ADC PORT PIN(MSMP0G3519)
//-----------------------------------------
//    {GPIOA, DL_GPIO_PIN_27}, // A0_0
//    {GPIOA, DL_GPIO_PIN_26}, // A0_1
//    {GPIOA, DL_GPIO_PIN_25}, // A0_2
//    {GPIOA, DL_GPIO_PIN_24}, // A0_3
//    {GPIOB, DL_GPIO_PIN_25}, // A0_4
//    {GPIOA, DL_GPIO_PIN_15}, // A1_0
//    {GPIOA, DL_GPIO_PIN_16}, // A1_1
//    {GPIOA, DL_GPIO_PIN_17}, // A1_2
//    {GPIOA, DL_GPIO_PIN_18}, // A1_3
//    {GPIOB, DL_GPIO_PIN_17}, // A1_4 

static const tHalAdcPortPin gmHalAdcPortPinConfigMap[TEST_HAL_ADC_PIN_CONFIG_MAX_NUM]=
{
    {BSP_ADC_0_PORT, BSP_ADC_0_PIN}, 
    {BSP_ADC_1_PORT, BSP_ADC_1_PIN}, 
    {BSP_ADC_2_PORT, BSP_ADC_2_PIN}, 
    {BSP_ADC_3_PORT, BSP_ADC_3_PIN},  
};

void TestHalAdcSendPacket(uint16_t *pu16AdcValues, uint8_t u8NumChannels) {
    uint8_t u8Packet[35];   // Supported 16 channels
    uint8_t pos = 0;
    uint8_t checksum = 0;

    u8Packet[pos++] = TEST_HALADC_SENDPACKET_HEAD1; // SYNC
    u8Packet[pos++] = TEST_HALADC_SENDPACKET_HEAD2; // SYNC
  
    
    #ifdef TEST_HALADC_FILL_PATTERN_DATA
    u8NumChannels = 10;
    static int32_t test_cnt= 0;
    ++test_cnt;

    if(test_cnt>=100)
    { 
         test_cnt = 0;
    }

    pu16AdcValues[0] = 200   + test_cnt;
    pu16AdcValues[1] = 400   + test_cnt;
    pu16AdcValues[2] = 800   + test_cnt;
    pu16AdcValues[3] = 1000  + test_cnt;
    pu16AdcValues[4] = 1200  + test_cnt;

    pu16AdcValues[5] = 1400  + test_cnt;
    pu16AdcValues[6] = 1600  + test_cnt;
    pu16AdcValues[7] = 1800  + test_cnt;
    pu16AdcValues[8] = 2000  + test_cnt;
    pu16AdcValues[9] = 2200  + test_cnt;
    #endif

    for (int i = 0; i < u8NumChannels; i++)
    {
        uint8_t lsb =  pu16AdcValues[i] & 0xFF;
        uint8_t msb = (pu16AdcValues[i] >> 8) & 0xFF;
        u8Packet[pos++] = lsb;
        u8Packet[pos++] = msb;        
    }

    // Send packet：packet[0] ~ packet[pos - 1]
    TestAppSerialUartBytese(u8Packet , pos);
}

static void TestHalAdcExecutionFun(void)
{   char c8Str[500];

    for(int8_t i = 0 ; i < TEST_HAL_ADC_PIN_CONFIG_MAX_NUM ; i++)
    {
        //Read Adc value by Port Pin maping
        gu16HalAdcResult[i] = HalAdcGetData(gmHalAdcPortPinConfigMap[i].pmGpioPort , gmHalAdcPortPinConfigMap[i].u32GpioPin);            
    }

    TestHalAdcSendPacket(gu16HalAdcResult, TEST_HAL_ADC_PIN_CONFIG_MAX_NUM);

    #ifdef TEST_HALADC_DEBUG_IO
    HalGpioTogglePin(TEST_HALADC_DEBUG0_PORT, TEST_HALADC_DEBUG0_PIN);
    #endif

    BSP_DELAY_CYCLES(160);   
}

static void TestHalAdcInit(void)
{
    //Test debug pin
    #ifdef TEST_HALADC_DEBUG_IO 
    tHalGpioConfig mHalGpioConfig;    
    HalGpioPowerInit();
    mHalGpioConfig.eGpioIoType = kHAL_GPIO_OUTPUT;
    mHalGpioConfig.pmPort = TEST_HALADC_DEBUG0_PORT;
    mHalGpioConfig.u32Pin = TEST_HALADC_DEBUG0_PIN;
    mHalGpioConfig.eInversionType = kHAL_GPIO_INVERSION_DISABLE;
    mHalGpioConfig.eResistorType = kHAL_GPIO_RESISTOR_NONE;
    mHalGpioConfig.eHysteresisType = kHAL_GPIO_HYSTERESIS_DISABLE;
    mHalGpioConfig.eWakeUpType = kHAL_GPIO_WAKEUP_DISABLE;
    mHalGpioConfig.eDriveStrengthType = kHAL_GPIO_DRIVE_STRENGTH_LOW;
    mHalGpioConfig.eHizType = kHAL_GPIO_HIZ_DISABLE;
    HalGpioInit(&mHalGpioConfig);
    #endif

    // You have must first use HalAdcOpen() before ADC pin register config.
    HalAdcOpen();

    // Register ADC Pin(ADC12_0 CH and AD12_1 CH register)
    //------------------------------------------------------
    for(int8_t i = 0 ; i < TEST_HAL_ADC_PIN_CONFIG_MAX_NUM ; i++)
    {
        if(HalAdcSetPinRegister(gmHalAdcPortPinConfigMap[i].pmGpioPort , gmHalAdcPortPinConfigMap[i].u32GpioPin) == RES_SUCCESS)
        {
            #ifdef TEST_HALADC_DEBUG_IO 
            HalGpioTogglePin(BSP_DEBUG2_PORT, BSP_DEBUG2_PIN);  
            BSP_DELAY_CYCLES(HAL_ADC_POWER_STARTUP_DELAY);  
            #endif
        }  
    }
    //------------------------------------------------------

    //Start ADC and setup sampling time   
    //------------------------------------------------------
    if(HalAdcStart(kHAL_ADC_SAMPLETIME_5MS) == RES_SUCCESS)
    {
        #ifdef TEST_HALADC_DEBUG_IO 
        HalGpioTogglePin(BSP_DEBUG2_PORT, BSP_DEBUG2_PIN);  
        BSP_DELAY_CYCLES(HAL_ADC_POWER_STARTUP_DELAY);  
        #endif
    }
    //------------------------------------------------------

    for(int8_t i = 0 ; i < TEST_HAL_ADC_PIN_CONFIG_MAX_NUM ; i++)
    {
        gu16HalAdcResult[i] = 0;    
    }

    #ifdef TEST_HALADC_DEBUG_IO 
    HalGpioClearPin(BSP_DEBUG0_PORT, BSP_DEBUG0_PIN);  
    HalGpioClearPin(BSP_DEBUG1_PORT, BSP_DEBUG1_PIN);   
    HalGpioClearPin(BSP_DEBUG2_PORT, BSP_DEBUG2_PIN); 
    #endif
}

static void TestHalAdcAppSwTimerHandler(__far void *pvDest, uint16_t u16Event, void *pvData)
{   static uint16_t u16Cnt = 0;

    if (u16Event & kLIB_SW_TIMER_EVT_1_MS)
    {
        if((u16Cnt % 5) == 0)
        {
            TestHalAdcExecutionFun();        
        }
        ++u16Cnt;
    }
}

static void TestHalAdcAppOpen(void)
{
    //Can be used if the previous action is not enabled
    //--------------------------------------------------------- 
    #if 0
    HalMcuPeripheralInit();
    #endif
    //--------------------------------------------------------- 

    LibSoftwareTimerHandlerOpen(TestHalAdcAppSwTimerHandler, 0);
}

void TestHalAdcApp(void)
{    
   TestHalAdcAppOpen();   
   TestHalAdcInit();
}
