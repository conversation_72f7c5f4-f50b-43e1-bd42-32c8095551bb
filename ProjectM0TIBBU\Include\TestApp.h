/*
******************************************************************************
* @file     TestApp.h
* <AUTHOR>
* @brief    This file is TestApp function

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __TEST_APP_H__
#define	__TEST_APP_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include <stdint.h>


/* Global define ------------------------------------------------------------*/
/* Global typedef -----------------------------------------------------------*/
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void TestApp(void);
void TestAppSerialUartSendMessage(uint8_t *pu8Str);
void TestAppSerialUartBytese(uint8_t *u8Packet, uint16_t u16Len);
#ifdef __cplusplus
}
#endif

#endif
