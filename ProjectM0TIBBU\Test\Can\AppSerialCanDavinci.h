/**
  ******************************************************************************
  * @file        AppSerialCanDavinci.h
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2021/10/20
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

#ifndef _APP_SERIAL_CAN_DAVINCI_H_
#define _APP_SERIAL_CAN_DAVINCI_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>

#include "AppCanTemp.h"
#include "SmpCanBusProtocol.h"
#include "SmpParameterId.h"
//#include "AppProject.h"
#include "Define.h"
#include "HalCan.h"
#include "HalAfe.h"
#include "HalRtc.h"

#ifdef __cplusplus
extern "C" {
#endif
/* Public typedef -----------------------------------------------------------*/



typedef void (*tAppSerialPacketExe)(tHalCanFrame *pCanPkg);


	
typedef struct {
  uint32_t 	canid;
  uint32_t	mask;
  tAppSerialPacketExe fun;
} tAppSerialSmpCanDecode;

/* Public define ------------------------------------------------------------*/

#define	SCU_ID()				1
//appProjectGetScuId()
#define	CHECK_SMP_CAN_FUN		(0x0fu << 25)
#define	CHECK_SMP_CAN_OBJ		(0xffu << 10)
#define	CHECK_SMP_CAN_SUB		(0x3ff)

#define	CHECK_SMP_CAN_SCU_ID	(0x07fu << 18)
	
#define	SMP_CAN_GET_FUN(id)			((id >> 25) & 0x0f)
#define	SMP_CAN_GET_SCU_ID(id)		((id >> 18) & 0x7f)
#define	SMP_CAN_GET_OBJ_INDEX(id)	((id >> 10) & 0xff)
#define	SMP_CAN_GET_SUB_INDEX(id)	(id & 0x3ff)

	
#define	MAKE_SMP_CAN_ID(fun, bmuid, obj, sub)	(((uint32_t)fun<<25)|((uint32_t)bmuid<<18)|((uint32_t)obj<<10)|(uint32_t)sub)

#define SMP_CAN_DECODE_CMD_START(name) const tAppSerialSmpCanDecode name[] = {
#define SMP_CAN_DECODE_CMD_CONTENT(canid, mask, fun) {canid, mask, (tAppSerialPacketExe)fun},
#define SMP_CAN_DECODE_CMD_END() {0, 0, 0}}


/* Public macro -------------------------------------------------------------*/

/* Public function prototypes -----------------------------------------------*/
uint8_t appSerialCanDavinciIsCorrectScuId(tHalCanFrame *pCanPkg);
void appSerialCanDavinciOpen(void);
void appSerialCanDavinciClose(void);
int8_t appSerialCanDavinciPutPkgToCanFifo(tHalCanFrame *pCanPkg);
int8_t appSerialCanDavinciPutPkgToCanRxFifo(tHalCanFrame *pCanPkg);
void appSerialCanDavinciSendTextMessage(char *msg);
void appSerialCanDavinciSendGpioStatus(uint32_t mask, uint32_t ststus);
uint16_t AppSerialCan0DavinciGetTxFifoFreeSize(void);

extern uint16_t gu16CellVoltageBuffer[MAX_CELL_NUMBER];

#ifdef __cplusplus
}
#endif


	

#endif /* _APP_SERIAL_UART_H_ */

/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    


