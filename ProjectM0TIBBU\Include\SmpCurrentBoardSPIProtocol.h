/**
 ******************************************************************************
 * @file        SmpCurrentBoardSPIProtocol.h
 * <AUTHOR>
 * @version     v0.0.3
 * @date        2023/06/26
 * @brief
 ******************************************************************************
 * @attention
 *
 * <h2><center>&copy; COPYRIGHT(c) 2023 SMP ESS </center></h2>
 *
 *
 ******************************************************************************
 */
#ifndef _SMP_CURRENTBOARD_PROTOCOL_H_
#define _SMP_CURRENTBOARD_PROTOCOL_H_

#ifdef __cplusplus
extern "C"
{
#endif
  /* Includes ------------------------------------------------------------------*/
  /* Public define -------------------------------------------------------------*/
  
  // Register Address 
  #define SMP_CUR_BOARD_DID_R_STATUS 		    0x00
  #define SMP_CUR_BOARD_DID_W_MOSCTRL 		  0x01
  #define SMP_CUR_BOARD_DID_W_HWOCPREL 		  0x02
  #define SMP_CUR_BOARD_DID_R_CURR 			    0x03
  #define SMP_CUR_BOARD_DID_R_CURRADC 		  0x04
  #define SMP_CUR_BOARD_DID_W_CALA_CURADC   0x05
  #define SMP_CUR_BOARD_DID_W_CALA_CUR  	  0x06
  #define SMP_CUR_BOARD_DID_W_CALB_CURADC   0x07
  #define SMP_CUR_BOARD_DID_W_CALB_CUR 		  0x08
	#define SMP_CUR_BOARD_DID_R_CALA_CURADC   0x09
  #define SMP_CUR_BOARD_DID_R_CALA_CUR  	  0x0A
  #define SMP_CUR_BOARD_DID_R_CALB_CURADC   0x0B
  #define SMP_CUR_BOARD_DID_R_CALB_CUR 		  0x0C
	#define SMP_CUR_BOARD_DID_R_PACKV				  0x0D
	#define SMP_CUR_BOARD_DID_R_PACKV_ADC		  0x0E
	#define SMP_CUR_BOARD_DID_W_CALA_PACKVADC 0x0F
  #define SMP_CUR_BOARD_DID_W_CALA_PACKV  	0x10
  #define SMP_CUR_BOARD_DID_W_CALB_PACKVADC 0x11
  #define SMP_CUR_BOARD_DID_W_CALB_PACKV 		0x12
	#define SMP_CUR_BOARD_DID_R_CALA_PACKVADC 0x13
  #define SMP_CUR_BOARD_DID_R_CALA_PACKV  	0x14
  #define SMP_CUR_BOARD_DID_R_CALB_PACKVADC 0x15
  #define SMP_CUR_BOARD_DID_R_CALB_PACKV 		0x16
	#define SMP_CUR_BOARD_DID_W_HWCOCP_PAR		0x17
	#define SMP_CUR_BOARD_DID_R_HWCOCP_PAR		0x18
	#define SMP_CUR_BOARD_DID_W_HWDOCP_PAR		0x19
	#define SMP_CUR_BOARD_DID_R_HWDOCP_PAR		0x1A
	#define SMP_CUR_BOARD_DID_W_HWSHORTP_PAR	0x1B
	#define SMP_CUR_BOARD_DID_R_HWSHORTP_PAR	0x1C
	#define SMP_CUR_BOARD_DID_R_CUM_COULM     0x1D 
	#define SMP_CUR_BOARD_DID_R_CUM_COULM_ADC 0x1E 
	#define SMP_CUR_BOARD_DID_R_CUM_TIME      0x1F 
	
  // Data meaning
  #define SMP_CUR_BOARD_CFET_OFF_DFET_OFF   0x00
  #define SMP_CUR_BOARD_CFET_OFF_DFET_ON    0x01
  #define SMP_CUR_BOARD_CFET_ON_DFET_OFF    0x02
  #define SMP_CUR_BOARD_CFET_ON_DFET_ON     0x03
  
  #define SMP_CUR_BOARD_REL_HWCOCP          0x00
  #define SMP_CUR_BOARD_REL_HWDOCP          0x01
  
#ifdef __cplusplus
}
#endif

#endif