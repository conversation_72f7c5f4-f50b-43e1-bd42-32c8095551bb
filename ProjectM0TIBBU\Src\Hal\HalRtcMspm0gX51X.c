/*
******************************************************************************
* @file     HalRtcMspm0gX51X.c
* <AUTHOR> Chen
* @brief    This file is the HAL common function of real-time clock with MSPM0GX51 Driverlib

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "HalRtc.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
#define HAL_RTC_CHECK_LOOP_CNT                     (480000) 
#define HAL_RTC_WRITE_TIME_CNT                     (16000)  
#define HAL_RTC_CHECK_COUNT_THS                    (3)               
#define HAL_RTC_WRITE_RETRY_THS                    (5)  

#if 1
#define HAL_RTC_DEBUGRUN_EANLE     //Continue to operate normally ignoring the debug state of the CPU. 
#endif

/* Local typedef ------------------------------------------------------------*/
static const DL_RTC_B_Calendar gmRtcDefalutCalendarConfig = {
    .seconds    = 0,    /* Seconds      = 0 */
    .minutes    = 0,    /* Minute       = 0 */
    .hours      = 0,    /* Hour         = 0 */
    .dayOfWeek  = 5,    /* Day of week  = 1 (Friday) */
    .dayOfMonth = 1,    /* Day of month = 1 */
    .month      = 1,    /* Month        = 1 (January) */
    .year       = 2021, /* Year         = 2021 */
};

/* Local macro --------------------------------------------------------------*/
#define pHalRtcReg                                 (RTC_B)

/* Local function declare ---------------------------------------------------*/
/*Local variables*/
static tHalRtcCallbackHandler pmRtcPrescaler0Cb = NULL;
static tHalRtcCallbackHandler pmRtcPrescaler1Cb = NULL;
static tHalRtcCallbackHandler pmRtcReadyCb      = NULL;

/* Global variables ---------------------------------------------------------*/
uint32_t gu32RtcTimeCnt = 0;
uint32_t gu32RtcRdyCheckCnt = 0;
uint32_t gu32RtcWriteRetrykCnt = 0;
/* Local function prototypes ------------------------------------------------*/
static tFunRetunCode HalRtcCheckRdy(void);

static tFunRetunCode HalRtcCheckRdy(void)
{
    gu32RtcRdyCheckCnt = 0;
    //There is a short delay to test the RTC clock and determine whether it is normal(delay=6ms).
    //--------------------------------------------------------
    delay_cycles(HAL_RTC_CHECK_LOOP_CNT);
    if(gu32RtcRdyCheckCnt >= HAL_RTC_CHECK_COUNT_THS)
    {
        return(RES_SUCCESS);          
    }
    //------------------------------------------------------
    return(RES_ERROR_FAIL);
}

//Low frequency subsystem for pHalRtcReg IRQ handle
//-----------------------------------------------------------------------------
void LFSS_IRQHandler(void)
{      
    uint32_t u32RtcIIdxRetCode = 0;

    u32RtcIIdxRetCode = DL_RTC_B_getPendingInterrupt(pHalRtcReg);
    switch (u32RtcIIdxRetCode)
    {
        case DL_RTC_B_IIDX_PRESCALER0:
            if (pmRtcPrescaler0Cb != NULL)
            {
                pmRtcPrescaler0Cb();
            }
            gu32RtcRdyCheckCnt++;
            break;
        case DL_RTC_B_IIDX_PRESCALER1:
            if (pmRtcPrescaler1Cb != NULL)
            {
                pmRtcPrescaler1Cb();
            }
            gu32RtcTimeCnt++;            
            break;
        case DL_RTC_B_IIDX_READY:     
            if (pmRtcReadyCb != NULL)
            {
                pmRtcReadyCb();      
            }                
            break;
        default:
            break;
    }
}
//-----------------------------------------------------------------------------

/* Global function prototypes -----------------------------------------------*/
tFunRetunCode HalRtcOpen(void)
{
    // Set RT0PS and RT1PS Reg pre-divided frequency (Prseclaer0 is /8 ,Prseclaer1 is /128, Prseclaer2 for 3519, it is useless , interrupt per second)
    DL_RTC_B_setPrescalerEvents(pHalRtcReg, 
                                DL_RTC_COMMON_PRESCALER0_DIVIDE_8,
                                DL_RTC_COMMON_PRESCALER1_DIVIDE_128,
                                DL_RTC_COMMON_PRESCALER2_4_SEC);

    // Setting pending interrput with Presecaler1 enable.
    DL_RTC_B_enableInterrupt(pHalRtcReg, (DL_RTC_COMMON_INTERRUPT_PRESCALER0 | 
                                     DL_RTC_COMMON_INTERRUPT_PRESCALER1 | 
                                     DL_RTC_COMMON_INTERRUPT_READY));

    // Setting pending interrput with Presecaler1 clear status.
    DL_RTC_B_clearInterruptStatus(pHalRtcReg, (DL_RTC_COMMON_INTERRUPT_PRESCALER0 | 
                                          DL_RTC_COMMON_INTERRUPT_PRESCALER1 |
                                          DL_RTC_COMMON_INTERRUPT_READY)); 

    #ifdef HAL_RTC_DEBUGRUN_EANLE
    //Continue to operate normally ignoring the debug state of the CPU. 
    pHalRtcReg->DBGCTL |= RTC_DBGCTL_DBGRUN_RUN;
    #else
    //Counter is halted if CPU is in debug state.
    pHalRtcReg->DBGCTL &= ~RTC_DBGCTL_DBGRUN_RUN;
    #endif

    /* Enable RTC interrupts on device */
    NVIC_EnableIRQ(RTC_B_INT_IRQn);

    /* Start RTC clock */
    DL_RTC_B_enableClockControl(pHalRtcReg);

    if(HalRtcCheckRdy() == RES_SUCCESS)
    {
        return(RES_SUCCESS);
    }
    
    return(RES_ERROR_INIT);      
}

tFunRetunCode HalRtcClose(void)
{  
    DL_RTC_B_disableClockControl(pHalRtcReg);
    return(RES_SUCCESS);
}

tFunRetunCode HalRtcSetupDate(uint16_t u16Year, uint8_t u8Month, uint8_t u8Day)
{
    DL_RTC_B_Calendar mRtcGetCalendar = {0};

    gu32RtcWriteRetrykCnt = 0;

    while(1)
    {
        mRtcGetCalendar = DL_RTC_B_getCalendarTime(pHalRtcReg);

        if((mRtcGetCalendar.year      == u16Year) &&
           (mRtcGetCalendar.month     == u8Month) &&
           (mRtcGetCalendar.dayOfMonth == u8Day)  )  
        {
           break;
        }

        mRtcGetCalendar.year       = u16Year;
        mRtcGetCalendar.month      = u8Month;
        mRtcGetCalendar.dayOfMonth = u8Day;

        /*Setup RTC Calendar */
        DL_RTC_B_initCalendar(pHalRtcReg, mRtcGetCalendar, DL_RTC_B_FORMAT_BINARY);

        /* Wait for the write time to avoid overwriting next time (~200us)*/
        delay_cycles(HAL_RTC_WRITE_TIME_CNT);

        gu32RtcWriteRetrykCnt++;
        if(gu32RtcWriteRetrykCnt >= HAL_RTC_WRITE_RETRY_THS)
        {
            return(RES_ERROR_FAIL);    
        }
    }
    
    return(RES_SUCCESS);
}

tFunRetunCode HalRtcSetupTime(uint8_t u8Hour, uint8_t u8Min, uint8_t u8Sec)
{
    DL_RTC_B_Calendar mRtcGetCalendar;
    
    gu32RtcWriteRetrykCnt = 0;

    while(1)
    {
        mRtcGetCalendar = DL_RTC_B_getCalendarTime(pHalRtcReg);

        if((mRtcGetCalendar.hours   == u8Hour) &&
           (mRtcGetCalendar.minutes == u8Min)  &&
           (mRtcGetCalendar.seconds == u8Sec)  )  
        {
           break;
        }

        mRtcGetCalendar.hours   = u8Hour;
        mRtcGetCalendar.minutes = u8Min;
        mRtcGetCalendar.seconds = u8Sec;

        /*Setup RTC Calendar */
        DL_RTC_B_initCalendar(pHalRtcReg, mRtcGetCalendar, DL_RTC_B_FORMAT_BINARY);

        /* Wait for the write time to avoid overwriting next time (~200us)*/
        delay_cycles(HAL_RTC_WRITE_TIME_CNT);

        gu32RtcWriteRetrykCnt++;
        if(gu32RtcWriteRetrykCnt >= HAL_RTC_WRITE_RETRY_THS)
        {
            return(RES_ERROR_FAIL);    
        }
    }

    return(RES_SUCCESS);
}
    
void HalRtcGetDateTime(tHalRtcDateTime *pmDateTime)
{
    DL_RTC_B_Calendar mRtcGetCalendar;
    
    mRtcGetCalendar = DL_RTC_B_getCalendarTime(pHalRtcReg);
    pmDateTime->u16Year  = mRtcGetCalendar.year;
    pmDateTime->u8Month  = mRtcGetCalendar.month;
    pmDateTime->u8Day    = mRtcGetCalendar.dayOfMonth;
    pmDateTime->u8Hour   = mRtcGetCalendar.hours; 
    pmDateTime->u8Minute = mRtcGetCalendar.minutes;
    pmDateTime->u8Second = mRtcGetCalendar.seconds;
}

/* Unix Time calculated from 2000/1/1 00:00:00 UTC (Self define, since uint32_t format can represent space saving)*/
uint32_t HalRtcGetSelfUnixTime(void)
{
	uint32_t baseday, day, sec, yearoff;
	uint8_t  i;
    uint8_t  iMonthDay[12] ={31,28,31,30,31,30,31,31,30,31,30,31};

    tHalRtcDateTime	mRtcDateTime;
	HalRtcGetDateTime(&mRtcDateTime);

    yearoff = (mRtcDateTime.u16Year % 400);           //  146097*4+135140
    baseday = (mRtcDateTime.u16Year / 400) * 146097L; // - 716241L;
    if (yearoff > 0)
        baseday += yearoff*365u + ((yearoff + 3) / 4) - (yearoff - 1) / 100;

	baseday -= (146097L * 5L);

    if(yearoff & 3)
        iMonthDay[1] = 28;
    else
    {
        if((yearoff % 400)==0)
            iMonthDay[1] = 29;
        else if((yearoff % 100)==0)
            iMonthDay[1] = 28;
        else
            iMonthDay[1] = 29;
    }
    for (day = 0, i = 1; i <mRtcDateTime.u8Month; i++)
        day += iMonthDay[i-1];
    day +=(baseday +mRtcDateTime.u8Day-1);

	day *= 86400L;
  	sec = mRtcDateTime.u8Hour * 3600;
  	sec += mRtcDateTime.u8Minute * 60;
  	sec += mRtcDateTime.u8Second;
	sec += day;
    return sec;
}

/* Unix Time calculated from 2000/1/1 00:00:00 UTC (Self define, since uint32_t format can represent space saving)*/
void HalRtcSelfUnixTimeToDateTime(uint32_t u8Sec, tHalRtcDateTime *pmRtcDateTime)
{
 	uint8_t	iMonthDay[12] ={31,28,31,30,31,30,31,31,30,31,30,31};
    uint32_t yearoff;

	uint8_t	    i;
	uint32_t	dayoffset;
	uint32_t	yearday;

	dayoffset = u8Sec / 86400L;

    pmRtcDateTime->u8Second = (u8Sec % 60);
    pmRtcDateTime->u8Minute = (u8Sec % 3600) / 60;
    pmRtcDateTime->u8Hour   = (u8Sec % 86400L) / 3600;

    /* Unix Time calculated from 2000/1/1 00:00:00 UTC */
    yearoff = 2000;

    while(dayoffset > 36524L)	//100¦~
    {
    	dayoffset -= 36524L;
    	yearoff += 100;
    }

  	while(dayoffset >= 1461)	//¦~
    {
    	yearoff += 4;
    	dayoffset -= 1461;
    }

    while(1)
    {
    	if((yearoff % 400) == 0)
    		yearday = 366;
    	else if((yearoff % 100) == 0)
    		yearday = 365;
    	else if((yearoff % 4) == 0)
    		yearday = 366;
    	else
    		yearday = 365;

    	if(dayoffset < yearday)
    		break;
    	dayoffset -= yearday;
    	yearoff++;
    }
    if((yearoff % 400) == 0)
    	iMonthDay[1] = 29;
   	else if((yearoff%100) == 0)
 		iMonthDay[1] = 28;
   	else if((yearoff%4) == 0)
		iMonthDay[1] = 29;
   	else
		iMonthDay[1] = 28;
    for (i = 0; i < 12; i++)
    {
        if(dayoffset < iMonthDay[i])
            break;
        dayoffset -= iMonthDay[i];
    }
    pmRtcDateTime->u16Year  = yearoff;
    pmRtcDateTime->u8Month  = i + 1;
    pmRtcDateTime->u8Day    = dayoffset + 1;
}

bool HalRtcIsRtcValid(void)
{
    bool bRetRtcValid = false;
    bool bRtcModClkEn = false;
    bool bRtcSafeRead = false;

    if((pHalRtcReg->CLKCTL & RTC_CLKCTL_MODCLKEN_ENABLE) == RTC_CLKCTL_MODCLKEN_ENABLE)
    {
        bRtcModClkEn = true;
    }
    else
    {
        bRtcModClkEn = false;
    } 

    /* Keep-out window is approximately 128/32768 seconds (~4ms) before the counters update */
    /* The actual measurement takes more than stable time > 6ms*/
    //bRtcSafeRead = DL_RTC_B_isSafeToRead(pHalRtcReg);
    
    if(HalRtcCheckRdy() == RES_SUCCESS)
    {
        bRtcSafeRead = true;
    }
 
    if((bRtcModClkEn == true) && (bRtcSafeRead == true))
    {
       bRetRtcValid = true; 
    }
    else
    {
       bRetRtcValid = false; 
    }

    return(bRetRtcValid);
}

void HalRtcRegisterPrescaler0Callback(tHalRtcCallbackHandler mCallBackfun) {
    pmRtcPrescaler0Cb = mCallBackfun;
}

void HalRtcRegisterPrescaler1Callback(tHalRtcCallbackHandler mCallBackfun) {
    pmRtcPrescaler1Cb = mCallBackfun;
}

void HalRtcRegisterReadyCallback(tHalRtcCallbackHandler mCallBackfun) {
    pmRtcReadyCb = mCallBackfun;
}

uint32_t HalRtcGetPrescaler1Cnt(void)
{
    return(gu32RtcTimeCnt);
}

void HalRtcSetPrescaler1Cnt(uint32_t u32SetCnt)
{
    gu32RtcTimeCnt = u32SetCnt;
}
