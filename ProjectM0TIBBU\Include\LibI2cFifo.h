/*
******************************************************************************
* @file     LibI2cFifo.h
* <AUTHOR>
* @brief    This file include MSPM0G3519 I2c FIFO Library Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __LIBI2CFIFO_H__
#define	__LIBI2CFIFO_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h" 
/* Global define ------------------------------------------------------------*/
/* Global typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t		*pu8FifoStartAddr;	     /* FIFO queue buffer start address */
	uint16_t	u16FifoSize;	         /* FIFO queue buffer size */
	uint16_t	u16FifoPushInPosi;		 /* FIFO queue buffer push in position */
	uint16_t	u16FifoPopOutPosi;		 /* FIFO queue buffer pop out position */
}tLibI2cFifoStatus;
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
int8_t LibI2cFifoPush(tLibI2cFifoStatus* pmI2cFifo, uint8_t u8Value);
int8_t LibI2cFifoPop(tLibI2cFifoStatus* pmI2cFifo, uint8_t *pu8Value);
int8_t LibI2cFifoGetUsedSize(tLibI2cFifoStatus* pmI2cFifo, uint16_t *pu16RemainSize);

#ifdef __cplusplus
}
#endif

#endif