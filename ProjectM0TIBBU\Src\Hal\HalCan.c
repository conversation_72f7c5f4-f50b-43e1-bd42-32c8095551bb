/*
******************************************************************************
* @file     <PERSON><PERSON><PERSON>.c
* <AUTHOR> & <PERSON> Lee
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "<PERSON><PERSON>an.h"
/* Local typedef ------------------------------------------------------------*/
typedef enum
{
    kHAL_CAN_FUNCTION_PIN_TX = 0,
    kHAL_CAN_FUNCTION_PIN_RX,
    kHAL_CAN_FUNCTION_PIN_COUNT
} eTypeHalCanFunctionPin;

typedef enum
{
    kHAL_CAN_CHANNEL_0_IRQ = CANFD0_INT_IRQn,
    kHAL_CAN_CHANNEL_1_IRQ = CANFD1_INT_IRQn,
} eTypeHalCanChannelIrq;

typedef enum
{
    kHAL_CAN_BIT_TIMING_NOMINAL = 0,
    kHAL_CAN_BIT_TIMING_DATA,
} eTypeHalCanBitTiming;

typedef struct
{
    IOMUX_PINCM eIomuxPincm;
    uint32_t u32IomuxPf;
} tHalCanPinIomuxValue;

typedef struct tHalCanEventHandler
{
    void *pContext;
    tfpHalCanEventCallback fpCallback;
    struct tHalCanEventHandler *pmNextHandler;
} tHalCanEventHandler;

typedef struct
{
    tHalCanEventHandler *pHandlerListHead;
    uint16_t u16HandlerCount;
} tHalCanEventDispatcher;

typedef struct
{
    uint8_t u8Dlc;
    uint8_t u8RamWords;
    uint8_t u8MaxElements;
} tHalCanRamMap;

typedef struct
{
    MCAN_Regs *pmCan;
    tHalCanPinIomuxValue mPinIomuxValueMappings[kHAL_CAN_FUNCTION_PIN_COUNT];
    DL_MCAN_ProtocolStatus mProtocolStatus;
    DL_MCAN_ErrCntStatus mErrorCountStatus;
    int32_t i32IntIrqN;
    uint32_t u32InterruptLine1Status;

    tHalCanFrame canframeTx[HAL_CAN_FW_TX_FIFO_SIZE], canframeRx[HAL_CAN_FW_RX_FIFO_SIZE], canframeBuf;
    tLibFifoBase fifoTx, fifoRx;

    tHalCanEventDispatcher mDispatcher;
    tHalCanEventHandler mHandlers[HAL_CAN_CALLBACK_HANDLER_SIZE];
    DL_MCAN_RxFIFOStatus RxFifoStatus;
    DL_MCAN_TxFIFOStatus TxFifoStatus;
} tHalCanPrivate;
/* Local define -------------------------------------------------------------*/
#define HAL_CAN_SOURCE_CLK_SYSPLLCLK1                       (80000000)
#define HAL_CAN_SOURCE_CLK_HFCLK                            (40000000)
#define HAL_CAN_PERCENT_BASE                                (1000)
#define HAL_CAN_MAX_NBRP                                    (511)
#define HAL_CAN_MAX_NTTQ                                    (385)
#define HAL_CAN_MAX_NTSEG1                                  (255)
#define HAL_CAN_MAX_NTSEG2                                  (127)
#define HAL_CAN_MAX_NSJW                                    (3) //(127)
#define HAL_CAN_MAX_DBRP                                    (31)
#define HAL_CAN_MAX_DTTQ                                    (49)
#define HAL_CAN_MAX_DTSEG1                                  (31)
#define HAL_CAN_MAX_DTSEG2                                  (15)
#define HAL_CAN_MAX_DSJW                                    (3) //(15)
#define HAL_CAN_SAMPLE_POINT_PERCENT                        (875)
#define HAL_CAN_SYNCH_JUMP_WIDTH_DIV                        (2)
#define HAL_CAN_MCAN0_IS_READY_FLAG                         SYSCTL_SYSSTATUS_MCAN0READY_MASK
#define HAL_CAN_MCAN1_IS_READY_FLAG                         SYSCTL_SYSSTATUS_MCAN1READY_MASK

#define HAL_CAN_ID_MASK_STD                                 (0x000007FFU)     // 11-bit ID mask
#define HAL_CAM_ID_MASK_STD_32BITS                          (0x1FFC0000U)
#define HAL_CAM_ID_OFFSET_STD_32BITS                        (18)
#define HAL_CAN_ID_MASK_EXT                                 (0x1FFFFFFFU)     // 29-bit ID mask
#define HAL_CAN_ID_TYPE_STD                                 (0x00000000U)
#define HAL_CAN_ID_TYPE_RTR                                 (0x40000000U)
#define HAL_CAN_ID_TYPE_EXT                                 (0x80000000U)

#define HAL_CAN_MSG_RAM_SIZE                                (1024U)
#define HAL_CAN_MSG_RAM_RX_FIFO0_ELEM_SIZE                  (DL_MCAN_ELEM_SIZE_8BYTES)  // HAL_CAN_MSG_RAM_RX_FIFO0_ELEMENT_SIZE
#define HAL_CAN_MSG_RAM_RX_FIFO1_ELEM_SIZE                  (DL_MCAN_ELEM_SIZE_8BYTES)  // HAL_CAN_MSG_RAM_RX_FIFO1_ELEMENT_SIZE
#define HAL_CAN_MSG_RAM_RX_BUFFER_ELEM_SIZE                 (DL_MCAN_ELEM_SIZE_8BYTES)  // HAL_CAN_MSG_RAM_RX_BUFFER_ELEMENT_SIZE
#define HAL_CAN_MSG_RAM_TX_BUFFER_ELEM_SIZE                 (DL_MCAN_ELEM_SIZE_8BYTES)  // HAL_CAN_MSG_RAM_TX_ELEMENT_SIZE
#define HAL_CAN_MSG_RAM_RX_FIFO0_BLOCKING0_OR_OVERWEIRE1    (0)
#define HAL_CAN_MSG_RAM_RX_FIFO1_BLOCKING0_OR_OVERWEIRE1    (0)
#define HAL_CAN_MSG_RAM_TX_BUF_MODE_FIFO0_OR_QUEUE1         (0)
// Calc Ram map address.
// Element Size
#define HAL_CAN_MSG_RAM_LSS_ELEMENT_SIZE                    (4)
#define HAL_CAN_MSG_RAM_LSE_ELEMENT_SIZE                    (8)
#define HAL_CAN_MSG_RAM_RX_FIFO0_ELEMENT_SIZE               (16)
#define HAL_CAN_MSG_RAM_RX_FIFO1_ELEMENT_SIZE               (16)
#define HAL_CAN_MSG_RAM_RX_BUFFER_ELEMENT_SIZE              (16)
#define HAL_CAN_MSG_RAM_TX_EVENT_ELEMENT_SIZE               (16)
#define HAL_CAN_MSG_RAM_TX_ELEMENT_SIZE                     (16)
// Qty
#define HAL_CAN_MSG_RAM_LSS_SIZE                            (1)
#define HAL_CAN_MSG_RAM_LSE_SIZE                            (1)
#define HAL_CAN_MSG_RAM_RX_FIFO0_SIZE                       (30)
#define HAL_CAN_MSG_RAM_RX_FIFO0_WATER_MARK_SIZE            (HAL_CAN_MSG_RAM_RX_FIFO0_SIZE / 2)
#define HAL_CAN_MSG_RAM_RX_FIFO1_SIZE                       (0)
#define HAL_CAN_MSG_RAM_RX_FIFO1_WATER_MARK_SIZE            (HAL_CAN_MSG_RAM_RX_FIFO1_SIZE / 2)
#define HAL_CAN_MSG_RAM_TX_EVENT_FIFO_SIZE                  (0)
#define HAL_CAN_MSG_RAM_TX_EVENT_FIFO_WATER_MARK_SIZE       (HAL_CAN_MSG_RAM_TX_EVENT_FIFO_SIZE / 2)
#define HAL_CAN_MSG_RAM_TX_BUF_NUM                          (0)
#define HAL_CAN_MSG_RAM_TX_FIFO_SIZE                        (30)
// Address
#define HAL_CAN_MSG_RAM_FLSSA_START_ADDRESS                 (0)
#define HAL_CAN_MSG_RAM_FLESA_START_ADDRESS                 (HAL_CAN_MSG_RAM_FLSSA_START_ADDRESS + (HAL_CAN_MSG_RAM_LSS_ELEMENT_SIZE * HAL_CAN_MSG_RAM_LSS_SIZE))
#define HAL_CAN_MSG_RAM_RX_FIFO0_START_ADDRESS              (HAL_CAN_MSG_RAM_FLESA_START_ADDRESS + (HAL_CAN_MSG_RAM_LSE_ELEMENT_SIZE * HAL_CAN_MSG_RAM_LSE_SIZE))
#define HAL_CAN_MSG_RAM_RX_FIFO1_START_ADDRESS              (HAL_CAN_MSG_RAM_RX_FIFO0_START_ADDRESS + (HAL_CAN_MSG_RAM_RX_FIFO0_ELEMENT_SIZE * HAL_CAN_MSG_RAM_RX_FIFO0_SIZE))
#define HAL_CAN_MSG_RAM_RX_BUFFER_START_ADDRESS             (HAL_CAN_MSG_RAM_RX_FIFO1_START_ADDRESS + (HAL_CAN_MSG_RAM_RX_FIFO1_ELEMENT_SIZE * HAL_CAN_MSG_RAM_RX_FIFO1_SIZE))
#define HAL_CAN_MSG_RAM_TX_EVENT_FIFO_START_ADDRESS         (HAL_CAN_MSG_RAM_RX_BUFFER_START_ADDRESS + HAL_CAN_MSG_RAM_RX_BUFFER_ELEMENT_SIZE)
#define HAL_CAN_MSG_RAM_TX_START_ADDRESS                    (HAL_CAN_MSG_RAM_TX_EVENT_FIFO_START_ADDRESS + (HAL_CAN_MSG_RAM_TX_EVENT_ELEMENT_SIZE * HAL_CAN_MSG_RAM_TX_EVENT_FIFO_SIZE))
#define HAL_CAN_MSG_RAM_END_ADDRESS                         (HAL_CAN_MSG_RAM_TX_START_ADDRESS + ((HAL_CAN_MSG_RAM_TX_BUF_NUM + HAL_CAN_MSG_RAM_TX_FIFO_SIZE) * HAL_CAN_MSG_RAM_TX_ELEMENT_SIZE))

#if HAL_CAN_MSG_RAM_END_ADDRESS > HAL_CAN_MSG_RAM_SIZE
#error "Msg Ram > 1kBytes."
#endif

#define HAL_CAN_TRIGGER_INTERRUPT                           (DL_MCAN_INTERRUPT_RF0N | MCAN_IR_RF0F_MASK | DL_MCAN_INTERRUPT_TC | DL_MCAN_INTERRUPT_TEFL | DL_MCAN_INTERRUPT_BO | DL_MCAN_INTERRUPT_PEA)    //(DL_MCAN_INTR_MASK_ALL)
#define HAL_CAN_TX_INTERRUPT                                (DL_MCAN_INTERRUPT_TC | DL_MCAN_INTERRUPT_TEFL | DL_MCAN_INTERRUPT_PEA)

/* Local macro --------------------------------------------------------------*/
/* Local variables ----------------------------------------------------------*/
static bool bIsInitHalCanResource = false;
static tHalCan *pmHalCanContext[kHAL_CAN_CHANNEL_COUNT];
static tHalCanPrivate mHalCanPrivateContext[kHAL_CAN_CHANNEL_COUNT] =
{
    [kHAL_CAN_CHANNEL_0] =
    {
        .pmCan = HAL_CAN_0,
        .i32IntIrqN = kHAL_CAN_CHANNEL_0_IRQ,
        .u32InterruptLine1Status = 0,
        #if defined(BSP_CAN_0_TX_IOMUX) && defined(BSP_CAN_0_TX_IOMUX_PF) && defined(BSP_CAN_0_RX_IOMUX) && defined(BSP_CAN_0_RX_IOMUX_PF)
        .mPinIomuxValueMappings =
        {
            [kHAL_CAN_FUNCTION_PIN_TX] =
            {
                .eIomuxPincm = BSP_CAN_0_TX_IOMUX,
                .u32IomuxPf = BSP_CAN_0_TX_IOMUX_PF,
            },
            [kHAL_CAN_FUNCTION_PIN_RX] =
            {
                .eIomuxPincm = BSP_CAN_0_RX_IOMUX,
                .u32IomuxPf = BSP_CAN_0_RX_IOMUX_PF,
            },
        },
        #endif
    },
    [kHAL_CAN_CHANNEL_1] =
    {
        .pmCan = HAL_CAN_1,
        .i32IntIrqN = kHAL_CAN_CHANNEL_1_IRQ,
        .u32InterruptLine1Status = 0,
        #if defined(BSP_CAN_1_TX_IOMUX) && defined(BSP_CAN_1_TX_IOMUX_PF) && defined(BSP_CAN_1_RX_IOMUX) && defined(BSP_CAN_1_RX_IOMUX_PF)
        .mPinIomuxValueMappings =
        {
            [kHAL_CAN_FUNCTION_PIN_TX] =
            {
                .eIomuxPincm = BSP_CAN_1_TX_IOMUX,
                .u32IomuxPf = BSP_CAN_1_TX_IOMUX_PF,
            },
            [kHAL_CAN_FUNCTION_PIN_RX] =
            {
                .eIomuxPincm = BSP_CAN_1_RX_IOMUX,
                .u32IomuxPf = BSP_CAN_1_RX_IOMUX_PF,
            },
        },
        #endif
    }
};
static const DL_MCAN_ClockConfig gmCanClockConfig = 
{
    .clockSel = DL_MCAN_FCLK_HFCLK,
    .divider  = DL_MCAN_FCLK_DIV_1,
};
static const DL_MCAN_InitParams gmCanInitParams = 
{
    .fdMode            = false,
    .brsEnable         = false,
    .txpEnable         = false,
    .efbi              = false,
    .pxhddisable       = false,
    .darEnable         = true,
    .wkupReqEnable     = true,
    .autoWkupEnable    = true,
    .emulationEnable   = true,
    .tdcEnable         = true,
    .wdcPreload        = 255,
/* Transmitter Delay Compensation parameters. */
    .tdcConfig.tdcf    = 10,
    .tdcConfig.tdco    = 6,
};
static const DL_MCAN_ConfigParams gmCanConfigParams =
{
    .monEnable         = false,
    .asmEnable         = false,
    .tsPrescalar       = 15,
    .tsSelect          = 0,
    .timeoutSelect     = DL_MCAN_TIMEOUT_SELECT_CONT,
    .timeoutPreload    = 65535,
    .timeoutCntEnable  = false,
    .filterConfig.rrfs = true,
    .filterConfig.rrfe = true,
    .filterConfig.anfe = 1,
    .filterConfig.anfs = 1,
};
static const DL_MCAN_MsgRAMConfigParams gmCanMsgRamConfigParams =
{
    .flssa                  = HAL_CAN_MSG_RAM_FLSSA_START_ADDRESS,
    .lss                    = HAL_CAN_MSG_RAM_LSS_SIZE,
    .flesa                  = HAL_CAN_MSG_RAM_FLESA_START_ADDRESS,
    .lse                    = HAL_CAN_MSG_RAM_LSE_SIZE,

    .rxFIFO0startAddr       = HAL_CAN_MSG_RAM_RX_FIFO0_START_ADDRESS,
    .rxFIFO0ElemSize        = HAL_CAN_MSG_RAM_RX_FIFO0_ELEM_SIZE,
    .rxFIFO0size            = HAL_CAN_MSG_RAM_RX_FIFO0_SIZE,
    .rxFIFO0waterMark       = HAL_CAN_MSG_RAM_RX_FIFO0_WATER_MARK_SIZE,
    .rxFIFO0OpMode          = HAL_CAN_MSG_RAM_RX_FIFO0_BLOCKING0_OR_OVERWEIRE1,

    .rxFIFO1startAddr       = HAL_CAN_MSG_RAM_RX_FIFO1_START_ADDRESS,
    .rxFIFO1ElemSize        = HAL_CAN_MSG_RAM_RX_FIFO1_ELEM_SIZE,
    .rxFIFO1size            = HAL_CAN_MSG_RAM_RX_FIFO1_SIZE,
    .rxFIFO1waterMark       = HAL_CAN_MSG_RAM_RX_FIFO1_WATER_MARK_SIZE,
    .rxFIFO1OpMode          = HAL_CAN_MSG_RAM_RX_FIFO1_BLOCKING0_OR_OVERWEIRE1,

    .rxBufStartAddr         = HAL_CAN_MSG_RAM_RX_BUFFER_START_ADDRESS,
    .rxBufElemSize          = HAL_CAN_MSG_RAM_RX_BUFFER_ELEM_SIZE,

    .txEventFIFOStartAddr   = HAL_CAN_MSG_RAM_TX_EVENT_FIFO_START_ADDRESS,
    .txEventFIFOSize        = HAL_CAN_MSG_RAM_TX_EVENT_FIFO_SIZE,
    .txEventFIFOWaterMark   = HAL_CAN_MSG_RAM_TX_EVENT_FIFO_WATER_MARK_SIZE,

    .txStartAddr            = HAL_CAN_MSG_RAM_TX_START_ADDRESS,
    .txBufElemSize          = HAL_CAN_MSG_RAM_TX_BUFFER_ELEM_SIZE,
    .txBufMode              = HAL_CAN_MSG_RAM_TX_BUF_MODE_FIFO0_OR_QUEUE1,
    .txBufNum               = HAL_CAN_MSG_RAM_TX_BUF_NUM,
    .txFIFOSize             = HAL_CAN_MSG_RAM_TX_FIFO_SIZE,
};
static const DL_MCAN_StdMsgIDFilterElement gmRxStandardFrameIdFilter =
{
    .sft  = 0x0,
    .sfec = 0x1,
    .sfid1 = 0x000,
    .sfid2 = HAL_CAN_ID_MASK_STD,
};
static const DL_MCAN_ExtMsgIDFilterElement gmRxExtendedFrameIdFilter = 
{
    .eft = 0x0,
    .efec = 0x1,
    .efid1 = 0x00000000,
    .efid2 = HAL_CAN_ID_MASK_EXT,
};
/* Local function declare ---------------------------------------------------*/
static void HalCanInit(void);
static bool HalCanCheckResoure(tHalCan *pmHalCan);
static bool HalCanHardwareIsReady(tHalCan *pmHalCan);
static void HalCanInitHardware(tHalCan *pmHalCan);
static void HalCanInitParams(tHalCan *pmHalCan);
static void HalCanConfigParams(tHalCan *pmHalCan);
static void HalCanCalcBitTiming(uint32_t u32CanClockHz, uint32_t u32BitRate, eTypeHalCanBitTiming eBitTimingType, uint32_t *pu32Brp, uint32_t *pu32TSeg1, uint32_t *pu32TSeg2, uint32_t *pu32Sjw);
static void HalCanSetBitTiming(tHalCan *pmHalCan);
static void HalCanMsgRamConfigParams(tHalCan *pmHalCan);
static void HalCanAddFilterAndMask(tHalCan *pmHalCan);
static void HalCanSetInterrupt(tHalCan *pmHalCan);
static void HalCanInitFifo(tHalCan *pmHalCan);
static void HalCanGetErrorCount(tHalCan *pmHalCan);
static void HalCanBusOffRecovery(tHalCan *pmHalCan);
static void HalCanSetTxInterrupt(tHalCan *pmHalCan, bool bEnable);
static inline int8_t HalCanSend(tHalCan *pmHalCan, tHalCanFrame *pmCanFrame);

static void HalCanIrqHandler(tHalCan *pmHalCan);
static void HalCanTriggerEvent(tHalCan *pmHalCan, eTypeHalCanEvent eEvent, void *pData);
static tHalCanEventHandler *HalCanGetEventHandlerPointer(tHalCan *pmHalCan);
static void HalCanReleaseEventHandlerPointer(tHalCan *pmHalCan, tHalCanEventHandler *pTarget);
/* Local function prototypes ------------------------------------------------*/
static void HalCanInit(void)
{
    if (bIsInitHalCanResource == true)
    {
        return;
    }
    for (uint8_t u8Index = 0; u8Index < kHAL_CAN_CHANNEL_COUNT; u8Index++)
    {
        pmHalCanContext[u8Index] = NULL;
    }
    DL_MCAN_reset(HAL_CAN_0);
    DL_MCAN_reset(HAL_CAN_1);
    DL_MCAN_disablePower(HAL_CAN_0);
    DL_MCAN_disablePower(HAL_CAN_1);
    delay_cycles(HAL_MCU_POWER_STARTUP_DELAY_CYCLE_CNT);
    bIsInitHalCanResource = true;
    return;
}
static bool HalCanCheckResoure(tHalCan *pmHalCan)
{
    if (pmHalCan == NULL ||
        pmHalCan -> eChannel == kHAL_CAN_CHANNEL_COUNT ||
        pmHalCanContext[pmHalCan -> eChannel] != NULL ||
        pmHalCan -> u32NominalBitRate == 0)
    {
        return false;
    }
    return true;
}

static bool HalCanHardwareIsReady(tHalCan *pmHalCan)
{
    uint32_t u32IsReadyFlag;
    switch (pmHalCan -> eChannel)
    {
    case kHAL_CAN_CHANNEL_0:
        u32IsReadyFlag = HAL_CAN_MCAN0_IS_READY_FLAG;
        break;
    case kHAL_CAN_CHANNEL_1:
        u32IsReadyFlag = HAL_CAN_MCAN1_IS_READY_FLAG;
        break;
    default:
        u32IsReadyFlag = 0;
        break;
    }
    if ((DL_SYSCTL_getStatus() & u32IsReadyFlag) == u32IsReadyFlag)
    {
        return true;
    }
    return false;
}

static void HalCanInitHardware(tHalCan *pmHalCan)
{
    tHalCanPinIomuxValue *pPinIomuxValue;
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    pPinIomuxValue = &(pmHalCanPrivateContext -> mPinIomuxValueMappings[kHAL_CAN_FUNCTION_PIN_TX]);
    DL_GPIO_initPeripheralOutputFunction(pPinIomuxValue -> eIomuxPincm, pPinIomuxValue -> u32IomuxPf);
    pPinIomuxValue = &(pmHalCanPrivateContext -> mPinIomuxValueMappings[kHAL_CAN_FUNCTION_PIN_RX]);
    DL_GPIO_initPeripheralInputFunction(pPinIomuxValue -> eIomuxPincm, pPinIomuxValue -> u32IomuxPf);
    DL_MCAN_enablePower(pmHalCanPrivateContext -> pmCan);
    while (HalCanHardwareIsReady(pmHalCan) == false)
    {
        delay_cycles(HAL_MCU_MAIN_CLOCK_FREQUENCY);
    }
    DL_MCAN_disableModuleClock(pmHalCanPrivateContext -> pmCan);
    DL_MCAN_enableModuleClock(pmHalCanPrivateContext -> pmCan);
    DL_MCAN_setClockConfig(pmHalCanPrivateContext -> pmCan, (DL_MCAN_ClockConfig *) &gmCanClockConfig);
    while (DL_MCAN_isMemInitDone(pmHalCanPrivateContext -> pmCan) == false)
    {
        delay_cycles(HAL_MCU_MAIN_CLOCK_FREQUENCY);
    }
    return;
}

static void HalCanInitParams(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_InitParams mCanInitParams = gmCanInitParams;
    mCanInitParams.fdMode = pmHalCan -> bFdMode;
    if (pmHalCan -> bFdMode == false || pmHalCan -> bBitRateSwitch == false)
    {
        mCanInitParams.brsEnable = false;
    }
    else
    {
        mCanInitParams.brsEnable = true;
    }
    mCanInitParams.darEnable = pmHalCan -> bDisabledAutomaticRetransmission;
    DL_MCAN_init(pmHalCanPrivateContext -> pmCan, (DL_MCAN_InitParams *) &mCanInitParams);
    return;
}

static void HalCanConfigParams(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_ConfigParams mCanConfigParams = gmCanConfigParams;
    mCanConfigParams.monEnable = pmHalCan -> bListenOnly;
    DL_MCAN_config(pmHalCanPrivateContext -> pmCan, (DL_MCAN_ConfigParams*) &mCanConfigParams);
    return;
}

static void HalCanCalcBitTiming(uint32_t u32CanClockHz, uint32_t u32BitRate, eTypeHalCanBitTiming eBitTimingType, uint32_t *pu32Brp, uint32_t *pu32TSeg1, uint32_t *pu32TSeg2, uint32_t *pu32Sjw)
{
    if (u32BitRate == 0)
    {
        (*pu32Brp) = (*pu32TSeg1) = (*pu32TSeg2) = (*pu32Sjw) = 0;
        return;
    }
    uint32_t u32MaxBrp, u32MaxTotalTq, u32MaxTSeg1, u32MaxTSeg2, u32MaxSjw;
    uint32_t u32BitRateTolerance, u32BestBitRateError, u32BitRateError, u32BestClockHzError, u32ClockHzError;
    uint32_t u32CalcBrp, u32CalcTotalTq, u32CalcTSeg1, u32CalcTSeg2, u32CalcSjw;
    switch (eBitTimingType)
    {
    case kHAL_CAN_BIT_TIMING_NOMINAL:
        u32MaxBrp = HAL_CAN_MAX_NBRP;
        u32MaxTotalTq = HAL_CAN_MAX_NTTQ;
        u32MaxTSeg1 = HAL_CAN_MAX_NTSEG1;
        u32MaxTSeg2 = HAL_CAN_MAX_NTSEG2;
        u32MaxSjw = HAL_CAN_MAX_NSJW;
        break;
    case kHAL_CAN_BIT_TIMING_DATA:
        u32MaxBrp = HAL_CAN_MAX_DBRP;
        u32MaxTotalTq = HAL_CAN_MAX_DTTQ;
        u32MaxTSeg1 = HAL_CAN_MAX_DTSEG1;
        u32MaxTSeg2 = HAL_CAN_MAX_DTSEG2;
        u32MaxSjw = HAL_CAN_MAX_DSJW;
        break;
    }
    u32BitRateTolerance = u32BitRate / HAL_CAN_PERCENT_BASE / HAL_CAN_SYNCH_JUMP_WIDTH_DIV;
    u32CalcBrp = 0;
    u32BestBitRateError = u32BestClockHzError = 0xFFFFFFFF;
    while (u32CalcBrp <= u32MaxBrp)
    {
        u32CalcTotalTq = (u32CanClockHz / (u32CalcBrp + 1)) / u32BitRate;
        u32BitRateError = u32CanClockHz / ((u32CalcBrp + 1) * u32CalcTotalTq);
        u32ClockHzError = u32CalcTotalTq * (u32CalcBrp + 1) * u32BitRate;
        if (u32BitRate >= u32BitRateError)
        {
            u32BitRateError = u32BitRate - u32BitRateError;
        }
        else
        {
            u32BitRateError -= u32BitRate;
        }
        if (u32CanClockHz >= u32ClockHzError)
        {
            u32ClockHzError = u32CanClockHz - u32ClockHzError;
        }
        else
        {
            u32ClockHzError -= u32CanClockHz;
        }
        if (u32CalcTotalTq <= u32MaxTotalTq &&
            u32BitRateError < u32BestBitRateError &&
            u32ClockHzError < u32BestClockHzError)
        {
            u32BestBitRateError = u32BitRateError;
            u32BestClockHzError = u32ClockHzError;
            u32CalcTotalTq--;
            u32CalcTSeg1 = u32CalcTotalTq * HAL_CAN_SAMPLE_POINT_PERCENT / HAL_CAN_PERCENT_BASE;
            u32CalcTSeg2 = u32CalcTotalTq - u32CalcTSeg1;
            u32CalcTSeg1--;
            u32CalcTSeg2--;
            if (u32BestBitRateError <= u32BitRateTolerance)
            {
                break;
            }
        }
        u32CalcBrp++;
    }
    u32CalcSjw = u32CalcTSeg2 / HAL_CAN_SYNCH_JUMP_WIDTH_DIV;
    if (u32CalcSjw > u32MaxSjw)
    {
        u32CalcSjw = u32MaxSjw;
    }
    *pu32Brp = u32CalcBrp;
    *pu32TSeg1 = u32CalcTSeg1;
    *pu32TSeg2 = u32CalcTSeg2;
    *pu32Sjw = u32CalcSjw;
    return;
}

static void HalCanSetBitTiming(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_ClockConfig mCanClockConfig;
    DL_MCAN_BitTimingParams mCanBitTiming;
    uint32_t u32SourceClockHz, u32ClockDiv, u32CanClockHz;
    DL_MCAN_getClockConfig(pmHalCanPrivateContext -> pmCan, &mCanClockConfig);
    switch (mCanClockConfig.clockSel)
    {
    case DL_MCAN_FCLK_SYSPLLCLK1:
        u32SourceClockHz = HAL_CAN_SOURCE_CLK_SYSPLLCLK1;
        break;
    case DL_MCAN_FCLK_HFCLK:
        u32SourceClockHz = HAL_CAN_SOURCE_CLK_HFCLK;
        break;
    }
    switch (mCanClockConfig.divider)
    {
    case DL_MCAN_FCLK_DIV_1:
        u32ClockDiv = 1;
        break;
    case DL_MCAN_FCLK_DIV_2:
        u32ClockDiv = 2;
        break;
    case DL_MCAN_FCLK_DIV_4:
        u32ClockDiv = 4;
        break;
    }
    u32CanClockHz = u32SourceClockHz / u32ClockDiv;
    HalCanCalcBitTiming(u32CanClockHz, pmHalCan -> u32NominalBitRate, kHAL_CAN_BIT_TIMING_NOMINAL, &mCanBitTiming.nomRatePrescalar, &mCanBitTiming.nomTimeSeg1, &mCanBitTiming.nomTimeSeg2, &mCanBitTiming.nomSynchJumpWidth);
    if (pmHalCan -> bFdMode == false || pmHalCan -> bBitRateSwitch == false)
    {
        pmHalCan -> u32DataBitRate = 0;
    }
    HalCanCalcBitTiming(u32CanClockHz, pmHalCan -> u32DataBitRate, kHAL_CAN_BIT_TIMING_DATA, &mCanBitTiming.dataRatePrescalar, &mCanBitTiming.dataTimeSeg1, &mCanBitTiming.dataTimeSeg2, &mCanBitTiming.dataSynchJumpWidth);
    DL_MCAN_setBitTime(pmHalCanPrivateContext -> pmCan, (DL_MCAN_BitTimingParams*) &mCanBitTiming);
    return;
}

static void HalCanMsgRamConfigParams(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_msgRAMConfig(pmHalCanPrivateContext -> pmCan, (DL_MCAN_MsgRAMConfigParams*) &gmCanMsgRamConfigParams);
    return;
}

static void HalCanAddFilterAndMask(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_addStdMsgIDFilter(pmHalCanPrivateContext -> pmCan, 0, &gmRxStandardFrameIdFilter);
    DL_MCAN_addExtMsgIDFilter(pmHalCanPrivateContext -> pmCan, 0, &gmRxExtendedFrameIdFilter);
    DL_MCAN_setExtIDAndMask(pmHalCanPrivateContext -> pmCan, HAL_CAN_ID_MASK_EXT);
    DL_MCAN_resetTSCounter(pmHalCanPrivateContext -> pmCan);
    return;
}

static void HalCanSetInterrupt(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_enableIntr(pmHalCanPrivateContext -> pmCan, HAL_CAN_TRIGGER_INTERRUPT, true);
    DL_MCAN_selectIntrLine(pmHalCanPrivateContext -> pmCan, HAL_CAN_TRIGGER_INTERRUPT, DL_MCAN_INTR_LINE_NUM_1);
    DL_MCAN_enableIntrLine(pmHalCanPrivateContext -> pmCan, DL_MCAN_INTR_LINE_NUM_1, true);
    DL_MCAN_clearInterruptStatus(pmHalCanPrivateContext -> pmCan, DL_MCAN_MSP_INTERRUPT_LINE1);  
    DL_MCAN_enableInterrupt(pmHalCanPrivateContext -> pmCan, DL_MCAN_MSP_INTERRUPT_LINE1);
    NVIC_EnableIRQ(pmHalCanPrivateContext -> i32IntIrqN);
    return;
}

static void HalCanInitFifo(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    pmHalCanPrivateContext -> fifoTx = (tLibFifoBase)
    {
        .pmFifoStartAddr = pmHalCanPrivateContext -> canframeTx,
        .u16ElementSize = sizeof(pmHalCanPrivateContext -> canframeTx[0]),
        .u16FifoSize = LIB_FIFO_CALC_ARRAY_SIZE(pmHalCanPrivateContext -> canframeTx),
        .u16FifoPushInPosi = 0,
        .u16FifoPopOutPosi = 0,
    };
    pmHalCanPrivateContext -> fifoRx = (tLibFifoBase)
    {
        .pmFifoStartAddr = pmHalCanPrivateContext -> canframeRx,
        .u16ElementSize = sizeof(pmHalCanPrivateContext -> canframeRx[0]),
        .u16FifoSize = LIB_FIFO_CALC_ARRAY_SIZE(pmHalCanPrivateContext -> canframeRx),
        .u16FifoPushInPosi = 0,
        .u16FifoPopOutPosi = 0,
    };

    pmHalCanPrivateContext -> mDispatcher = (tHalCanEventDispatcher)
    {
        .pHandlerListHead = NULL,
        .u16HandlerCount = 0
    };
    for (uint8_t u8Index = 0; u8Index < HAL_CAN_CALLBACK_HANDLER_SIZE; u8Index++)
    {
        pmHalCanPrivateContext -> mHandlers[u8Index] = (tHalCanEventHandler)
        {
            .pContext = NULL,
            .fpCallback = NULL,
            .pmNextHandler = NULL
        };
    }
    return;
}

static void HalCanGetErrorCount(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_getErrCounters(pmHalCanPrivateContext -> pmCan, &(pmHalCanPrivateContext -> mErrorCountStatus));
    return;
}

static void HalCanBusOffRecovery(tHalCan *pmHalCan)
{
    if (HalCanIsBusOff(pmHalCan) == false)
    {
        return;
    }
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_setOpMode(pmHalCanPrivateContext -> pmCan, DL_MCAN_OPERATION_MODE_NORMAL);
    while (DL_MCAN_getOpMode(pmHalCanPrivateContext -> pmCan) != DL_MCAN_OPERATION_MODE_NORMAL)
    {
        delay_cycles(HAL_MCU_MAIN_CLOCK_FREQUENCY);
    }
    return;
}

static void HalCanSetTxInterrupt(tHalCan *pmHalCan, bool bEnable)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_enableIntr(pmHalCanPrivateContext -> pmCan, HAL_CAN_TX_INTERRUPT, bEnable);
    return;
}

static inline int8_t HalCanSend(tHalCan *pmHalCan, tHalCanFrame *pmCanFrame)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_TxFIFOStatus *pTxFifoStatus = &(pmHalCanPrivateContext -> TxFifoStatus);
    DL_MCAN_TxBufElement txbufelement;
    uint8_t *pu8DataPtr = pmCanFrame -> tUnionData.u8Data;
    uint16_t *pu16DataPtr = txbufelement.data;
    if ((pmCanFrame -> u32Id & HAL_CAN_ID_TYPE_EXT) != 0)
    {
        txbufelement.xtd = 1;
        txbufelement.id = (pmCanFrame -> u32Id & HAL_CAN_ID_MASK_EXT);
    }
    else
    {
        txbufelement.xtd = 0;
        txbufelement.id = ((pmCanFrame -> u32Id & HAL_CAN_ID_MASK_STD) << HAL_CAM_ID_OFFSET_STD_32BITS);
    }
    txbufelement.rtr = !!(pmCanFrame -> u32Id & HAL_CAN_ID_TYPE_RTR);
    if (pmCanFrame -> u8Dlc > HAL_CAN_DATA_SIZE_TARGET)
    {
        pmCanFrame -> u8Dlc = HAL_CAN_DATA_SIZE_TARGET;
    }
    txbufelement.dlc = pmCanFrame -> u8Dlc;
    while (pmCanFrame -> u8Dlc--)
    {
        *pu16DataPtr++ = *pu8DataPtr++;
    }
    DL_MCAN_writeMsgRam(pmHalCanPrivateContext -> pmCan, DL_MCAN_MEM_TYPE_FIFO, pTxFifoStatus -> putIdx, &txbufelement);
    DL_MCAN_TXBufTransIntrEnable(pmHalCanPrivateContext -> pmCan, pTxFifoStatus -> putIdx, true);
    DL_MCAN_TXBufAddReq(pmHalCanPrivateContext -> pmCan, pTxFifoStatus -> putIdx);
    return RES_SUCCESS;
}

static void HalCanIrqHandler(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    switch (DL_MCAN_getPendingInterrupt(pmHalCanPrivateContext -> pmCan))
    {
    case DL_MCAN_IIDX_LINE1:
        pmHalCanPrivateContext -> u32InterruptLine1Status = DL_MCAN_getIntrStatus(pmHalCanPrivateContext -> pmCan);
        if ((pmHalCanPrivateContext -> u32InterruptLine1Status & DL_MCAN_INTERRUPT_RF0N) == DL_MCAN_INTERRUPT_RF0N)
        {
            tLibFifoBase *pRxFifo = &(pmHalCanPrivateContext -> fifoRx);
            DL_MCAN_RxFIFOStatus rxfifostatus;
            DL_MCAN_RxBufElement rxbufelement;
            tHalCanFrame *pmCanFrame = LibFifoGetPushPointer(&(pmHalCanPrivateContext -> fifoRx));
            if (pmCanFrame == NULL) {
                LibFifoPop(pRxFifo, NULL);
                pmCanFrame = LibFifoGetPushPointer(pRxFifo);
            }
            rxfifostatus.num = DL_MCAN_RX_FIFO_NUM_0;
            DL_MCAN_getRxFIFOStatus(pmHalCanPrivateContext -> pmCan, &rxfifostatus);
            DL_MCAN_readMsgRam(pmHalCanPrivateContext -> pmCan, DL_MCAN_MEM_TYPE_FIFO, 0, rxfifostatus.num, &rxbufelement);
            if (rxbufelement.xtd == 1)
            {
                pmCanFrame -> u32Id = (rxbufelement.id | HAL_CAN_ID_TYPE_EXT);
            }
            else
            {
                pmCanFrame -> u32Id = ((rxbufelement.id & (uint32_t) HAL_CAM_ID_MASK_STD_32BITS) >> (uint32_t) HAL_CAM_ID_OFFSET_STD_32BITS);
            }
            if (rxbufelement.rtr == 1)
            {
                pmCanFrame -> u32Id |= HAL_CAN_ID_TYPE_RTR;
            }
            pmCanFrame -> u8Dlc = rxbufelement.dlc;
            uint8_t *pu8DataPtr = pmCanFrame -> tUnionData.u8Data;
            uint16_t *pu16DataPtr = rxbufelement.data;
            while (rxbufelement.dlc--)
            {
                *pu8DataPtr++ = *pu16DataPtr++;
            }
            LibFifoPush(pRxFifo, NULL);
            DL_MCAN_writeRxFIFOAck(pmHalCanPrivateContext -> pmCan, rxfifostatus.num, rxfifostatus.getIdx);
        }
        if (((pmHalCanPrivateContext -> u32InterruptLine1Status & DL_MCAN_INTERRUPT_TC) == DL_MCAN_INTERRUPT_TC) ||
            (pmHalCan -> bDisabledAutomaticRetransmission == true && ((pmHalCanPrivateContext -> u32InterruptLine1Status & DL_MCAN_INTERRUPT_PEA) == DL_MCAN_INTERRUPT_PEA)))
        {
            if (LibFifoIsEmpty(&(pmHalCanPrivateContext -> fifoTx)) == false)
            {
                DL_MCAN_getTxFIFOQueStatus(pmHalCanPrivateContext -> pmCan, &(pmHalCanPrivateContext -> TxFifoStatus));
                HalCanSend(pmHalCan, LibFifoGetFrontPointer(&(pmHalCanPrivateContext -> fifoTx)));
                LibFifoPop(&(pmHalCanPrivateContext -> fifoTx), NULL);
            }
        }
        if ((pmHalCanPrivateContext -> u32InterruptLine1Status & DL_MCAN_INTERRUPT_BO) == DL_MCAN_INTERRUPT_BO)
        {
            if (HalCanIsBusOff(pmHalCan) == true)
            {
                HalCanTriggerEvent(pmHalCan, kHAL_CAN_EVENT_BUS_OFF, NULL);
                HalCanBusOffRecovery(pmHalCan);
            }
            else
            {
                HalCanTriggerEvent(pmHalCan, kHAL_CAN_EVENT_READY, NULL);
            }
        }
        DL_MCAN_clearIntrStatus(pmHalCanPrivateContext -> pmCan, pmHalCanPrivateContext -> u32InterruptLine1Status, DL_MCAN_INTR_SRC_MCAN_LINE_1);
        break;
    default:
        break;
    }
    return;
}

static void HalCanTriggerEvent(tHalCan *pmHalCan, eTypeHalCanEvent eEvent, void *pData)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    if (pmHalCan -> fpEvent != NULL) {
        pmHalCan -> fpEvent(eEvent, pData);
    }
    tHalCanEventHandler *pHandler = pmHalCanPrivateContext -> mDispatcher.pHandlerListHead;
    while (pHandler != NULL)
    {
        if (pHandler -> fpCallback != NULL)
        {
            pHandler -> fpCallback(pHandler -> pContext, eEvent, pData);
        }
        pHandler = pHandler -> pmNextHandler;
    }
    return;
}

static tHalCanEventHandler *HalCanGetEventHandlerPointer(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    for (uint8_t u8Index = 0; u8Index < HAL_CAN_CALLBACK_HANDLER_SIZE; u8Index++)
    {
        if (pmHalCanPrivateContext -> mHandlers[u8Index].pContext == NULL &&
            pmHalCanPrivateContext -> mHandlers[u8Index].fpCallback == NULL &&
            pmHalCanPrivateContext -> mHandlers[u8Index].pmNextHandler == NULL)
        {
            return &(pmHalCanPrivateContext -> mHandlers[u8Index]);
        }
    }
    return NULL;
}

static void HalCanReleaseEventHandlerPointer(tHalCan *pmHalCan, tHalCanEventHandler *pTarget)
{
    pTarget -> pContext = NULL;
    pTarget -> fpCallback = NULL;
    pTarget -> pmNextHandler = NULL;
    return;
}
/* Global function prototypes -----------------------------------------------*/
int8_t HalCanOpen(tHalCan *pmHalCan)
{
    HalCanInit();
    if (HalCanCheckResoure(pmHalCan) == false)
    {
        return RES_ERROR_INVALID_STATE;
    }
    pmHalCanContext[pmHalCan -> eChannel] = pmHalCan;
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    HalCanInitHardware(pmHalCan);
    DL_MCAN_setOpMode(pmHalCanPrivateContext -> pmCan, DL_MCAN_OPERATION_MODE_SW_INIT);
    while (DL_MCAN_getOpMode(pmHalCanPrivateContext -> pmCan) != DL_MCAN_OPERATION_MODE_SW_INIT)
    {
        delay_cycles(HAL_MCU_MAIN_CLOCK_FREQUENCY);
    }
    HalCanInitParams(pmHalCan);
    HalCanConfigParams(pmHalCan);
    HalCanSetBitTiming(pmHalCan);
    HalCanMsgRamConfigParams(pmHalCan);
    HalCanAddFilterAndMask(pmHalCan);
    DL_MCAN_setOpMode(pmHalCanPrivateContext -> pmCan, DL_MCAN_OPERATION_MODE_NORMAL);
    while (DL_MCAN_getOpMode(pmHalCanPrivateContext -> pmCan) != DL_MCAN_OPERATION_MODE_NORMAL)
    {
        delay_cycles(HAL_MCU_MAIN_CLOCK_FREQUENCY);
    }
    HalCanSetInterrupt(pmHalCan);
    HalCanInitFifo(pmHalCan);
    HalCanTriggerEvent(pmHalCan, kHAL_CAN_EVENT_READY, NULL);
    return RES_SUCCESS;
}

bool HalCanIsExtendedFrame(uint32_t u32Id)
{
    if ((u32Id & HAL_CAN_ID_TYPE_EXT) == 0)
    {
        return false;
    }
    return true;
}

bool HalCanIsRemoteFrame(uint32_t u32Id)
{
    if ((u32Id & HAL_CAN_ID_TYPE_RTR) == 0)
    {
        return false;
    }
    return true;
}

uint32_t HalCanGetRawId(uint32_t u32Id)
{
    u32Id &= HAL_CAN_ID_MASK_EXT;
    return u32Id;
}

uint32_t HalCanBuildId(uint32_t u32Id, bool bIsExtendedFrame, bool bIsRemoteFrame)
{
    if (bIsExtendedFrame == true)
    {
        u32Id &= HAL_CAN_ID_MASK_EXT;
        u32Id |= HAL_CAN_ID_TYPE_EXT;
    }
    else
    {
        u32Id &= HAL_CAN_ID_MASK_STD;
    }
    if (bIsRemoteFrame == true)
    {
        u32Id |= HAL_CAN_ID_TYPE_RTR;
    }
    return u32Id;
}

bool HalCanIsBusOff(tHalCan *pmHalCan)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    DL_MCAN_ProtocolStatus *pProtocolStatus = &(pmHalCanPrivateContext -> mProtocolStatus);
    DL_MCAN_getProtocolStatus(pmHalCanPrivateContext -> pmCan, pProtocolStatus);
    return (pProtocolStatus -> busOffStatus == 1);
}

uint16_t HalCanGetTxFifoFreeCount(tHalCan *pmHalCan)
{
    return LibFifoGetFreeCount(&(mHalCanPrivateContext[pmHalCan -> eChannel].fifoTx));
}

int8_t HalCanPut(tHalCan *pmHalCan, tHalCanFrame *pmCanFrame)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    tLibFifoBase *pTxFifo = &(pmHalCanPrivateContext -> fifoTx);
    if (LibFifoIsFull(pTxFifo) == true)
    {
        LibFifoPop(pTxFifo, NULL);
    }
    DL_MCAN_TxFIFOStatus *pTxFifoStatus = &(pmHalCanPrivateContext -> TxFifoStatus);
    HalCanSetTxInterrupt(pmHalCan, false);
    DL_MCAN_getTxFIFOQueStatus(pmHalCanPrivateContext -> pmCan, pTxFifoStatus);
    if (LibFifoIsEmpty(pTxFifo) == false ||
        pTxFifoStatus -> freeLvl == 0)
    {
        LibFifoPush(pTxFifo, pmCanFrame);
    }
    else if (LibFifoIsEmpty(pTxFifo) == true &&
        pTxFifoStatus -> freeLvl != 0)
    {
        HalCanSend(pmHalCan, pmCanFrame);
    }
    HalCanSetTxInterrupt(pmHalCan, true);
    return RES_SUCCESS;
}

uint16_t HalCanGetRxFifoCount(tHalCan *pmHalCan)
{
    return LibFifoGetCount(&(mHalCanPrivateContext[pmHalCan -> eChannel].fifoRx));
}

int8_t HalCanGet(tHalCan *pmHalCan, tHalCanFrame *pmCanFrame)
{
    tLibFifoBase *pRxFifo = &(mHalCanPrivateContext[pmHalCan -> eChannel].fifoRx);
    if (LibFifoIsEmpty(pRxFifo) == true)
    {
        return RES_ERROR_EMPTY;
    }
    LibFifoPop(pRxFifo, pmCanFrame);
    return RES_SUCCESS;
}

int8_t HalCanRegisterEventHandler(tHalCan *pmHalCan, void *pContext, tfpHalCanEventCallback fpCallback)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    if (pmHalCanPrivateContext -> mDispatcher.u16HandlerCount >= HAL_CAN_CALLBACK_HANDLER_SIZE)
    {
        return RES_ERROR_FULL;
    }
    uint16_t u16HandlerOffset = 0;
    tHalCanEventHandler *pNowHandler, *pNextHandler;
    pNowHandler = NULL;
    pNextHandler = pmHalCanPrivateContext -> mDispatcher.pHandlerListHead;
    while (pNextHandler != NULL)
    {
        if (pNextHandler -> pContext == pContext &&
            pNextHandler -> fpCallback == fpCallback)
        {
            return RES_ERROR_INVALID_STATE;
        }
        pNowHandler = pNextHandler;
        pNextHandler = pNextHandler -> pmNextHandler;
        u16HandlerOffset++;
    }
    if (pmHalCanPrivateContext -> mDispatcher.u16HandlerCount != u16HandlerOffset)
    {
        return RES_ERROR_NOT_FOUND;
    }
    pNextHandler = HalCanGetEventHandlerPointer(pmHalCan);
    pNextHandler -> pContext = pContext;
    pNextHandler -> fpCallback = fpCallback;
    pNextHandler -> pmNextHandler = NULL;
    if (pNowHandler == NULL)
    {
        pmHalCanPrivateContext -> mDispatcher.pHandlerListHead = pNextHandler;
    }
    else
    {
        pNowHandler -> pmNextHandler = pNextHandler;
    }
    pmHalCanPrivateContext -> mDispatcher.u16HandlerCount++;
    return RES_SUCCESS;
}

int8_t HalCanUnregisterEventHandler(tHalCan *pmHalCan, void *pContext, tfpHalCanEventCallback fpCallback)
{
    tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
    if (pmHalCanPrivateContext -> mDispatcher.u16HandlerCount == 0 ||
        pmHalCanPrivateContext -> mDispatcher.pHandlerListHead == NULL)
    {
        return RES_ERROR_EMPTY;
    }
    if (fpCallback == NULL)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    tHalCanEventHandler *pNowHandler, *pNextHandler;
    pNowHandler = NULL;
    pNextHandler = pmHalCanPrivateContext -> mDispatcher.pHandlerListHead;
    while (pNextHandler != NULL)
    {
        if (pNextHandler -> pContext == pContext &&
            pNextHandler -> fpCallback == fpCallback)
        {
            break;
        }
        pNowHandler = pNextHandler;
        pNextHandler = pNextHandler -> pmNextHandler;
    }
    if (pNextHandler == NULL)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    else
    {
        if (pNowHandler == NULL)
        {
            pmHalCanPrivateContext -> mDispatcher.pHandlerListHead = pNextHandler -> pmNextHandler;
        }
        else
        {
            pNowHandler -> pmNextHandler = pNextHandler -> pmNextHandler;
        }
        HalCanReleaseEventHandlerPointer(pmHalCan, pNextHandler);
    }
    pmHalCanPrivateContext -> mDispatcher.u16HandlerCount--;
    return RES_SUCCESS;
}

void HAL_CAN_0_IRQHandler(void)
{
    if (pmHalCanContext[kHAL_CAN_CHANNEL_0] != NULL)
    {
        HalCanIrqHandler(pmHalCanContext[kHAL_CAN_CHANNEL_0]);
    }
    return;
}

void HAL_CAN_1_IRQHandler(void)
{
    if (pmHalCanContext[kHAL_CAN_CHANNEL_1] != NULL)
    {
        HalCanIrqHandler(pmHalCanContext[kHAL_CAN_CHANNEL_1]);
    }
    return;
}
/* Example Code Sart */
#ifdef HAL_CAN_ENABLE_EXAMPLE
tHalCan gmHalCan0Config, gmHalCan1Config;
static bool bExampleIsExtendedFrame = false;
static uint32_t u32ExampleCount, u32Id = 0;
static uint64_t u64ExampleCount = 0;
static void HalCanExampleSwTimer(__far void *pvDest, uint16_t u16Event, void *pvData)
{
    if (u16Event & kLIB_SW_TIMER_EVT_1_MS)
    {
        tHalCan *pmHalCan = (tHalCan *) pvDest;
        tHalCanPrivate *pmHalCanPrivateContext = &(mHalCanPrivateContext[pmHalCan -> eChannel]);
        if (HalCanIsBusOff(pmHalCan) == false)
        {
            tHalCanFrame mCanFrame;
            mCanFrame.u8Dlc = 8;
            u32ExampleCount = 3;
            while (u32ExampleCount--)
            {
                if ((bExampleIsExtendedFrame == false && u32Id > HAL_CAN_ID_MASK_STD) ||
                    (bExampleIsExtendedFrame == true && u32Id > HAL_CAN_ID_MASK_EXT))
                {
                    u32Id = 0;
                    bExampleIsExtendedFrame = (bExampleIsExtendedFrame == false);
                }
                mCanFrame.u32Id = HalCanBuildId(u32Id, bExampleIsExtendedFrame, false);
                mCanFrame.tUnionData.u8Data[0] = (uint8_t) ((mCanFrame.u32Id >> 24) & 0xFF);
                mCanFrame.tUnionData.u8Data[1] = (uint8_t) ((mCanFrame.u32Id >> 16) & 0xFF);
                mCanFrame.tUnionData.u8Data[2] = (uint8_t) ((mCanFrame.u32Id >> 8) & 0xFF);
                mCanFrame.tUnionData.u8Data[3] = (uint8_t) (mCanFrame.u32Id & 0xFF);
                mCanFrame.tUnionData.u32Data[1] = mCanFrame.u32Id;
                HalCanPut(pmHalCan, &mCanFrame);
                u32Id++;
            }
        }
    }
    return;
}

void HalCanExampleInit(void)
{
    gmHalCan0Config.eChannel = kHAL_CAN_CHANNEL_0;
    gmHalCan0Config.u32NominalBitRate = 500000;
    gmHalCan0Config.bFdMode = false;
    gmHalCan0Config.bBitRateSwitch = false;
    gmHalCan0Config.bDisabledAutomaticRetransmission = true;
    gmHalCan0Config.bListenOnly = false;
    HalCanOpen(&gmHalCan0Config);
    return;
}

void HalCanExampleTransmit(void)
{
    LibSoftwareTimerHandlerOpen(HalCanExampleSwTimer, &gmHalCan0Config);
    return;
}
#endif
/* Example Code End */