/**
  ******************************************************************************
  * @file        LibNtc.c
  * <AUTHOR> 
  * @version     v0.0.1
  * @date        2025/05/27
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2025 FW Team</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define	B_VALUE		3435.0
#define	R25_VALUE	10000.0

/*
-40 ~ - 15 := 53752		25157*EXP(-0.05*A2)
-14 ~ +6:		21196	27509*EXP(-0.044*A28)
7 ~ 30 :		8305	26583*EXP(-0.039*A49)
31 ~ 45 : 		4902	23608*EXP(-0.035*A73)
46 ~ 66 : 		2512	20598*EXP(-0.032*A88)
67 ~ 83			1534	15818*EXP(-0.028*A109)
84 ~ 105 		857		13137*EXP(-0.026*A126)

*/

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/

/* Public function prototypes -----------------------------------------------*/
uint16_t LibSetRealTemperatureToInternalValue(int16_t i16Temp)
{
	i16Temp += 40;
	if(i16Temp < 0)
		i16Temp = 0;
	if(i16Temp > 160)
		i16Temp = 160;
	return (uint16_t)i16Temp;
}
uint16_t LibTemperatureToVoltage(int16_t i16Temp)
{
	uint16_t	u16N;

	double	dNtcR;
    double  dD1,dD2;
    
	i16Temp -= (int16_t)40;		//�ū׳]�w��Offset -40 ��
	
	if(i16Temp <= -15)		//-40 ~ - 15 := 25157*EXP(-0.05*A2)
	{
		dD1 = (double)i16Temp;
		dD1 *= (-5.0);
		dD1 /= 100.0;
		dD2 = exp(dD1);
		dNtcR = dD2 * 25157.0;
	}
	else if(i16Temp <= 6 )	//-14 ~ +6:27509*EXP(-0.044*A28)
	{
		dD1 = (double)i16Temp;
		dD1 *= (-44.0);
		dD1 /= 1000.0;
		dD2 = exp(dD1);
		dNtcR = dD2 * 27509.0;
	}
	else if(i16Temp <= 30 )	//7 ~ 30 :26583*EXP(-0.039*A49)
	{
		dD1 = (double)i16Temp;
		dD1 *= (-39.0);
		dD1 /= 1000.0;
		dD2 = exp(dD1);
		dNtcR = dD2 * 26583.0;
	}
	else if(i16Temp <= 45 )	//31 ~ 45 : = 23608*EXP(-0.035*A73)
	{
		dD1 = (double)i16Temp;
		dD1 *= (-35.0);
		dD1 /= 1000.0;
		dD2 = exp(dD1);
		dNtcR = dD2 * 23608.0;
	}
	else if(i16Temp <= 66 )	//46 ~ 66 : 20598*EXP(-0.032*A88)
	{
		dD1 = (double)i16Temp;
		dD1 *= (-32.0);
		dD1 /= 1000.0;
		dD2 = exp(dD1);
		dNtcR = dD2 * 20598.0;
	}
	else if(i16Temp <= 83 )	//67 ~ 83		=15818*EXP(-0.028*A109)
	{
		dD1 = (double)i16Temp;
		dD1 *= (-28.0);
		dD1 /= 1000.0;
		dD2 = exp(dD1);
		dNtcR = dD2 * 15818.0;
	}
	else //if(i16Temp <= 83 )	//84 ~ 105 :=13137*EXP(-0.026*A126)
	{
		dD1 = (double)i16Temp;
		dD1 *= (-26.0);
		dD1 /= 1000.0;
		dD2 = exp(dD1);
		dNtcR = dD2 * 13137.0;
	}
	
    dD1 = (5000.0 * dNtcR);
    dD1 /= (dNtcR + R25_VALUE);
	u16N = (uint16_t)dD1;
    return u16N;
}

uint16_t LibNtcRToTemperature(double dNtcR)
{
	uint16_t	u16Value;

    double  dD1,dD2,dD3;   
    
    if(dNtcR >= 193278) 			// < -40 
    {
    	dD1 = -40.0;
	}
    else if(dNtcR >= 53752.0)	//-40 ~ - 15 53752	25157*EXP(-0.05*A2)
    {
		dD2 = dNtcR / 25157.0;
   		dD3 = (double)log(dD2);
	    dD1 = dD3 * (-1000.0 / 50.0);   
    }	
    else if(dNtcR >= 21196.0)	//-14 ~ +6 	21196	27509*EXP(-0.044*A28)
    {
		dD2 = dNtcR / 27509.0;
   		dD3 = (double)log(dD2);
	    dD1 = dD3 * (-1000.0 / 44.0);   
    }
    
    else if(dNtcR >= 8305.0)	//7 ~ 30 8305	26583*EXP(-0.039*A49)
    {
		dD2 = dNtcR / 26583.0;
   		dD3 = (double)log(dD2);
	    dD1 = dD3 * (-1000.0 / 39.0);
    }
    else if(dNtcR >= 4902.0)	//31 ~ 45 4902	23608*EXP(-0.035*A73)
	{
		dD2 = dNtcR / 23608.0;
   		dD3 = (double)log(dD2);
	    dD1 = dD3 * (-1000.0 / 35.0);
    }	
    else if(dNtcR >= 2512.0)	//46 ~ 66 2512	20598*EXP(-0.032*A88)
	{
		dD2 = dNtcR / 20598.0;
   		dD3 = (double)log(dD2);
	    dD1 = dD3 * (-1000.0 / 32.0);
    }
    else if(dNtcR >= 1534.0)	//67 ~ 83	1534	15818*EXP(-0.028*A109)
	{
		dD2 = dNtcR / 15818.0;
   		dD3 = (double)log(dD2);
	    dD1 = dD3 * (-1000.0 / 28.0);
    }
    else if(dNtcR >= 200)
	{
		dD2 = dNtcR / 11068.0;
   		dD3 = (double)log(dD2);
	    dD1 = dD3 * (-1000.0 / 24.0);
    }
	else
	{
		dD1 = 16000;
	}

	dD1 *= 100.0;
	dD1 += 4000.0;
	if(dD1 < 0.0)	
		dD1 = 0;
   	u16Value = (uint16_t)dD1;
    if(u16Value >= 20000)
    	u16Value = 20000;

    return	u16Value;
}

#define	BAT_NTC_R_BASE		(double)10000.0
uint16_t LibNtcVoltageToTemperature(uint16_t u16NtcVoltage)
{
	uint16_t	u16Value;
    double  dNtcR;
    double  dAdcValue;

	dAdcValue = (double)u16NtcVoltage;

    if(dAdcValue >= 5000.0) 
    	dAdcValue = 5000.0;   //R2/R1=V2/V1 => R2xV1=R1xV2
    dNtcR = BAT_NTC_R_BASE * dAdcValue;             //R2=(R1xV2)/V1
    
    if(dAdcValue < 5000.0)
    	dNtcR /= (5000.0 - dAdcValue);		//�p��X�Ӫ� NTC �q����
    else
    	dNtcR = 300000.0;    
	u16Value = LibNtcRToTemperature(dNtcR);	
    return	u16Value;
}

/************************ (C) COPYRIGHT *****END OF FILE****/    
