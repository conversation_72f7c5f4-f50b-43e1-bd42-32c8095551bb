/*
******************************************************************************
* @file     HalI2c.c
* <AUTHOR>
* @brief    This file include MSPM0G3519 UART Hardwarre Abstraction Layer Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "HalI2c.h"
#include "Bsp.h"
#include "LibI2cFifo.h"
#include "LibFunctionReturnValueDefine.h"

/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
static uint8_t* pgu8TxBuf = NULL;
static uint8_t* pgu8RxBuf = NULL;

static uint32_t gu32DmaRxLen = 0;
static uint8_t* pgu8DmaRxBuf = NULL;

static uint32_t gu32TxLen = 0;
static uint32_t gu32TxCount = 0;

static uint32_t gu32RxLen = 0;
static uint32_t gu32RxCount = 0;

/* Local function declare ---------------------------------------------------*/
static tHalI2cChannelConfig HalI2cFindI2cChannel(eTypeHalI2cChannel eI2cChannel);
/* Global variables ---------------------------------------------------------*/
static tLibI2cFifoStatus gmHalI2c0TxFifoStatus;
static tLibI2cFifoStatus gmHalI2c0RxFifoStatus;
uint8_t gu8HalI2c0TxFifo[I2C_TX_MAX_PACKET_SIZE];
uint8_t gu8HalI2c0RxFifo[I2C_RX_MAX_PACKET_SIZE];
tfpHalI2cEvent gfpHalI2c0Event = NULL;
tfpHalI2cMasterEvent gfpHalI2c0MasterEvent = NULL;

static tLibI2cFifoStatus gmHalI2c1TxFifoStatus;
static tLibI2cFifoStatus gmHalI2c1RxFifoStatus;
uint8_t gu8HalI2c1TxFifo[I2C_TX_MAX_PACKET_SIZE];
uint8_t gu8HalI2c1RxFifo[I2C_RX_MAX_PACKET_SIZE];
tfpHalI2cEvent gfpHalI2c1Event = NULL;
tfpHalI2cMasterEvent gfpHalI2c1MasterEvent = NULL;

static tLibI2cFifoStatus gmHalI2c2TxFifoStatus;
static tLibI2cFifoStatus gmHalI2c2RxFifoStatus;
uint8_t gu8HalI2c2TxFifo[I2C_TX_MAX_PACKET_SIZE];
uint8_t gu8HalI2c2RxFifo[I2C_RX_MAX_PACKET_SIZE];
tfpHalI2cEvent gfpHalI2c2Event = NULL;
tfpHalI2cMasterEvent gfpHalI2c2MasterEvent = NULL;

eTypeHalI2cMasterEvent eHalI2cMasterEvent;

/* Local function prototypes ------------------------------------------------*/
static const DL_I2C_ClockConfig gI2CClockConfig = {
    .clockSel = DL_I2C_CLOCK_BUSCLK,
    .divideRatio = DL_I2C_CLOCK_DIVIDE_1,
};

static tHalI2cChannelConfig HalI2cFindI2cChannel(eTypeHalI2cChannel eI2cChannel)
{
    tHalI2cChannelConfig mI2cChannelConfig;

    switch (eI2cChannel)
    {
		case (kHAL_I2C0):	
            mI2cChannelConfig.pmDlI2cChannel = BSP_I2C_0;
            mI2cChannelConfig.u8IntIrqn = I2C0_INT_IRQn;
            mI2cChannelConfig.pmI2cTxFifoStatus = &gmHalI2c0TxFifoStatus;
            mI2cChannelConfig.pmI2cRxFifoStatus = &gmHalI2c0RxFifoStatus;
            mI2cChannelConfig.pfpI2cEvent = &gfpHalI2c0Event;
            return (mI2cChannelConfig);
            break;
		case (kHAL_I2C1):
            mI2cChannelConfig.pmDlI2cChannel = BSP_I2C_1;
            mI2cChannelConfig.u8IntIrqn = I2C1_INT_IRQn;
            mI2cChannelConfig.pmI2cTxFifoStatus = &gmHalI2c1TxFifoStatus;
            mI2cChannelConfig.pmI2cRxFifoStatus = &gmHalI2c1RxFifoStatus;	
            mI2cChannelConfig.pfpI2cEvent = &gfpHalI2c1Event;
            return (mI2cChannelConfig);
            break;
        case (kHAL_I2C2):	
            mI2cChannelConfig.pmDlI2cChannel = BSP_I2C_2;
            mI2cChannelConfig.u8IntIrqn = I2C2_INT_IRQn;
            mI2cChannelConfig.pmI2cTxFifoStatus = &gmHalI2c2TxFifoStatus;
            mI2cChannelConfig.pmI2cRxFifoStatus = &gmHalI2c2RxFifoStatus;
            mI2cChannelConfig.pfpI2cEvent = &gfpHalI2c2Event;
            return (mI2cChannelConfig);
            break;		      
	}
}

static tHalI2cMasterChannelConfig HalI2cFindI2cMasterChannel(eTypeHalI2cChannel eI2cChannel)
{
    tHalI2cMasterChannelConfig mI2cChannelConfig;

    switch (eI2cChannel)
    {
		case (kHAL_I2C0):	
            mI2cChannelConfig.pmDlI2cChannel = BSP_I2C_0;
            mI2cChannelConfig.u8IntIrqn = I2C0_INT_IRQn;
            mI2cChannelConfig.pfpI2cEvent = &gfpHalI2c0MasterEvent;
            return (mI2cChannelConfig);
            break;
		case (kHAL_I2C1):
            mI2cChannelConfig.pmDlI2cChannel = BSP_I2C_1;
            mI2cChannelConfig.u8IntIrqn = I2C1_INT_IRQn;
            mI2cChannelConfig.pfpI2cEvent = &gfpHalI2c1MasterEvent;
            return (mI2cChannelConfig);
            break;
        case (kHAL_I2C2):	
            mI2cChannelConfig.pmDlI2cChannel = BSP_I2C_2;
            mI2cChannelConfig.u8IntIrqn = I2C2_INT_IRQn;
            mI2cChannelConfig.pfpI2cEvent = &gfpHalI2c2MasterEvent;
            return (mI2cChannelConfig);
            break;		      
	}
}
/* Global function prototypes -----------------------------------------------*/

static int8_t HalI2cFifoStatusInit(tLibI2cFifoStatus* pmI2cFifoStatus, uint8_t* pu8FifoStartAddr, uint16_t u16FifoSize)
{
    // Check buffer for null pointer.
    if (pu8FifoStartAddr == NULL)
    {
        return RES_ERROR_NULL;
    }

    pmI2cFifoStatus->pu8FifoStartAddr   = pu8FifoStartAddr;
    pmI2cFifoStatus->u16FifoSize 	    = (u16FifoSize - 1);
    pmI2cFifoStatus->u16FifoPushInPosi  = 0;
    pmI2cFifoStatus->u16FifoPopOutPosi  = 0;

    return RES_SUCCESS;
}

int8_t HalI2cSlavePut(tHalI2cConfig* pmHalI2cConfig, uint8_t u8Data)
{
    uint16_t u16Size = 0;
	
    if (pmHalI2cConfig->eChannel == kHAL_I2C2)
    {
        if (LibI2cFifoPush(&gmHalI2c2TxFifoStatus, u8Data) == RES_SUCCESS)
	    {
		    if (LibI2cFifoGetUsedSize(&gmHalI2c2TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > I2C_TX_MAX_PACKET_SIZE)	
                {
                    u16Size = I2C_TX_MAX_PACKET_SIZE;
                }

                if (DL_I2C_isTargetTXFIFOEmpty(BSP_I2C_2) == true)
                {
			        for (uint16_t u16Idx = 0; u16Idx < u16Size; u16Idx++)
			        {	
				        LibI2cFifoPop(&gmHalI2c2TxFifoStatus, &gu8HalI2c2TxFifo[u16Idx]);
                    }

                    DL_I2C_fillTargetTXFIFO(BSP_I2C_2, gu8HalI2c2TxFifo, u16Size);
			    }
            }
        }
	}
    else if (pmHalI2cConfig->eChannel == kHAL_I2C1)
    {
        if (LibI2cFifoPush(&gmHalI2c1TxFifoStatus, u8Data) == RES_SUCCESS)
	    {
		    if (LibI2cFifoGetUsedSize(&gmHalI2c1TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > I2C_TX_MAX_PACKET_SIZE)	
                {
                    u16Size = I2C_TX_MAX_PACKET_SIZE;
                }

                if (DL_I2C_isTargetTXFIFOEmpty(BSP_I2C_1) == true)
                {
			        for (uint16_t u16Idx = 0; u16Idx < u16Size; u16Idx++)
			        {	
				        LibI2cFifoPop(&gmHalI2c1TxFifoStatus, &gu8HalI2c1TxFifo[u16Idx]);
                    }

                    DL_I2C_fillTargetTXFIFO(BSP_I2C_1, gu8HalI2c1TxFifo, u16Size);
			    }
            }
        }
	}
    else if (pmHalI2cConfig->eChannel == kHAL_I2C0)
    {
        if (LibI2cFifoPush(&gmHalI2c0TxFifoStatus, u8Data) == RES_SUCCESS)
	    {
		    if (LibI2cFifoGetUsedSize(&gmHalI2c0TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > I2C_TX_MAX_PACKET_SIZE)	
                {
                    u16Size = I2C_TX_MAX_PACKET_SIZE;
                }

                if (DL_I2C_isTargetTXFIFOEmpty(BSP_I2C_0) == true)
                {
			        for (uint16_t u16Idx = 0; u16Idx < u16Size; u16Idx++)
			        {	
				        LibI2cFifoPop(&gmHalI2c0TxFifoStatus, &gu8HalI2c0TxFifo[u16Idx]);
                    }

                    DL_I2C_fillTargetTXFIFO(BSP_I2C_0, gu8HalI2c0TxFifo, u16Size);
			    }
            }
        }
	}  
    else
	{
	    return RES_ERROR_NULL;
	}
	
	return RES_SUCCESS;
}

int8_t HalI2cSlaveGet(tHalI2cConfig* pmHalI2cConfig, uint8_t* pu8Data)
{
    int8_t i8ReturnValue;

    if (pmHalI2cConfig->eChannel == kHAL_I2C2)
    {
	    i8ReturnValue = LibI2cFifoPop(&gmHalI2c2RxFifoStatus, pu8Data);
    }
    else if (pmHalI2cConfig->eChannel == kHAL_I2C1)
    {
	    i8ReturnValue = LibI2cFifoPop(&gmHalI2c1RxFifoStatus, pu8Data);
    }
    else if (pmHalI2cConfig->eChannel == kHAL_I2C0)
    {
	    i8ReturnValue = LibI2cFifoPop(&gmHalI2c0RxFifoStatus, pu8Data);
    }   
    else
	{
		return RES_ERROR_NULL;
	}

	return(i8ReturnValue);
}

void HAL_I2C2_INST_IRQHandler(void)
{
    uint16_t u16Size = 0;

    switch (DL_I2C_getPendingInterrupt(BSP_I2C_2)) {
        case DL_I2C_IIDX_TARGET_START:            
            break;
        case DL_I2C_IIDX_TARGET_RXFIFO_TRIGGER:
            gu8HalI2c2RxFifo[0] = DL_I2C_receiveTargetData(BSP_I2C_2);
		    if (LibI2cFifoPush(&gmHalI2c2RxFifoStatus, gu8HalI2c2RxFifo[0]) == RES_SUCCESS)
            {
                DL_I2C_flushTargetTXFIFO(BSP_I2C_2);
                if (gfpHalI2c2Event != NULL)
                {
			    	gfpHalI2c2Event(kHAL_I2C_DATA_READY);
                }      
		    }                    
            break;
        case DL_I2C_IIDX_TARGET_TXFIFO_TRIGGER:
            if (LibI2cFifoGetUsedSize(&gmHalI2c2TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > I2C_TX_MAX_PACKET_SIZE)	
                {
                    u16Size = I2C_TX_MAX_PACKET_SIZE;
                }

			    for (uint16_t u16Idx = 0; u16Idx < u16Size; u16Idx++)
			    {	
				    LibI2cFifoPop(&gmHalI2c2TxFifoStatus, &gu8HalI2c2TxFifo[u16Idx]);
                }

                DL_I2C_fillTargetTXFIFO(BSP_I2C_2, gu8HalI2c2TxFifo, u16Size);
            }  
            break;
        case DL_I2C_IIDX_CONTROLLER_RX_DONE:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_RX_COMPLETE;
            break;
        case DL_I2C_IIDX_CONTROLLER_TX_DONE:
            DL_I2C_disableInterrupt(
                BSP_I2C_2, DL_I2C_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);
            eHalI2cMasterEvent = kHAL_I2C_STATUS_TX_COMPLETE;
            break;
        case DL_I2C_IIDX_CONTROLLER_RXFIFO_TRIGGER:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_RX_INPROGRESS;
            /* Receive all bytes from target */
            while (DL_I2C_isControllerRXFIFOEmpty(BSP_I2C_2) != true) {
                if (gu32RxCount < gu32RxLen) {
                    pgu8RxBuf[gu32RxCount++] = DL_I2C_receiveControllerData(BSP_I2C_2);
                } else {
                    /* Ignore and remove from FIFO if the buffer is full */
                    DL_I2C_receiveControllerData(BSP_I2C_2);
                }
                if(gfpHalI2c2MasterEvent != NULL)
                {
                    gfpHalI2c2MasterEvent(kHAL_I2C_STATUS_RX_INPROGRESS, pgu8RxBuf);
                }
            }
            break;
        case DL_I2C_IIDX_CONTROLLER_TXFIFO_TRIGGER:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_TX_INPROGRESS;
            /* Fill TX FIFO with next bytes to send */
            if (gu32TxCount < gu32TxLen) {
                gu32TxCount += DL_I2C_fillControllerTXFIFO(BSP_I2C_2, &pgu8TxBuf[gu32TxCount], gu32TxLen - gu32TxCount);
            }
            break;
            /* Not used for this example */
        case DL_I2C_IIDX_CONTROLLER_ARBITRATION_LOST:
        case DL_I2C_IIDX_CONTROLLER_NACK:
            if ((eHalI2cMasterEvent == kHAL_I2C_STATUS_RX_STARTED) || (eHalI2cMasterEvent == kHAL_I2C_STATUS_TX_STARTED)) {
                /* NACK interrupt if I2C Target is disconnected */
                eHalI2cMasterEvent = kHAL_I2C_STATUS_ERROR;
            }
        case DL_I2C_IIDX_CONTROLLER_EVENT1_DMA_DONE:
            // Dma Tx
            break;
        case DL_I2C_IIDX_CONTROLLER_EVENT2_DMA_DONE:
            // Dma Rx
            if (gfpHalI2c1MasterEvent != NULL && pgu8DmaRxBuf != NULL)
            {
				gfpHalI2c1MasterEvent(kHAL_I2C_STATUS_RX_DMA_INPROGRESS, pgu8DmaRxBuf);
            }
            break;
        default:
            break;
    } 
}

void HAL_I2C1_INST_IRQHandler(void)
{
    uint16_t u16Size = 0;
    switch (DL_I2C_getPendingInterrupt(BSP_I2C_1)) {
        case DL_I2C_IIDX_TARGET_START:            
            break;
        case DL_I2C_IIDX_TARGET_RXFIFO_TRIGGER:
            gu8HalI2c1RxFifo[0] = DL_I2C_receiveTargetData(BSP_I2C_1);
		    if (LibI2cFifoPush(&gmHalI2c1RxFifoStatus, gu8HalI2c1RxFifo[0]) == RES_SUCCESS)
            {
                DL_I2C_flushTargetTXFIFO(BSP_I2C_1);
                if (gfpHalI2c1Event != NULL)
                {
			    	gfpHalI2c1Event(kHAL_I2C_DATA_READY);
                }      
		    }                    
            break;
        case DL_I2C_IIDX_TARGET_TXFIFO_TRIGGER:
            if (LibI2cFifoGetUsedSize(&gmHalI2c1TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > I2C_TX_MAX_PACKET_SIZE)	
                {
                    u16Size = I2C_TX_MAX_PACKET_SIZE;
                }

			    for (uint16_t u16Idx = 0; u16Idx < u16Size; u16Idx++)
			    {	
				    LibI2cFifoPop(&gmHalI2c1TxFifoStatus, &gu8HalI2c1TxFifo[u16Idx]);
                }

                DL_I2C_fillTargetTXFIFO(BSP_I2C_1, gu8HalI2c1TxFifo, u16Size);
            }  
            break;
        case DL_I2C_IIDX_TARGET_STOP:
            break;
        case DL_I2C_IIDX_CONTROLLER_RX_DONE:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_RX_COMPLETE;
            break;
        case DL_I2C_IIDX_CONTROLLER_TX_DONE:
            DL_I2C_disableInterrupt(
                BSP_I2C_1, DL_I2C_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);
            eHalI2cMasterEvent = kHAL_I2C_STATUS_TX_COMPLETE;
            break;
        case DL_I2C_IIDX_CONTROLLER_RXFIFO_TRIGGER:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_RX_INPROGRESS;
            /* Receive all bytes from target */
            while (DL_I2C_isControllerRXFIFOEmpty(BSP_I2C_1) != true) {
                if (gu32RxCount < gu32RxLen) {
                    pgu8RxBuf[gu32RxCount++] = DL_I2C_receiveControllerData(BSP_I2C_1);
                } else {
                    /* Ignore and remove from FIFO if the buffer is full */
                    DL_I2C_receiveControllerData(BSP_I2C_1);
                }
                if(gfpHalI2c1MasterEvent != NULL)
                {
                    gfpHalI2c1MasterEvent(kHAL_I2C_STATUS_RX_INPROGRESS, pgu8RxBuf);
                }
            }
            break;
        case DL_I2C_IIDX_CONTROLLER_TXFIFO_TRIGGER:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_TX_INPROGRESS;
            /* Fill TX FIFO with next bytes to send */
            if (gu32TxCount < gu32TxLen) {
                gu32TxCount += DL_I2C_fillControllerTXFIFO(BSP_I2C_1, &pgu8TxBuf[gu32TxCount], gu32TxLen - gu32TxCount);
            }
            break;
            /* Not used for this example */
        case DL_I2C_IIDX_CONTROLLER_ARBITRATION_LOST:
        case DL_I2C_IIDX_CONTROLLER_NACK:
            if ((eHalI2cMasterEvent == kHAL_I2C_STATUS_RX_STARTED) || (eHalI2cMasterEvent == kHAL_I2C_STATUS_TX_STARTED)) {
                /* NACK interrupt if I2C Target is disconnected */
                eHalI2cMasterEvent = kHAL_I2C_STATUS_ERROR;
            }
            break;
        case DL_I2C_IIDX_CONTROLLER_EVENT1_DMA_DONE:
            // Dma Tx
            break;
        case DL_I2C_IIDX_CONTROLLER_EVENT2_DMA_DONE:
            // Dma Rx         
            if (gfpHalI2c1MasterEvent != NULL && pgu8DmaRxBuf != NULL)
            {
				gfpHalI2c1MasterEvent(kHAL_I2C_STATUS_RX_DMA_INPROGRESS, pgu8DmaRxBuf);
            }
            break;
        default:
            break;
    }
}

void HAL_I2C0_INST_IRQHandler(void)
{
    uint16_t u16Size = 0;

    switch (DL_I2C_getPendingInterrupt(BSP_I2C_0)) {
        case DL_I2C_IIDX_TARGET_START:            
            break;
        case DL_I2C_IIDX_TARGET_RXFIFO_TRIGGER:
            gu8HalI2c0RxFifo[0] = DL_I2C_receiveTargetData(BSP_I2C_0);
		    if (LibI2cFifoPush(&gmHalI2c0RxFifoStatus, gu8HalI2c0RxFifo[0]) == RES_SUCCESS)
            {
                DL_I2C_flushTargetTXFIFO(BSP_I2C_0);
                if (gfpHalI2c0Event != NULL)
                {
			    	gfpHalI2c0Event(kHAL_I2C_DATA_READY);
                }      
		    }                    
            break;
        case DL_I2C_IIDX_TARGET_TXFIFO_TRIGGER:
            if (LibI2cFifoGetUsedSize(&gmHalI2c0TxFifoStatus, &u16Size) == RES_SUCCESS)
		    {
                if (u16Size > I2C_TX_MAX_PACKET_SIZE)	
                {
                    u16Size = I2C_TX_MAX_PACKET_SIZE;
                }

			    for (uint16_t u16Idx = 0; u16Idx < u16Size; u16Idx++)
			    {	
				    LibI2cFifoPop(&gmHalI2c0TxFifoStatus, &gu8HalI2c0TxFifo[u16Idx]);
                }

                DL_I2C_fillTargetTXFIFO(BSP_I2C_0, gu8HalI2c0TxFifo, u16Size);
            }  
            break;
        case DL_I2C_IIDX_TARGET_STOP:
            break;
        case DL_I2C_IIDX_CONTROLLER_RX_DONE:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_RX_COMPLETE;
            break;
        case DL_I2C_IIDX_CONTROLLER_TX_DONE:
            DL_I2C_disableInterrupt(
                BSP_I2C_0, DL_I2C_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);
            eHalI2cMasterEvent = kHAL_I2C_STATUS_TX_COMPLETE;
            break;
        case DL_I2C_IIDX_CONTROLLER_RXFIFO_TRIGGER:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_RX_INPROGRESS;
            /* Receive all bytes from target */
            while (DL_I2C_isControllerRXFIFOEmpty(BSP_I2C_0) != true) {
                if (gu32RxCount < gu32RxLen) {
                    pgu8RxBuf[gu32RxCount++] = DL_I2C_receiveControllerData(BSP_I2C_0);
                } else {
                    /* Ignore and remove from FIFO if the buffer is full */
                    DL_I2C_receiveControllerData(BSP_I2C_0);
                }
                if(gfpHalI2c0MasterEvent != NULL)
                {
                    gfpHalI2c0MasterEvent(kHAL_I2C_STATUS_RX_INPROGRESS, pgu8RxBuf);
                }
            }
            break;
        case DL_I2C_IIDX_CONTROLLER_TXFIFO_TRIGGER:
            eHalI2cMasterEvent = kHAL_I2C_STATUS_TX_INPROGRESS;
            /* Fill TX FIFO with next bytes to send */
            if (gu32TxCount < gu32TxLen) {
                gu32TxCount += DL_I2C_fillControllerTXFIFO(BSP_I2C_0, &pgu8TxBuf[gu32TxCount], gu32TxLen - gu32TxCount);
            }
            break;
            /* Not used for this example */
        case DL_I2C_IIDX_CONTROLLER_ARBITRATION_LOST:
        case DL_I2C_IIDX_CONTROLLER_NACK:
            if ((eHalI2cMasterEvent == kHAL_I2C_STATUS_RX_STARTED) || (eHalI2cMasterEvent == kHAL_I2C_STATUS_TX_STARTED)) {
                /* NACK interrupt if I2C Target is disconnected */
                eHalI2cMasterEvent = kHAL_I2C_STATUS_ERROR;
            }
        case DL_I2C_IIDX_CONTROLLER_EVENT1_DMA_DONE:
            // Dma Tx
            break;
        case DL_I2C_IIDX_CONTROLLER_EVENT2_DMA_DONE:
            // Dma Rx
            if (gfpHalI2c1MasterEvent != NULL && pgu8DmaRxBuf != NULL)
            {
				gfpHalI2c1MasterEvent(kHAL_I2C_STATUS_RX_DMA_INPROGRESS, pgu8DmaRxBuf);
            }
            break;
        default:
            break;
    }
}

void HalI2cGpioInit(uint32_t u32I2CSdaPincm, uint32_t u32I2CSda, uint32_t u32I2CSclPincm, uint32_t u32I2CScl)
{
    DL_GPIO_initPeripheralInputFunctionFeatures(u32I2CSdaPincm,
        u32I2CSda, DL_GPIO_INVERSION_DISABLE,
        DL_GPIO_RESISTOR_NONE, DL_GPIO_HYSTERESIS_DISABLE,
        DL_GPIO_WAKEUP_DISABLE);
    DL_GPIO_initPeripheralInputFunctionFeatures(u32I2CSclPincm,
        u32I2CScl, DL_GPIO_INVERSION_DISABLE,
        DL_GPIO_RESISTOR_NONE, DL_GPIO_HYSTERESIS_DISABLE,
        DL_GPIO_WAKEUP_DISABLE);
    DL_GPIO_enableHiZ(u32I2CSdaPincm);
    DL_GPIO_enableHiZ(u32I2CSclPincm);
}


void HalI2cMasterInit(tHalI2cMasterConfig* pmHalI2cConfig)
{
    tHalI2cMasterChannelConfig mHalI2cChannelConfig;

    mHalI2cChannelConfig = HalI2cFindI2cMasterChannel(pmHalI2cConfig->eChannel);

    DL_I2C_reset(mHalI2cChannelConfig.pmDlI2cChannel);
    DL_I2C_enablePower(mHalI2cChannelConfig.pmDlI2cChannel);
    delay_cycles(16);

    if (pmHalI2cConfig->eChannel == kHAL_I2C2) 
    {
        HalI2cGpioInit(BSP_I2C_2_SDA_IOMUX, BSP_I2C_2_SDA_IOMUX_PF, BSP_I2C_2_SCL_IOMUX, BSP_I2C_2_SCL_IOMUX_PF);
    } 
    else if (pmHalI2cConfig->eChannel == kHAL_I2C1) 
    {
        HalI2cGpioInit(BSP_I2C_1_SDA_IOMUX, BSP_I2C_1_SDA_IOMUX_PF, BSP_I2C_1_SCL_IOMUX, BSP_I2C_1_SCL_IOMUX_PF);
    } 
    else if (pmHalI2cConfig->eChannel == kHAL_I2C0) 
    {
        HalI2cGpioInit(BSP_I2C_0_SDA_IOMUX, BSP_I2C_0_SDA_IOMUX_PF, BSP_I2C_0_SCL_IOMUX, BSP_I2C_0_SCL_IOMUX_PF);
    } 

    DL_I2C_setClockConfig(mHalI2cChannelConfig.pmDlI2cChannel,
        (DL_I2C_ClockConfig *) &gI2CClockConfig);
    DL_I2C_disableAnalogGlitchFilter(mHalI2cChannelConfig.pmDlI2cChannel);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

    /* Configure Controller Mode */
    DL_I2C_resetControllerTransfer(mHalI2cChannelConfig.pmDlI2cChannel);
    if(pmHalI2cConfig->eFreq == kHAL_I2C_FREQUENCY_400k)
    {
        /* Set frequency to 400000 Hz*/
        DL_I2C_setTimerPeriod(mHalI2cChannelConfig.pmDlI2cChannel, 9);
        DL_I2C_setControllerTXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_TX_FIFO_LEVEL_EMPTY);
        DL_I2C_setControllerRXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_RX_FIFO_LEVEL_BYTES_1);
        DL_I2C_enableControllerClockStretching(mHalI2cChannelConfig.pmDlI2cChannel);
    }
    else if(pmHalI2cConfig->eFreq == kHAL_I2C_FREQUENCY_100k)
    {
        /* Set frequency to 100000 Hz*/
        DL_I2C_setTimerPeriod(mHalI2cChannelConfig.pmDlI2cChannel, 31);
        DL_I2C_setControllerTXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_TX_FIFO_LEVEL_EMPTY);
        DL_I2C_setControllerRXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_RX_FIFO_LEVEL_BYTES_1);
        DL_I2C_enableControllerClockStretching(mHalI2cChannelConfig.pmDlI2cChannel);
    }  

    DL_I2C_enableInterrupt(mHalI2cChannelConfig.pmDlI2cChannel,
                           DL_I2C_INTERRUPT_CONTROLLER_ARBITRATION_LOST |
                           DL_I2C_INTERRUPT_CONTROLLER_NACK |
                           DL_I2C_INTERRUPT_CONTROLLER_RXFIFO_TRIGGER |
                           DL_I2C_INTERRUPT_CONTROLLER_RX_DONE |
                           DL_I2C_INTERRUPT_CONTROLLER_TX_DONE );

    DL_I2C_enableController(mHalI2cChannelConfig.pmDlI2cChannel);   

    NVIC_EnableIRQ(mHalI2cChannelConfig.u8IntIrqn);
    DL_SYSCTL_disableSleepOnExit();

    *(mHalI2cChannelConfig.pfpI2cEvent) = pmHalI2cConfig->fpI2cEvent;
}

tFunRetunCode HalI2cMasterSend(tHalI2cMasterConfig* pmHalI2cConfig, uint8_t u8SlaveAddress, uint8_t* pu8TxBuf, uint32_t u32TxLen)
{
    tHalI2CHandle *pI2C;
    pgu8TxBuf = pu8TxBuf;
    gu32TxLen = u32TxLen;
    gu32TxCount = 0;

    if (pmHalI2cConfig->eChannel == kHAL_I2C2) {
        pI2C = BSP_I2C_2;
    } else if (pmHalI2cConfig->eChannel == kHAL_I2C1) {
        pI2C = BSP_I2C_1;
    } else if (pmHalI2cConfig->eChannel == kHAL_I2C0) {
        pI2C = BSP_I2C_0;
    } else {
        return(RES_ERROR_FAIL);
    }

    gu32TxCount = DL_I2C_fillControllerTXFIFO(pI2C, (uint8_t*) pgu8TxBuf, gu32TxLen);

    if (gu32TxCount < gu32TxLen) {
        DL_I2C_enableInterrupt(pI2C, DL_I2C_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);
    } else {
        DL_I2C_disableInterrupt(pI2C, DL_I2C_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);
    }

    eHalI2cMasterEvent = kHAL_I2C_STATUS_TX_STARTED;

    while (!(DL_I2C_getControllerStatus(pI2C) & DL_I2C_CONTROLLER_STATUS_IDLE));

    DL_I2C_startControllerTransfer(pI2C, u8SlaveAddress, DL_I2C_CONTROLLER_DIRECTION_TX, gu32TxLen);

    while ((eHalI2cMasterEvent != kHAL_I2C_STATUS_TX_COMPLETE) && (eHalI2cMasterEvent != kHAL_I2C_STATUS_ERROR)) {
        __WFE();
    }

    while (DL_I2C_getControllerStatus(pI2C) & DL_I2C_CONTROLLER_STATUS_BUSY_BUS);

    if (DL_I2C_getControllerStatus(pI2C) & DL_I2C_CONTROLLER_STATUS_ERROR) {
        return (RES_ERROR_FAIL);
    }

    while (!(DL_I2C_getControllerStatus(pI2C) & DL_I2C_CONTROLLER_STATUS_IDLE));
    return (RES_SUCCESS);
}

tFunRetunCode HalI2cMasterReceive(tHalI2cMasterConfig* pmHalI2cConfig, uint8_t u8SlaveAddress, uint8_t* pu8RxBuf, uint32_t u32RxLen)
{
    tHalI2CHandle *pI2C;
    pgu8RxBuf = pu8RxBuf;
    gu32RxLen = u32RxLen;
    gu32RxCount = 0;

    if (pmHalI2cConfig->eChannel == kHAL_I2C2) {
        pI2C = BSP_I2C_2;
    } else if (pmHalI2cConfig->eChannel == kHAL_I2C1) {
        pI2C = BSP_I2C_1;
    } else if (pmHalI2cConfig->eChannel == kHAL_I2C0) {
        pI2C = BSP_I2C_0;
    } else {
        return (RES_ERROR_INVALID_PARAM);
    }

    eHalI2cMasterEvent = kHAL_I2C_STATUS_RX_STARTED;
    DL_I2C_startControllerTransfer(pI2C, u8SlaveAddress, DL_I2C_CONTROLLER_DIRECTION_RX, gu32RxLen);

    while (eHalI2cMasterEvent != kHAL_I2C_STATUS_RX_COMPLETE) {
        __WFE();
    }

    while (DL_I2C_getControllerStatus(pI2C) & DL_I2C_CONTROLLER_STATUS_BUSY_BUS);

    if (DL_I2C_getControllerStatus(pI2C) & DL_I2C_CONTROLLER_STATUS_BUSY_BUS)
    {
        return (RES_ERROR_BUSY);
    }
    return (RES_SUCCESS);
}

/* I2C Slave Function*/
void HalI2cSlaveInit(tHalI2cConfig* pmHalI2cConfig, uint8_t u8SlaveAddress)
{
    tHalI2cChannelConfig mHalI2cChannelConfig;

    mHalI2cChannelConfig = HalI2cFindI2cChannel(pmHalI2cConfig->eChannel);
    
    DL_I2C_reset(mHalI2cChannelConfig.pmDlI2cChannel);
    DL_I2C_enablePower(mHalI2cChannelConfig.pmDlI2cChannel);
    delay_cycles(16);

    if (pmHalI2cConfig->eChannel == kHAL_I2C2) 
    {
        HalI2cGpioInit(BSP_I2C_2_SDA_IOMUX, BSP_I2C_2_SDA_IOMUX_PF, BSP_I2C_2_SCL_IOMUX, BSP_I2C_2_SCL_IOMUX_PF);
    } 
    else if (pmHalI2cConfig->eChannel == kHAL_I2C1) 
    {
        HalI2cGpioInit(BSP_I2C_1_SDA_IOMUX, BSP_I2C_1_SDA_IOMUX_PF, BSP_I2C_1_SCL_IOMUX, BSP_I2C_1_SCL_IOMUX_PF);
    } 
    else if (pmHalI2cConfig->eChannel == kHAL_I2C0) 
    {
        HalI2cGpioInit(BSP_I2C_0_SDA_IOMUX, BSP_I2C_0_SDA_IOMUX_PF, BSP_I2C_0_SCL_IOMUX, BSP_I2C_0_SCL_IOMUX_PF);
    }

    DL_I2C_setClockConfig(mHalI2cChannelConfig.pmDlI2cChannel,
        (DL_I2C_ClockConfig *) &gI2CClockConfig);
    DL_I2C_disableAnalogGlitchFilter(mHalI2cChannelConfig.pmDlI2cChannel);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

    /* Configure Controller Mode */
    DL_I2C_setTargetOwnAddress(mHalI2cChannelConfig.pmDlI2cChannel, u8SlaveAddress);
    DL_I2C_setTargetTXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_TX_FIFO_LEVEL_EMPTY);
    DL_I2C_setTargetRXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_RX_FIFO_LEVEL_BYTES_1);
    DL_I2C_enableTargetTXEmptyOnTXRequest(mHalI2cChannelConfig.pmDlI2cChannel);

    DL_I2C_enableTargetClockStretching(mHalI2cChannelConfig.pmDlI2cChannel);

    /* Workaround for errata I2C_ERR_04 */
    DL_I2C_disableTargetWakeup(mHalI2cChannelConfig.pmDlI2cChannel);

    /* Configure Interrupts */
    DL_I2C_enableInterrupt(mHalI2cChannelConfig.pmDlI2cChannel,
                           DL_I2C_INTERRUPT_TARGET_RXFIFO_TRIGGER |
                           DL_I2C_INTERRUPT_TARGET_START |
                           DL_I2C_INTERRUPT_TARGET_STOP);

    /* Enable module */
    DL_I2C_enableTarget(mHalI2cChannelConfig.pmDlI2cChannel);
    /* 啟用傳送 FIFO 中斷（啟動後才會處理） */
    DL_I2C_enableInterrupt(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_INTERRUPT_TARGET_TXFIFO_TRIGGER);

    NVIC_EnableIRQ(mHalI2cChannelConfig.u8IntIrqn);

    HalI2cFifoStatusInit(mHalI2cChannelConfig.pmI2cTxFifoStatus, pmHalI2cConfig->mI2cFifoConfig.pu8HalI2cTxFifo, 
    pmHalI2cConfig->mI2cFifoConfig.u16HalI2cTxFifoSize);
    HalI2cFifoStatusInit(mHalI2cChannelConfig.pmI2cRxFifoStatus, pmHalI2cConfig->mI2cFifoConfig.pu8HalI2cRxFifo, 
    pmHalI2cConfig->mI2cFifoConfig.u16HalI2cRxFifoSize);

    *(mHalI2cChannelConfig.pfpI2cEvent) = pmHalI2cConfig->fpI2cEvent;

}

void HalI2cMasterDmaInit(tHalI2cMasterConfig* pmHalI2cConfig)
{
    DL_DMA_Config gDMA_CH0Config;
    DL_DMA_Config gDMA_CH1Config; 

    tHalI2cMasterChannelConfig mHalI2cChannelConfig;
    mHalI2cChannelConfig = HalI2cFindI2cMasterChannel(pmHalI2cConfig->eChannel);

    DL_I2C_reset(mHalI2cChannelConfig.pmDlI2cChannel);
    DL_I2C_enablePower(mHalI2cChannelConfig.pmDlI2cChannel);
    delay_cycles(16);

    if (pmHalI2cConfig->eChannel == kHAL_I2C2) 
    {
        HalI2cGpioInit(BSP_I2C_2_SDA_IOMUX, BSP_I2C_2_SDA_IOMUX_PF, BSP_I2C_2_SCL_IOMUX, BSP_I2C_2_SCL_IOMUX_PF);
        gDMA_CH1Config.trigger        = DMA_I2C2_RX_TRIG;
        gDMA_CH0Config.trigger        = DMA_I2C2_TX_TRIG;
    } 
    else if (pmHalI2cConfig->eChannel == kHAL_I2C1) 
    {
        HalI2cGpioInit(BSP_I2C_1_SDA_IOMUX, BSP_I2C_1_SDA_IOMUX_PF, BSP_I2C_1_SCL_IOMUX, BSP_I2C_1_SCL_IOMUX_PF);
        gDMA_CH1Config.trigger        = DMA_I2C1_RX_TRIG;
        gDMA_CH0Config.trigger        = DMA_I2C1_TX_TRIG;  
    } 
    else if (pmHalI2cConfig->eChannel == kHAL_I2C0) 
    {
        HalI2cGpioInit(BSP_I2C_0_SDA_IOMUX, BSP_I2C_0_SDA_IOMUX_PF, BSP_I2C_0_SCL_IOMUX, BSP_I2C_0_SCL_IOMUX_PF);
        gDMA_CH1Config.trigger        = DMA_I2C0_RX_TRIG;
        gDMA_CH0Config.trigger        = DMA_I2C0_TX_TRIG;
    }

    gDMA_CH1Config.transferMode   = DL_DMA_SINGLE_TRANSFER_MODE;
    gDMA_CH1Config.extendedMode   = DL_DMA_NORMAL_MODE;
    gDMA_CH1Config.destIncrement  = DL_DMA_ADDR_INCREMENT;
    gDMA_CH1Config.srcIncrement   = DL_DMA_ADDR_UNCHANGED;
    gDMA_CH1Config.destWidth      = DL_DMA_WIDTH_BYTE;
    gDMA_CH1Config.srcWidth       = DL_DMA_WIDTH_BYTE;
    gDMA_CH1Config.triggerType    = DL_DMA_TRIGGER_TYPE_EXTERNAL;

    gDMA_CH0Config.transferMode   = DL_DMA_SINGLE_TRANSFER_MODE;
    gDMA_CH0Config.extendedMode   = DL_DMA_NORMAL_MODE;
    gDMA_CH0Config.destIncrement  = DL_DMA_ADDR_UNCHANGED;
    gDMA_CH0Config.srcIncrement   = DL_DMA_ADDR_INCREMENT;
    gDMA_CH0Config.destWidth      = DL_DMA_WIDTH_BYTE;
    gDMA_CH0Config.srcWidth       = DL_DMA_WIDTH_BYTE;
    gDMA_CH0Config.triggerType    = DL_DMA_TRIGGER_TYPE_EXTERNAL;

    DL_DMA_clearInterruptStatus(DMA, BSP_I2C_DMA_TX_INTERRUPT_CHANNEL);
    DL_DMA_enableInterrupt(DMA, BSP_I2C_DMA_TX_INTERRUPT_CHANNEL);
    DL_DMA_clearInterruptStatus(DMA, BSP_I2C_DMA_RX_INTERRUPT_CHANNEL);
    DL_DMA_enableInterrupt(DMA, BSP_I2C_DMA_RX_INTERRUPT_CHANNEL);
    DL_DMA_initChannel(DMA, BSP_I2C_DMA_RX_CHANNEL , (DL_DMA_Config *) &gDMA_CH1Config);
    DL_DMA_initChannel(DMA, BSP_I2C_DMA_TX_CHANNEL , (DL_DMA_Config *) &gDMA_CH0Config); 

    DL_I2C_setClockConfig(mHalI2cChannelConfig.pmDlI2cChannel,
        (DL_I2C_ClockConfig *) &gI2CClockConfig);
    DL_I2C_disableAnalogGlitchFilter(mHalI2cChannelConfig.pmDlI2cChannel);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

    /* Configure Controller Mode */
    DL_I2C_resetControllerTransfer(mHalI2cChannelConfig.pmDlI2cChannel);
    if(pmHalI2cConfig->eFreq == kHAL_I2C_FREQUENCY_400k)
    {
        /* Set frequency to 400000 Hz*/
        DL_I2C_setTimerPeriod(mHalI2cChannelConfig.pmDlI2cChannel, 9);
        DL_I2C_setControllerTXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_TX_FIFO_LEVEL_EMPTY);
        DL_I2C_setControllerRXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_RX_FIFO_LEVEL_BYTES_1);
        DL_I2C_enableControllerClockStretching(mHalI2cChannelConfig.pmDlI2cChannel);
    }
    else if(pmHalI2cConfig->eFreq == kHAL_I2C_FREQUENCY_100k)
    {
        /* Set frequency to 100000 Hz*/
        DL_I2C_setTimerPeriod(mHalI2cChannelConfig.pmDlI2cChannel, 31);
        DL_I2C_setControllerTXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_TX_FIFO_LEVEL_EMPTY);
        DL_I2C_setControllerRXFIFOThreshold(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_RX_FIFO_LEVEL_BYTES_1);
        DL_I2C_enableControllerClockStretching(mHalI2cChannelConfig.pmDlI2cChannel);
    }  
    DL_I2C_enableController(mHalI2cChannelConfig.pmDlI2cChannel);

     /* Configure DMA Event 1 */
    DL_I2C_enableDMAEvent(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_EVENT_ROUTE_1,
                          DL_I2C_DMA_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);
    /* Configure DMA Event 2 */
    DL_I2C_enableDMAEvent(mHalI2cChannelConfig.pmDlI2cChannel, DL_I2C_EVENT_ROUTE_2,
                          DL_I2C_DMA_INTERRUPT_CONTROLLER_RXFIFO_TRIGGER);

    DL_I2C_enableInterrupt(mHalI2cChannelConfig.pmDlI2cChannel,        
        DL_I2C_INTERRUPT_CONTROLLER_EVENT1_DMA_DONE | 
        DL_I2C_INTERRUPT_CONTROLLER_EVENT2_DMA_DONE     
    );     
    
    DL_DMA_enableInterrupt(DMA, BSP_I2C_DMA_TX_CHANNEL);
    DL_DMA_enableInterrupt(DMA, BSP_I2C_DMA_RX_CHANNEL);

    NVIC_EnableIRQ(mHalI2cChannelConfig.u8IntIrqn);
    DL_SYSCTL_disableSleepOnExit();

    *(mHalI2cChannelConfig.pfpI2cEvent) = pmHalI2cConfig->fpI2cEvent;
}

tFunRetunCode HalI2cMasterDmaSend(tHalI2cMasterConfig* pmHalI2cConfig, uint8_t u8SlaveAddress, uint8_t* pu8TxBuf, uint32_t u32TxLen)
{
    tHalI2CHandle *pI2C;

    if (pmHalI2cConfig->eChannel == kHAL_I2C0) 
    {
        pI2C = BSP_I2C_0;
    } 
    else if (pmHalI2cConfig->eChannel == kHAL_I2C1) 
    {
        pI2C = BSP_I2C_1;
    }
    else if (pmHalI2cConfig->eChannel == kHAL_I2C2) 
    {
        pI2C = BSP_I2C_2;
    }
    else {
        return RES_ERROR_FAIL;
    }

    DL_DMA_setSrcAddr(DMA, BSP_I2C_DMA_TX_CHANNEL, (uint32_t) pu8TxBuf);
    DL_DMA_setDestAddr(DMA, BSP_I2C_DMA_TX_CHANNEL, (uint32_t) &pI2C->MASTER.MTXDATA);
    DL_DMA_setTransferSize(DMA, BSP_I2C_DMA_TX_CHANNEL, u32TxLen);
    DL_DMA_enableChannel(DMA, BSP_I2C_DMA_TX_CHANNEL);

    DL_I2C_setTargetAddress(pI2C, u8SlaveAddress);
    
    // 關鍵：需保證 I2C 狀態空閒
    while (DL_I2C_getControllerStatus(pI2C) & DL_I2C_CONTROLLER_STATUS_BUSY_BUS);
    DL_I2C_startControllerTransfer(pI2C, u8SlaveAddress, DL_I2C_CONTROLLER_DIRECTION_TX, u32TxLen);


    return RES_SUCCESS;
}

tFunRetunCode HalI2cMasterDmaRead(tHalI2cMasterConfig* pmHalI2cConfig, uint8_t u8SlaveAddress, uint8_t* pu8RxBuf, uint32_t u32RxLen)
{
    tHalI2CHandle *pI2C;

    if (pmHalI2cConfig->eChannel == kHAL_I2C0) 
    {
        pI2C = BSP_I2C_0;
    } 
    else if (pmHalI2cConfig->eChannel == kHAL_I2C1) 
    {
        pI2C = BSP_I2C_1;
    }
    else if (pmHalI2cConfig->eChannel == kHAL_I2C2) 
    {
        pI2C = BSP_I2C_2;
    }
    else {
        return RES_ERROR_FAIL;
    }

    gu32DmaRxLen = u32RxLen;
    pgu8DmaRxBuf = pu8RxBuf;
    
    // 設定 DMA：來源是 I2C RX FIFO，目的地是 RAM buffer
    DL_DMA_setSrcAddr(DMA, BSP_I2C_DMA_RX_CHANNEL, (uint32_t)&pI2C->MASTER.MRXDATA);
    DL_DMA_setDestAddr(DMA, BSP_I2C_DMA_RX_CHANNEL, (uint32_t) pgu8DmaRxBuf);
    DL_DMA_setTransferSize(DMA, BSP_I2C_DMA_RX_CHANNEL, gu32DmaRxLen);
    DL_DMA_enableChannel(DMA, BSP_I2C_DMA_RX_CHANNEL);

    DL_I2C_setTargetAddress(pI2C, u8SlaveAddress);

    // 確保 I2C bus 是 idle 狀態
    while (DL_I2C_getControllerStatus(pI2C) & DL_I2C_CONTROLLER_STATUS_BUSY_BUS);

    // 啟動 Controller 接收傳輸
    DL_I2C_startControllerTransfer(pI2C, u8SlaveAddress, DL_I2C_CONTROLLER_DIRECTION_RX, gu32DmaRxLen);

    return RES_SUCCESS;
}

/*Example For I2C Slave Response to I2C Master*/
static void HalI2cCallback(eTypeHalI2cMasterEvent eI2cEvtt, uint8_t *u8RxData)
{
    uint8_t u8RxCBData[3] = {0};
    for (int i = 0; i < 3; i++)
    {
        u8RxCBData[i] = u8RxData[i];
    }   
}

static uint8_t u8RxBuf[10];
#define HAL_I2C_EEPROM_TEST_PROGRAM_ADDR (0x0010)
static tHalI2cMasterConfig gmHalI2c2Config ;
void HalI2CDMAExample(void)
{
    gmHalI2c2Config.eChannel = kHAL_I2C1;
    gmHalI2c2Config.eFreq = kHAL_I2C_FREQUENCY_400k;
    gmHalI2c2Config.fpI2cEvent = HalI2cCallback;
    HalI2cMasterDmaInit(&gmHalI2c2Config); 

    uint8_t u8TxBuf[12] = {0x00, 0x10, 0x55, 0x01, 0xAA};
    
    uint32_t u32ProgramAddr = HAL_I2C_EEPROM_TEST_PROGRAM_ADDR;

    HalI2cMasterDmaSend(&gmHalI2c2Config, 0x50, u8TxBuf, 5);

    delay_cycles(1600000 * 10);

    uint8_t u8TxBufRecv[2] = {0x00, 0x10};

    HalI2cMasterDmaSend(&gmHalI2c2Config, 0x50, u8TxBufRecv, 2);

    if (HalI2cMasterDmaRead(&gmHalI2c2Config, 0x50, u8RxBuf, 3))
    {
        //return (RES_ERROR_FAIL);
    }
}