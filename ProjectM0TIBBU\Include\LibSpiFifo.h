/*
******************************************************************************
* @file     LibSpiFifo.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __LIB_SPI_FIFO_H__
#define __LIB_SPI_FIFO_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "HalSpi.h"
#include "LibFunctionReturnValueDefine.h"
/* Global define ------------------------------------------------------------*/
/* Global typedef -----------------------------------------------------------*/
typedef enum
{
    kLIB_SPI_FIFO_TRANSFER_MODE_NONE = 0,
    kLIB_SPI_FIFO_TRANSFER_MODE_BLOCKING_FULL_DUPLEX,
    kLIB_SPI_FIFO_TRANSFER_MODE_BLOCKING_HALF_DUPLEX,
    kLIB_SPI_FIFO_TRANSFER_MODE_DMA_FULL_DUPLEX,
    //kLIB_SPI_FIFO_TRANSFER_MODE_DMA_HALF_DUPLEX,
    kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV,
} eTypeLibSpiFifoTransferMode;

typedef struct
{
    uint8_t                     *pu8TxData;
    uint16_t                    u16TxSize;
    uint8_t                     *pu8RxData;
    uint16_t                    u16RxSize;
    uint8_t                     u8CsIndex;
    eTypeLibSpiFifoTransferMode eTransferMode;
    tfpHalSpiEvent              fpCallback;
} tLibSpiPacket;

typedef struct
{
    tLibSpiPacket   *pmFifoStartAddr;   /* FIFO queue buffer start address */
    uint16_t        u16FifoSize;        /* FIFO queue buffer size */
    uint16_t        u16FifoPushInPosi;  /* FIFO queue buffer push in position */
    uint16_t        u16FifoPopOutPosi;  /* FIFO queue buffer pop out position */
} tLibSpiFifo;
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
bool LibSpiFifoIsEmpty(tLibSpiFifo *pmSpiFifo);
uint16_t LibSpiFifoGetCount(tLibSpiFifo *pmSpiFifo);
int8_t LibSpiFifoPushAndCloneTx(tLibSpiFifo *pmSpiFifo, tLibSpiPacket *pmLibSpiPacket);
int8_t LibSpiFifoPush(tLibSpiFifo *pmSpiFifo, tLibSpiPacket *pmLibSpiPacket);
int8_t LibSpiFifoPop(tLibSpiFifo *pmSpiFifo, tLibSpiPacket *pmLibSpiPacket);
int8_t LibSpiFifoPeekFront(tLibSpiFifo *pmSpiFifo, tLibSpiPacket *pmLibSpiPacket);

#ifdef __cplusplus
}
#endif

#endif