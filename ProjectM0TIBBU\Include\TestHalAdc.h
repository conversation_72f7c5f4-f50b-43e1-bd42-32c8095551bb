/*
******************************************************************************
* @file     TestHalAdc.h
* <AUTHOR>
* @brief    This file is Test HalAdc function

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __TEST_HAL_ADC_H__
#define	__TEST_HAL_ADC_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include <stdint.h>
#include "HalGpio.h"
#include "HalAdc.h"

/* Global define ------------------------------------------------------------*/

/* Global typedef -----------------------------------------------------------*/
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void TestHalAdcApp(void);

#ifdef __cplusplus
}
#endif

#endif
