/**
  ******************************************************************************
  * @file        LibSwTimer.h
  * <AUTHOR>
  * @version     v0.0
  * @date        2021/10/14
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

#ifndef _LIB_SW_TIMER_H
#define _LIB_SW_TIMER_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "LibFunctionReturnValueDefine.h"
#include "LibFunctionPointerRegister.h"

/* Public define ------------------------------------------------------------*/
#define tLibSwTimerEvtHandler tLibRegisterEvtHandler
/* Public typedef -----------------------------------------------------------*/
typedef enum 
{
    kLIB_SW_TIMER_EVT_10_0_MS = (1 << 0),
    kLIB_SW_TIMER_EVT_10_1_MS = (1 << 1),
    kLIB_SW_TIMER_EVT_10_2_MS = (1 << 2),
    kLIB_SW_TIMER_EVT_10_3_MS = (1 << 3),
    kLIB_SW_TIMER_EVT_10_4_MS = (1 << 4),
    kLIB_SW_TIMER_EVT_10_5_MS = (1 << 5),
    kLIB_SW_TIMER_EVT_10_6_MS = (1 << 6),
    kLIB_SW_TIMER_EVT_10_7_MS = (1 << 7),
    kLIB_SW_TIMER_EVT_10_8_MS = (1 << 8),
    kLIB_SW_TIMER_EVT_10_9_MS = (1 << 9),
    kLIB_SW_TIMER_EVT_1_MS    = (1 << 10),
    kLIB_SW_TIMER_EVT_100_MS  = (1 << 11),
    kLIB_SW_TIMER_EVT_500_MS  = (1 << 12),
    kLIB_SW_TIMER_EVT_1_S     = (1 << 13),
    kLIB_SW_TIMER_EVT_HW_1MS  = (1 << 14),
    kLIB_SW_TIMER_EVT_HW_5MS  = (1 << 15)
} tLibSwTimerEvt;

/* Public macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
uint16_t LibGetSwTimer(void);
void LibSwTimerClearCount(void);
tErrCode LibSwTimerOpen(tLibSwTimerEvtHandler handler, __far void *dest);
tErrCode LibSwTimerClose(tLibSwTimerEvtHandler handler, __far void *dest);
void LibSwTimerHwHandler(tLibSwTimerEvt evt, __far void *data);
void LibSWTimerHandler(void);
void LibSwTimerHwDelay(uint16_t ms);

tErrCode LibSwTimerTaskOpen(tLibSwTimerEvtHandler handler, __far void *dest);
tErrCode LibSwTimerTaskClose(tLibSwTimerEvtHandler handler, __far void *dest);

#define LibSoftwareTimerHandlerOpen     LibSwTimerOpen
#define LibSoftwareTimerHandlerClose    LibSwTimerClose
#define LibSwTimerHardwareTimerCounter  LibSwTimerHwHandler

#ifdef __cplusplus
}
#endif

#endif /* _LIB_SW_TIMER_H */
