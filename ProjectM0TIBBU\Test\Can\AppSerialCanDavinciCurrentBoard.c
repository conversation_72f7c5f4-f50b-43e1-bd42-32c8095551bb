/**
  ******************************************************************************
  * @file        AppSerialCanDavinciCurrentBoard.c
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2025/03/07
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2025 Chris</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "HalCan.h"
#include "AppSerialCanDavinci.h"
//#include "AppGauge.h"
//#include "AppBms.h"
#include "HalAfe.h"
#include "Define.h"
//#include "ApiSysPar.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/

//=========================================================================

static void davinciCanCbCurrent(tHalCanFrame *pCanPkg)
{		
	tUnion32Bits mPcurrent, mNcurrent;
	
	
	if (apiSysParGetSystemActiveFlag() & SYS_ACTIVE_FLAG_CURRENT_BY_EMS)
	{
		return;
	}
	
	mPcurrent.i32 = (int32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
	mNcurrent.i32 = (int32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[4]);
	
	HalAfeSetCurrentValue(P_CURRENT, mPcurrent.i32);
	HalAfeSetCurrentValue(N_CURRENT, mNcurrent.i32);
	appBmsSetScuCurrent(appProjectGetScuId(), mPcurrent.i32, mNcurrent.i32);
	
	#if 0
	char	str[100];
	sprintf(str, "P = %d, N = %d", mPcurrent.i32, mNcurrent.i32);
	appSerialCanDavinciSendTextMessage(str);
	#endif
}

//=========================================================================

static void davinciCanCbCurrentCc(tHalCanFrame *pCanPkg)
{			
	
}

//=========================================================================

static void davinciCanCbVoltage(tHalCanFrame *pCanPkg)
{			
	tUnion32Bits mVrack;
	uint32_t u32VbInt, u32VbExt;
	
	
	mVrack.u32 = (uint32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
	
	HalAfeSetVBatVoltage(kAFE_VPACK_INDEX, mVrack.u32);
	
	appBmsGetScuVbat(appProjectGetScuId(), &u32VbInt, &u32VbExt);
	appBmsSetScuVbat(appProjectGetScuId(), u32VbInt, mVrack.u32);
}

//=========================================================================

static void davinciCanCbIr(tHalCanFrame *pCanPkg)
{			
	tUnion32Bits mIrRp, mIrRn;
	
	
	mIrRp.u32 = (uint32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
	mIrRn.u32 = (uint32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[4]);
	appProjcetSetIrValue(mIrRp.u32, mIrRn.u32);
	
	#if 0
	char	str[100];
	sprintf(str, "Rp = %d, Rn = %d", mIrRp.sl, mIrRp.sl);
	appSerialCanDavinciSendTextMessage(str);
	#endif
}

//=========================================================================

SMP_CAN_DECODE_CMD_START(mDavinciCanCbTab)								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CB_TX, 0,
									SMP_CB_CURRENT_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanCbCurrent)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CB_TX, 0,
									SMP_CB_CURRENT_CC_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanCbCurrentCc)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CB_TX, 0,
									SMP_CB_VOLTAGE_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanCbVoltage)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CB_TX, 0,
									SMP_CB_IR_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanCbIr)	
SMP_CAN_DECODE_CMD_END();


/* Public function prototypes -----------------------------------------------*/
void DavinciCanFunCb(tHalCanFrame *pCanPkg)
{
	uint8_t u8Idx;
	
	
	for(u8Idx = 0; mDavinciCanCbTab[u8Idx].fun != 0; u8Idx++)
	{
		if((mDavinciCanCbTab[u8Idx].canid & mDavinciCanCbTab[u8Idx].mask) == 
		   (mDavinciCanCbTab[u8Idx].mask & pCanPkg -> u32Id))
		{
			mDavinciCanCbTab[u8Idx].fun(pCanPkg);
		
			break; 		
 		}
	}
}

/************************ (C) COPYRIGHT Chris Tsai *****END OF FILE****/

