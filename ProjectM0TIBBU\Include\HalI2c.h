/*
******************************************************************************
* @file     HalI2c.h
* <AUTHOR>
* @brief    This file include MSPM0G3519 I2c Hardwarre Abstraction Layer Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HALI2C_H__
#define __HALI2C_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "LibI2cFifo.h"
#include "LibFunctionReturnValueDefine.h"
/* Global define ------------------------------------------------------------*/
#define HAL_I2C0_INST_IRQHandler                I2C0_IRQHandler
#define HAL_I2C1_INST_IRQHandler                I2C1_IRQHandler
#define HAL_I2C2_INST_IRQHandler                I2C2_IRQHandler
#define HAL_I2C0_INST_INT_IRQN                  I2C0_INT_IRQn
#define HAL_I2C1_INST_INT_IRQN                  I2C1_INT_IRQn
#define HAL_I2C2_INST_INT_IRQN                  I2C2_INT_IRQn

#define I2C_TX_MAX_PACKET_SIZE (16)
#define I2C_RX_MAX_PACKET_SIZE (16)
/* Global typedef -----------------------------------------------------------*/
typedef enum
{
	kHAL_I2C0 = 0,										
	kHAL_I2C1,										
	kHAL_I2C2
}eTypeHalI2cChannel;

typedef enum
{
	kHAL_I2C_DATA_READY = 0,													/* I2C data has been received */
	kHAL_I2C_BUFFER_FULL,       												/* An error in the FIFO buffer full. The FIFO error code is stored in eTypeHalI2CEvt. */
	kHAL_I2C_COMMUNICATION_ERR,													/* I2C has occurred during reception */
	kHAL_I2C_TX_EMPTY,															/* I2C has complete transmission of all variable data */ 
	kHAL_I2C_TX_READY_TO_SEND           										/* I2C data TX for RS485 directiion use.*/
}eTypeHalI2cEvt;

typedef enum {
    kHAL_I2C_STATUS_IDLE = 0,
    kHAL_I2C_STATUS_TX_STARTED,
    kHAL_I2C_STATUS_TX_INPROGRESS,
    kHAL_I2C_STATUS_TX_COMPLETE,
    kHAL_I2C_STATUS_RX_STARTED,
    kHAL_I2C_STATUS_RX_INPROGRESS,
	kHAL_I2C_STATUS_RX_DMA_INPROGRESS,
    kHAL_I2C_STATUS_RX_COMPLETE,
    kHAL_I2C_STATUS_ERROR
} eTypeHalI2cMasterEvent;

typedef void(*tfpHalI2cEvent)(eTypeHalI2cEvt eI2cEvt);
typedef void(*tfpHalI2cMasterEvent)(eTypeHalI2cMasterEvent eI2cMasterEvt, uint8_t *u8RxData);

typedef enum{
	kHAL_I2C_FREQUENCY_100k = 0,
	kHAL_I2C_FREQUENCY_400k
}eTypeHalI2CFrequency;

typedef struct
{
	uint8_t			*pu8HalI2cRxFifo;		
	uint16_t		u16HalI2cRxFifoSize;			
	uint8_t			*pu8HalI2cTxFifo;				
	uint16_t		u16HalI2cTxFifoSize;		
}tHalI2cFifoConfig;

typedef struct 
{
    eTypeHalI2cChannel eChannel;
	eTypeHalI2CFrequency eFreq;
    tfpHalI2cMasterEvent fpI2cEvent;	
}tHalI2cMasterConfig;

typedef struct 
{
    I2C_Regs* pmDlI2cChannel;
    uint8_t u8IntIrqn;
    tfpHalI2cMasterEvent* pfpI2cEvent;
}tHalI2cMasterChannelConfig;

typedef struct 
{
    eTypeHalI2cChannel eChannel;
    tHalI2cFifoConfig	mI2cFifoConfig;
    tfpHalI2cEvent fpI2cEvent;  
}tHalI2cConfig;

typedef struct 
{
    I2C_Regs* pmDlI2cChannel;
    uint8_t u8IntIrqn;
    tLibI2cFifoStatus* pmI2cTxFifoStatus;
    tLibI2cFifoStatus* pmI2cRxFifoStatus;
    tfpHalI2cEvent* pfpI2cEvent;
}tHalI2cChannelConfig;
typedef I2C_Regs tHalI2CHandle;

/* Global function prototypes -----------------------------------------------*/
void HalI2cGpioInit(uint32_t u32I2CSdaPincm, uint32_t u32I2CSda, uint32_t u32I2CSclPincm, uint32_t u32I2CScl);
void HalI2cMasterInit(tHalI2cMasterConfig* pmHalI2cConfig);
tFunRetunCode HalI2cMasterSend(tHalI2cMasterConfig* pmHalI2cConfig, uint8_t u8SlaveAddress, uint8_t* pu8TxBuf, uint32_t u32TxLen);
tFunRetunCode HalI2cMasterRead(tHalI2cMasterConfig* pmHalI2cConfig, uint8_t u8SlaveAddress, uint8_t* pu8RxBuf, uint32_t u32RxLen);

void HalI2cSlaveInit(tHalI2cConfig* pmHalI2cConfig, uint8_t u8SlaveAddress);
int8_t HalI2cSlaveGet(tHalI2cConfig* pmHalI2cConfig, uint8_t* pu8Data);
int8_t HalI2cSlavePut(tHalI2cConfig* pmHalI2cConfig, uint8_t u8Data);

void HalI2cMasterDmaInit(tHalI2cMasterConfig* pmHalI2cConfig);
tFunRetunCode HalI2cMasterDmaSend(tHalI2cMasterConfig* pmHalI2cConfig, uint8_t u8SlaveAddress, uint8_t* pu8TxBuf, uint32_t u32TxLen);
tFunRetunCode HalI2cMasterDmaRead(tHalI2cMasterConfig* pmHalI2cConfig, uint8_t u8SlaveAddress, uint8_t* pu8RxBuf, uint32_t u32RxLen);


#ifdef __cplusplus
}
#endif

#endif