/**
  ******************************************************************************
  * @file        AppSerialCanDavinciFirmwareUpgrade.c
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2021/11/23
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

#include "HalAfe.h"

#include "HalRtc.h"
#include "HalCan.h"

#include "LibSoftwareTimerHandler.h"
#include "AppSerialCanDavinci.h"
//#include "AppBms.h"
//#include "ApiFu.h"
//#include "ApiCanbusPkgFu.h"

void appSerialCanDavinciSendTextMessage(char *str);
#define	canFuDebugMsg(str)	appSerialCanDavinciSendTextMessage(str)

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/

//static	uint16_t	rcv_pkg = 0;
void FuCallbackFunction(uint16_t evt, uint8_t *pMsgBuf)
{
	tHalCanFrame	CanPkg;
#if MAO_DISSABLE
	switch(evt)
	{
	case API_FU_EVT_PROGRESS:
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_FU_TX, appProjectGetScuId(),
									SMP_FU_INFO_OBJ_INDEX,
									SMP_FU_INFO_PROGRESS_SUB_INDEX);
		CanPkg.u8Dlc = 2;
		CanPkg.tUnionData.u8Data[0] = pMsgBuf[0];
		CanPkg.tUnionData.u8Data[1] = pMsgBuf[1];
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
		break;
	case API_FU_EVT_CHECK_RESULT:
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_FU_TX, appProjectGetScuId(),
									SMP_FU_INFO_OBJ_INDEX,
									SMP_FU_INFO_FW_CHECKING_SUB_INDEX);
		CanPkg.u8Dlc = 2;
		CanPkg.tUnionData.u8Data[0] = 0;
		CanPkg.tUnionData.u8Data[1] = pMsgBuf[0];
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
		break;
	case API_FU_EVT_START_FW_CHECK:
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_FU_TX, appProjectGetScuId(),
									SMP_FU_INFO_OBJ_INDEX,
									SMP_FU_INFO_FW_CHECKING_SUB_INDEX);
		CanPkg.u8Dlc = 3;
		CanPkg.tUnionData.u8Data[0] = 1;
		CanPkg.tUnionData.u8Data[1] = pMsgBuf[0];
		CanPkg.tUnionData.u8Data[2] = pMsgBuf[1];
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
		break;
	case API_FU_EVT_FW_CHECKING:
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_FU_TX, appProjectGetScuId(),
									SMP_FU_INFO_OBJ_INDEX,
									SMP_FU_INFO_FW_CHECKING_SUB_INDEX);
		CanPkg.u8Dlc = 3;
		CanPkg.tUnionData.u8Data[0] = 2;
		CanPkg.tUnionData.u8Data[1] = pMsgBuf[0];
		CanPkg.tUnionData.u8Data[2] = pMsgBuf[1];
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
		break;
	}
#endif
//	canFuDebugMsg("FuCallbackFunction");
}


static uint8_t isValidScuId(uint8_t scuid)
{
	if(scuid == 0 || scuid == appProjectGetScuId())
		return 1;
	else
		return 0;
}
			
			
void DavinciCanFunFuRx(tHalCanFrame *pCanPkg)
{
	uint8_t		objindex;
	uint8_t		scuid;
//	uint32_t	address;
	
	
	scuid = SMP_CAN_GET_SCU_ID(pCanPkg -> u32Id);
	
	if (scuid >= 17)
	{
		appSerialCanDavinciPutPkgToCanFifo(pCanPkg);
		return;
	}
	
	if(isValidScuId(scuid) == 0)
		return;
		
	objindex = SMP_CAN_GET_OBJ_INDEX(pCanPkg -> u32Id);
	
	if(objindex == SMP_FU_INFO_OBJ_INDEX)
	{
		#if MAO_DISSABLE
		apiCanbusPkgFuDecodeInfoPackage(pCanPkg, FuCallbackFunction);
		#endif
	}
	else if(objindex >= SMP_FU_DATA_START_OBJ_INDEX &&
			objindex <= SMP_FU_DATA_END_OBJ_INDEX)
	{
		#if MAO_DISSABLE
		apiCanbusPkgFuDecodeDataPackage(pCanPkg);
		#endif
	}
}


/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    

