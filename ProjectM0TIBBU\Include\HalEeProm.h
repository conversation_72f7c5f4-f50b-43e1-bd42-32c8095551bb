/*
******************************************************************************
* @file     HalCodeFlash.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_CODEFLASH_H__
#define	__HAL_CODEFLASH_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "Define.h"
#include "LibFunctionReturnValueDefine.h"

/* Global define ------------------------------------------------------------*/
#define HAL_EEPROM_PROGRAM_DATA_ALIGNMENT_BYTE_CNT (8)
/* Global typedef -----------------------------------------------------------*/
typedef struct{
	uint32_t	u32StartAddress;
	uint32_t	u32EndAddress;
	uint16_t	u16Length;
	uint8_t		*pu8DataBuffer;
}tHalEeProm;

typedef enum {
    kHAL_FLASH_STATUS_DED_IDLE = 0,
    kHAL_FLASH_STATUS_ERROR
} eTypeHalFlashEvent;

typedef void(*tfpHalFlashEvent)(eTypeHalFlashEvent eI2cEvt);

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void HalEePromTestProgram(void);

tFunRetunCode HalEePromWrite(tHalEeProm *pmEeProm);
tFunRetunCode HalEePromErase(tHalEeProm *pmEeProm);
tFunRetunCode HalEePromRead(tHalEeProm *pmEeProm);

void HalFlashCallback(tfpHalFlashEvent cb);

void HalEePromExample(void);

#ifdef __cplusplus
}
#endif

#endif