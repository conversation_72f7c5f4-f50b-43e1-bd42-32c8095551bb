/*
******************************************************************************
* @file     ApiProtectDotp.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "Define.h"
#include "HalAfe.h"
#include "ApiProtect.h"
#include "LibSysPar.h"

void AppSerialUartSendMessage(char *str);

/* Private define ------------------------------------------------------------*/
#define	API_PROTECT_DOTP_DEBUG_MSG(pc8Msg)		AppSerialUartSendMessage(pc8Msg)
#define	API_PROTECT_DOTP_CHECK_NUM_PER_TIME	    (20)
#define	API_PROTECT_DOTP_GET_NTC_NUMBER()		LibSysParGetNtcNumber()
#define API_PROTECT_DOTP_GET_NTC_VOLTAGE(u16Ntcs)      HalAfeGetNtcVoltage(u16Ntcs);

#define	API_PROTECT_DOTP_GET_DOTP_PAR(u8ProtectLevel, mProtectPar) 		LibSysParGetDotpProtectPar(u8ProtectLevel, mProtectPar)
#define	API_PROTECT_DOTP_GET_LEVEL_MASK(u8ProtectLevel, mProtectFlagValue)	ApiProtectGetLevelMask(u8ProtectLevel, mProtectFlagValue)
#define API_PROTECT_DOTP_IS_OT(u16NtcVoltage, u32ParValue)  ApiProtectIsOverTemperter(u16NtcVoltage, u32ParValue)
#define API_PROTECT_DOTP_IS_UT(u16NtcVoltage, u32ParValue)  ApiProtectIsUnderTemperter(u16NtcVoltage, u32ParValue)


/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t	u8Flag[MAX_NTC_NUMBER];
	uint8_t	u8SetCount[API_PROTECT_LEVEL][MAX_NTC_NUMBER];
	uint8_t	u8ReleaseCount[API_PROTECT_LEVEL][MAX_NTC_NUMBER];
	tfpApiProtectEvtHandler  fpEvtHandler;
}tDotpProtect;

static tDotpProtect	gmDotpProtect = {0};
static uint16_t	gu16DotpNtcIndex = 0;
static bool bDotpEnable = 0;
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static void ApiProtectDotpProtectIni(void)
{
	gu16DotpNtcIndex = 0;	
}
/* Private function prototypes -----------------------------------------------*/

/* Public function prototypes -----------------------------------------------*/
uint8_t	ApiProtectDotpGetFlag(uint16_t u16NtcIndex)
{
	return gmDotpProtect.u8Flag[u16NtcIndex];
}


uint8_t ApiProtectDotpHandler(uint8_t u8ProtectLevel)
{
	uint8_t	u8Checkcount = 0;
	uint16_t	u16NtcVoltage;
	tProtectFlagValue	mProtectFlagValue;
	tScuProtectPar		mProtectPar;
	
	if (bDotpEnable == 0)
	{
		return 1;
	}
	
	API_PROTECT_DOTP_GET_DOTP_PAR(u8ProtectLevel, &mProtectPar);
	API_PROTECT_DOTP_GET_LEVEL_MASK(u8ProtectLevel, &mProtectFlagValue);

	while(1)
	{			
		u16NtcVoltage = API_PROTECT_DOTP_GET_NTC_VOLTAGE(gu16DotpNtcIndex);
		if((mProtectPar.mSTime.u32Value != 0) &&
		   (API_PROTECT_DOTP_IS_OT(u16NtcVoltage, mProtectPar.mSetValue.u32Value) != 0))
		{
			if((gmDotpProtect.u8Flag[gu16DotpNtcIndex] & mProtectFlagValue.u8Mask) == 0)
			{
				gmDotpProtect.u8Flag[gu16DotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
				gmDotpProtect.u8Flag[gu16DotpNtcIndex] |= mProtectFlagValue.u8Setting;
				gmDotpProtect.u8SetCount[u8ProtectLevel][gu16DotpNtcIndex] = 1;
			}	
			else if((gmDotpProtect.u8Flag[gu16DotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
			{
				gmDotpProtect.u8SetCount[u8ProtectLevel][gu16DotpNtcIndex]++;
				if(gmDotpProtect.u8SetCount[u8ProtectLevel][gu16DotpNtcIndex] >= mProtectPar.mSTime.u32Value)
				{
					gmDotpProtect.u8Flag[gu16DotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
					gmDotpProtect.u8Flag[gu16DotpNtcIndex] |= mProtectFlagValue.u8Setted;
					gmDotpProtect.u8SetCount[u8ProtectLevel][gu16DotpNtcIndex] = 0;

					if(gmDotpProtect.fpEvtHandler)
					{
						gmDotpProtect.fpEvtHandler(0, kAPI_PROTECT_DOTP_L1_SET + u8ProtectLevel, &gu16DotpNtcIndex);
					}
				}
			}
		}
		else if((gmDotpProtect.u8Flag[gu16DotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
		{
			gmDotpProtect.u8Flag[gu16DotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
		}
		//--------------------------------------------------------------------------
		//	Level	Release
		if((mProtectPar.mRTime.u32Value != 0) &&
		   (API_PROTECT_DOTP_IS_UT(u16NtcVoltage, mProtectPar.mRelValue.u32Value) != 0))
		{
			if((gmDotpProtect.u8Flag[gu16DotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setted)
			{
				gmDotpProtect.u8Flag[gu16DotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
				gmDotpProtect.u8Flag[gu16DotpNtcIndex] |= mProtectFlagValue.u8Releasing;
				gmDotpProtect.u8ReleaseCount[u8ProtectLevel][gu16DotpNtcIndex] = 1;
			}	
			else if((gmDotpProtect.u8Flag[gu16DotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
			{
				gmDotpProtect.u8ReleaseCount[u8ProtectLevel][gu16DotpNtcIndex]++;
				if(gmDotpProtect.u8ReleaseCount[u8ProtectLevel][gu16DotpNtcIndex] >= mProtectPar.mRTime.u32Value)
				{
					gmDotpProtect.u8Flag[gu16DotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
					gmDotpProtect.u8ReleaseCount[u8ProtectLevel][gu16DotpNtcIndex] = 0;
					if(gmDotpProtect.fpEvtHandler)
					{
						gmDotpProtect.fpEvtHandler(0, kAPI_PROTECT_DOTP_L1_RELEASE + u8ProtectLevel, &gu16DotpNtcIndex);
					}
				}
			}
		}
		else if((gmDotpProtect.u8Flag[gu16DotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
		{
			gmDotpProtect.u8Flag[gu16DotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
			gmDotpProtect.u8Flag[gu16DotpNtcIndex] |= mProtectFlagValue.u8Setted;
		}
		gu16DotpNtcIndex++;
		if(gu16DotpNtcIndex >= API_PROTECT_DOTP_GET_NTC_NUMBER())
		{
			gu16DotpNtcIndex = 0;
			return 1;
		}
		u8Checkcount++;
		if(u8Checkcount >= API_PROTECT_DOTP_CHECK_NUM_PER_TIME)
			break;
	}

	return 0;
}

void ApiProtectDotpOpen(tfpApiProtectEvtHandler fpEvtHandler)
{
	ApiProtectDotpProtectIni();
	
	bDotpEnable = 1;
	
	gmDotpProtect.fpEvtHandler = fpEvtHandler;
}

/************************ (C) COPYRIGHT *****END OF FILE****/    

