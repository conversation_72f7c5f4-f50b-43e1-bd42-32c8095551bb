/*
******************************************************************************
* @file     HalGeneralTimer.h
* <AUTHOR>
* @brief    This file include MSPM0G Timer Hardwarre Abstraction Layer Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_GENERALTIMER_H__
#define __HAL_GENERALTIMER_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "LibFunctionPointerRegister.h"
#include "LibFunctionReturnValueDefine.h"
#include "HalMcuPeripheralConfig.h"

/* Global define ------------------------------------------------------------*/
/* Global typedef -----------------------------------------------------------*/
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
tFunRetunCode HalGeneralTimerLowPowerOpenAndStart(uint16_t u16PeriodCntBaseOn1MHz, tLibFuncPtrRegEvtHandler evtHandler);
tFunRetunCode HalGeneralTimerLowPowerStop(void);
tFunRetunCode HalGeneralTimerLowPowerStart(void);
tFunRetunCode HalGeneralTimerLowPowerClose(void);

tFunRetunCode HalGeneralTimerHighPowerOpenAndStart(uint16_t u16PeriodCntBaseOn1MHz, tLibFuncPtrRegEvtHandler mEvtHandler);
tFunRetunCode HalGeneralTimerHighPowerStop(void);
tFunRetunCode HalGeneralTimerHighPowerStart(void);
tFunRetunCode HalGeneralTimerHighPowerClose(void);

#ifdef __cplusplus
}
#endif

#endif