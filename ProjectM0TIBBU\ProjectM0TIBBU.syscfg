/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G351X" --part "Default" --package "LQFP-100(PZ)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3519" --package "LQFP-100(PZ)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.23.0+4000"}
 */
scripting.excludeFromBuild("device_linker.cmd");
scripting.excludeFromBuild("device.opt");
scripting.excludeFromBuild("device.cmd.genlibs");
scripting.excludeFromBuild("ti_msp_dl_config.c");
scripting.excludeFromBuild("ti_msp_dl_config.h");

/**
 * Import the modules used in this configuration.
 */
const NONMAIN       = scripting.addModule("/ti/driverlib/NONMAIN");
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
NONMAIN.nonmainWarning = true;
NONMAIN.cscExists      = true;
NONMAIN.flashBankSwap  = true;


const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

ProjectConfig.deviceSpin = "MSPM0G3519";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
SYSCTL.peripheral.$suggestSolution         = "SYSCTL";
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
