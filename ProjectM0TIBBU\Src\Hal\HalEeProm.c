/*
******************************************************************************
* @file     HalCodeFlah.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "HalEeProm.h"

/* Local typedef ------------------------------------------------------------*/
typedef enum
{
    kHAL_FLASH_ECC_CORRECTED = 0,
    kHAL_FLASH_ECC_NONCORRECTED,
    kHAL_FLASH_ECC_UNSUPPORT
}eTypeHalFlashAddressCategory;

/* Local define -------------------------------------------------------------*/
#ifdef __MSPM0G3507__

#define HAL_EEPROM_CODE_FLASH_ECC_START_ADDR       (0x00000000)
#define HAL_EEPROM_CODE_FLASH_ECC_END_ADDR         (0x0001FFF8)

#define HAL_EEPROM_CODE_FLASH_NON_ECC_START_ADDR   (0x00400000)
#define HAL_EEPROM_CODE_FLASH_NON_ECC_END_ADDR     (0x0041FFF8)

#elif __MSPM0G3519__

#define HAL_CODE_FLASH_ECC_BANK0_START_ADDR         (0x00000000U)
#define HAL_CODE_FLASH_ECC_BANK0_END_ADDR           (0x0003FFFFU)

#define HAL_CODE_FLASH_ECC_BANK1_START_ADDR         (0x00040000U)
#define HAL_CODE_FLASH_ECC_BANK1_END_ADDR           (0x0007FFFFU)

#define HAL_CODE_FLASH_NON_ECC_BANK0_START_ADDR     (0x00400000U)
#define HAL_CODE_FLASH_NON_ECC_BANK0_END_ADDR       (0x0043FFFFU)

#define HAL_CODE_FLASH_NON_ECC_BANK1_START_ADDR     (0x00440000U)
#define HAL_CODE_FLASH_NON_ECC_BANK1_END_ADDR       (0x0047FFFFU)

#define HAL_CODE_FLASH_ECC_CODE_BANK0_START_ADDR    (0x00800000U)
#define HAL_CODE_FLASH_ECC_CODE_BANK0_END_ADDR      (0x0083FFFFU)

#define HAL_CODE_FLASH_ECC_CODE_BANK1_START_ADDR    (0x00840000U)
#define HAL_CODE_FLASH_ECC_CODE_BANK1_END_ADDR      (0x0087FFFFU)

#define HAL_DATA_FLASH_ECC_START_ADDR               (0x41D00000U)
#define HAL_DATA_FLASH_ECC_END_ADDR                 (0x41D03FFFU)
#define HAL_DATA_FLASH_NON_ECC_START_ADDR           (0x41E00000U)
#define HAL_DATA_FLASH_NON_ECC_END_ADDR             (0x41E03FFFU)
#define HAL_DATA_FLASH_ECC_CODE_START_ADDR          (0x41F00000U)
#define HAL_DATA_FLASH_ECC_CODE_END_ADDR            (0x41F03FFFU)
#endif

#define HAL_FLASH_ALIGNMENT_CHECK (0x07)
#define HAL_FLASH_PROGRAM_WORD_BYTE_CNT (8U)
#define HAL_FLASH_SECTOR_SHIFT (10) // Refer to DL_FLASHCTL_SECTOR_SIZE 
#define HAL_FLASH_SECTOR_ADDR_MASK (0xFFFFFC00)

/* Local macro --------------------------------------------------------------*/
tfpHalFlashEvent gfpHalFlashEvent = NULL;
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
static DL_FLASHCTL_FAIL_TYPE gFailType; 
/* Local function prototypes ------------------------------------------------*/
static eTypeHalFlashAddressCategory GetFlashCategory(uint32_t u32Addr)
{
    if ((u32Addr >= HAL_CODE_FLASH_ECC_BANK0_START_ADDR) && (u32Addr <= HAL_CODE_FLASH_ECC_BANK0_END_ADDR))
    {
        return kHAL_FLASH_ECC_CORRECTED;
    }
    else if ((u32Addr >= HAL_CODE_FLASH_ECC_BANK1_START_ADDR) && (u32Addr <= HAL_CODE_FLASH_ECC_BANK1_END_ADDR))
    {
        return kHAL_FLASH_ECC_CORRECTED;
    }
    else if ((u32Addr >= HAL_DATA_FLASH_ECC_START_ADDR) && (u32Addr <= HAL_DATA_FLASH_ECC_END_ADDR))
    {
        return kHAL_FLASH_ECC_CORRECTED;
    }
    else if ((u32Addr >= HAL_CODE_FLASH_NON_ECC_BANK0_START_ADDR) && (u32Addr <= HAL_CODE_FLASH_NON_ECC_BANK0_END_ADDR))
    {
        return kHAL_FLASH_ECC_NONCORRECTED;
    }
    else if ((u32Addr >= HAL_CODE_FLASH_NON_ECC_BANK1_START_ADDR) && (u32Addr <= HAL_CODE_FLASH_NON_ECC_BANK1_END_ADDR))
    {
        return kHAL_FLASH_ECC_NONCORRECTED;
    }
    else if ((u32Addr >= HAL_DATA_FLASH_NON_ECC_START_ADDR) && (u32Addr <= HAL_DATA_FLASH_NON_ECC_END_ADDR))
    {
        return kHAL_FLASH_ECC_NONCORRECTED;
    }
    return kHAL_FLASH_ECC_UNSUPPORT;
}

static bool IsParmValid(tHalEeProm *pmEeProm)
{
    /* Check Pointer */
    if (pmEeProm == 0 || pmEeProm->pu8DataBuffer == 0)
    {
        return false;
    }

    /* Check Length */
    if (pmEeProm->u16Length == 0)
    {
        return false;
    }

    /* Check Data Alignment */
    if ((pmEeProm->u32StartAddress & HAL_FLASH_ALIGNMENT_CHECK) != 0)
    {
        return false;
    }

    /* If necessary, Check address range.*/

    return true;
}
static bool IsEraseParmValid(tHalEeProm *pmEeProm)
{
    /* Check Pointer */
    if (pmEeProm == 0)
    {
        return false;
    }

    /* Check Length */
    if (pmEeProm->u16Length == 0)
    {
        return false;
    }

    /* Check Data Alignment */
    if ((pmEeProm->u32StartAddress & HAL_FLASH_ALIGNMENT_CHECK) != 0)
    {
        return false;
    }
    return true;
}

/* Global function prototypes -----------------------------------------------*/
tFunRetunCode HalEePromWrite(tHalEeProm *pmEeProm)
{
    if (IsParmValid(pmEeProm) == false)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    
    uint32_t u32ProgramAddr = pmEeProm->u32StartAddress;
    uint16_t u16DataIndex = 0;
    tUnionU64Bits mUnion64Program = {0};
    volatile DL_FLASHCTL_COMMAND_STATUS eFlashCmdStatus; 

    while (u16DataIndex < pmEeProm->u16Length)
    {
        DL_FlashCTL_executeClearStatus(FLASHCTL);
        DL_FlashCTL_unprotectSector(FLASHCTL, u32ProgramAddr, DL_FLASHCTL_REGION_SELECT_MAIN);
        
        for(uint8_t u8ByteId = 0; u8ByteId < HAL_FLASH_PROGRAM_WORD_BYTE_CNT; u8ByteId++)
        {
            if (u16DataIndex < pmEeProm->u16Length)
            {
                mUnion64Program.u8[u8ByteId] = pmEeProm->pu8DataBuffer[u16DataIndex];
                u16DataIndex++;
            }
            else
            {
                mUnion64Program.u8[u8ByteId] = 0xFF;
            }
        }

        switch (GetFlashCategory(u32ProgramAddr))
        {
            case kHAL_FLASH_ECC_CORRECTED:
                eFlashCmdStatus = DL_FlashCTL_programMemoryFromRAM64WithECCGenerated(FLASHCTL, u32ProgramAddr, &mUnion64Program.u32[0]);
                break;
            case kHAL_FLASH_ECC_NONCORRECTED:
                eFlashCmdStatus = DL_FlashCTL_programMemoryFromRAM64(FLASHCTL, u32ProgramAddr, &mUnion64Program.u32[0]);
                break;
            default:
                return RES_ERROR_FAIL;
                break;
        }
        if(eFlashCmdStatus != DL_FLASHCTL_COMMAND_STATUS_PASSED)
        {
          gFailType = DL_FlashCTL_getFailureStatus(FLASHCTL);
          return RES_ERROR_FAIL;
        }
        u32ProgramAddr += HAL_FLASH_PROGRAM_WORD_BYTE_CNT;
    }
    return RES_SUCCESS;
}

tFunRetunCode HalEePromErase(tHalEeProm *pmEeProm)
{
    if(IsEraseParmValid(pmEeProm) == false)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    volatile DL_FLASHCTL_COMMAND_STATUS eFlashCmdStatus; 
    uint32_t u32StartSectorAddr = pmEeProm->u32StartAddress & HAL_FLASH_SECTOR_ADDR_MASK;
    uint32_t u32EndSectorAddr = (pmEeProm->u32StartAddress + pmEeProm->u16Length - 1) & HAL_FLASH_SECTOR_ADDR_MASK;
    uint32_t u32EraseAddr = u32StartSectorAddr;

    while (u32EraseAddr <= u32EndSectorAddr)
    {
        DL_FlashCTL_executeClearStatus(FLASHCTL);
        DL_FlashCTL_unprotectSector(FLASHCTL, u32EraseAddr, DL_FLASHCTL_REGION_SELECT_MAIN);
        eFlashCmdStatus = DL_FlashCTL_eraseMemoryFromRAM(FLASHCTL, u32EraseAddr, DL_FLASHCTL_COMMAND_SIZE_SECTOR);
        if (eFlashCmdStatus != DL_FLASHCTL_COMMAND_STATUS_PASSED) 
        {
            return RES_ERROR_FAIL;
        }
        u32EraseAddr += DL_FLASHCTL_SECTOR_SIZE;
    }
    return RES_SUCCESS;
}

tFunRetunCode HalEePromRead(tHalEeProm *pmEeProm)
{
    if (IsParmValid(pmEeProm) == false)
    {
        return RES_ERROR_INVALID_PARAM;
    }

    uint16_t u16DataIndex = 0;
    uint32_t u32ReadAddr = pmEeProm->u32StartAddress;
    tUnionU64Bits mUnion64Read = {0};

    while (u16DataIndex < pmEeProm->u16Length)
    {

        mUnion64Read.u64 = *((volatile uint64_t *)u32ReadAddr); 
        for(uint8_t u8ByteId = 0; u8ByteId < HAL_FLASH_PROGRAM_WORD_BYTE_CNT; u8ByteId++)
        {
            if (u16DataIndex < pmEeProm->u16Length)
            {
                pmEeProm->pu8DataBuffer[u16DataIndex] = mUnion64Read.u8[u8ByteId];
                u16DataIndex++;
            }
            else 
            {
                break;
            } 
        }
        u32ReadAddr += HAL_FLASH_PROGRAM_WORD_BYTE_CNT;
    }
    return RES_SUCCESS;
}

void HalFlashCallback(tfpHalFlashEvent cb)
{
    gfpHalFlashEvent = cb;
}

void NMI_Handler(void)
{
    switch (DL_SYSCTL_getPendingNonMaskableInterrupt()) {
        case DL_SYSCTL_NMI_IIDX_FLASH_DED:
            DL_SYSCTL_clearECCErrorStatus();
            if (gfpHalFlashEvent != NULL) {
                gfpHalFlashEvent(kHAL_FLASH_STATUS_ERROR);
            }
            break;
        default:
            break;
    }
}

#define HAL_EEPROM_TEST_ARR_LEN (64)
#define HAL_EEPROM_TEST_PROGRAM_ADDR (0x41D00000)
static tHalEeProm gmHalEePromTestWrite = {0};
static uint8_t gu8ArrWriteData[HAL_EEPROM_TEST_ARR_LEN] = {0};
static tHalEeProm gmHalEePromTestRead = {0};
static uint8_t gu8ArrReadData[HAL_EEPROM_TEST_ARR_LEN] = {0};

void HalEePromExample(void)
{
    for (int i = 0; i < HAL_EEPROM_TEST_ARR_LEN; i++){
        gu8ArrWriteData[i] = i;
    }
    
    gmHalEePromTestWrite.u32StartAddress = HAL_EEPROM_TEST_PROGRAM_ADDR;
    gmHalEePromTestWrite.u16Length = HAL_EEPROM_TEST_ARR_LEN;
    gmHalEePromTestWrite.pu8DataBuffer = gu8ArrWriteData;

    HalEePromErase(&gmHalEePromTestWrite);
    delay_cycles(40000000);
    HalEePromWrite(&gmHalEePromTestWrite);
    delay_cycles(40000000);
    
    gmHalEePromTestRead.u32StartAddress = HAL_EEPROM_TEST_PROGRAM_ADDR;
    gmHalEePromTestRead.u16Length = HAL_EEPROM_TEST_ARR_LEN;
    gmHalEePromTestRead.pu8DataBuffer = gu8ArrReadData;
    HalEePromRead(&gmHalEePromTestRead);
    delay_cycles(40000000);
}

