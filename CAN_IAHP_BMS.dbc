VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: BMS HOST


BO_ 2147483904 BMS_PackInfo1: 8 BMS
 SG_ BMS_Vpack : 0|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_PackVoltage : 32|32@1+ (1,0) [0|4294967295] "mV"  HOST

BO_ 2147483905 BMS_PackInfo2: 8 BMS
 SG_ BMS_PackCurrent : 0|32@1- (1,0) [-2147483648|2147483647] "mA"  HOST
 SG_ BMS_AvgCurrent : 32|32@1- (1,0) [-2147483648|2147483647] "mA"  HOST

BO_ 2147483906 BMS_PackInfo3: 8 BMS
 SG_ BMS_RSOC : 0|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_ASOC : 16|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_RC : 32|32@1+ (1,0) [0|4294967295] "mAh"  HOST

BO_ 2147483907 BMS_PackInfo4: 8 BMS
 SG_ BMS_FCC : 0|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_CycleCount : 32|16@1+ (1,0) [0|65535] "Times"  HOST
 SG_ BMS_LearnCycle : 48|16@1+ (1,0) [0|65535] "Times"  HOST

BO_ 2147483908 BMS_PackInfo5: 8 BMS
 SG_ BMS_UserRC : 0|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_DCR : 32|32@1+ (1,0) [0|4294967295] "mAh"  HOST

BO_ 2147483909 BMS_PackInfo6: 8 BMS
 SG_ BMS_FDCR : 0|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_UserRSOC : 32|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_FCCmin : 48|16@1+ (1,0) [0|65535] "100mWh"  HOST

BO_ 2147483910 BMS_PackInfo7: 8 BMS
 SG_ BMS_DeltaRC : 0|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_SrartRSOC : 32|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_StartFDCR : 48|16@1+ (1,0) [0|65535] "mAh"  HOST

BO_ 2147483911 BMS_PackInfo8: 8 BMS
 SG_ BMS_RCminCutoff : 0|32@1+ (1,0) [0|4294967295] "mAh"  HOST

BO_ 2147483912 BMS_Status1: 8 BMS
 SG_ BMS_BatteryStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_BatteryStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PackStatusLow : 32|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PackStatusHigh : 48|16@1+ (1,0) [0|65535] "Hex"  HOST

BO_ 2147483913 BMS_Status2: 8 BMS
 SG_ BMS_SafetyStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_SafetyStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_WarnStatus : 32|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_STStatus : 48|16@1+ (1,0) [0|65535] "Hex"  HOST

BO_ 2147483914 BMS_Status3: 8 BMS
 SG_ BMS_PFStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PFStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_CBS0_15 : 32|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_StartRSOCmin : 48|16@1+ (0.01,0) [0|655.35] "%"  HOST

BO_ 2147483915 BMS_Status4: 8 BMS
 SG_ BMS_UsageCapacity : 0|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_SuccChaCap : 32|32@1+ (1,0) [0|4294967295] "mAh"  HOST

BO_ 2147483916 BMS_Status5: 8 BMS
 SG_ BMS_SystemTime : 0|32@1+ (1,0) [0|4294967295] "DateTime"  HOST
 SG_ BMS_EngMode : 32|8@1+ (1,0) [0|255] "Hex"  HOST

BO_ 2147483917 BMS_Temp01_04: 8 BMS
 SG_ BMS_Temp1 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp2 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp3 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp4 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483918 BMS_Temp05_08: 8 BMS
 SG_ BMS_Temp5 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp6 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp7 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp8 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483919 BMS_Temp09_12: 8 BMS
 SG_ BMS_Temp9 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp10 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp11 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp12 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483920 BMS_Temp13_16: 8 BMS
 SG_ BMS_Temp13 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp14 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp15 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp16 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483921 BMS_Temp17_20: 8 BMS
 SG_ BMS_Temp17 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp18 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp19 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp20 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483922 BMS_Temp21_24: 8 BMS
 SG_ BMS_Temp21 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp22 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp23 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp24 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483923 BMS_Temp25_28: 8 BMS
 SG_ BMS_Temp25 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp26 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp27 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp28 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483924 BMS_Temp29_32: 8 BMS
 SG_ BMS_Temp29 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp30 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp31 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp32 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483925 BMS_Temp33_36: 8 BMS
 SG_ BMS_Temp33 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp34 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp35 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp36 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483926 BMS_Temp37_40: 8 BMS
 SG_ BMS_Temp37 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp38 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp39 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp40 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483927 BMS_Temp41_44: 8 BMS
 SG_ BMS_Temp41 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp42 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp43 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp44 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483928 BMS_Temp45_48: 8 BMS
 SG_ BMS_Temp45 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp46 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp47 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp48 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147483929 BMS_Cell01_04: 8 BMS
 SG_ BMS_CellVolt1 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt2 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt3 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt4 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483930 BMS_Cell05_08: 8 BMS
 SG_ BMS_CellVolt5 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt6 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt7 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt8 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483931 BMS_Cell09_12: 8 BMS
 SG_ BMS_CellVolt9 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt10 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt11 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt12 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483932 BMS_Cell13_16: 8 BMS
 SG_ BMS_CellVolt13 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt14 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt15 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt16 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483933 BMS_Cell17_20: 8 BMS
 SG_ BMS_CellVolt17 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt18 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt19 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt20 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483934 BMS_Cell21_24: 8 BMS
 SG_ BMS_CellVolt21 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt22 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt23 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt24 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483935 BMS_Cell25_28: 8 BMS
 SG_ BMS_CellVolt25 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt26 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt27 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt28 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483936 BMS_Cell29_32: 8 BMS
 SG_ BMS_CellVolt29 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt30 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt31 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt32 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483937 BMS_Cell33_36: 8 BMS
 SG_ BMS_CellVolt33 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt34 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt35 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt36 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483938 BMS_Cell37_40: 8 BMS
 SG_ BMS_CellVolt37 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt38 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt39 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt40 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483939 BMS_Cell41_44: 8 BMS
 SG_ BMS_CellVolt41 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt42 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt43 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt44 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483940 BMS_Cell45_48: 8 BMS
 SG_ BMS_CellVolt45 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt46 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt47 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt48 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483941 BMS_Cell49_52: 8 BMS
 SG_ BMS_CellVolt49 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt50 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt51 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt52 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483942 BMS_Cell53_56: 8 BMS
 SG_ BMS_CellVolt53 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt54 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt55 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt56 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483943 BMS_Cell57_60: 8 BMS
 SG_ BMS_CellVolt57 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt58 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt59 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt60 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483944 BMS_Cell61_64: 8 BMS
 SG_ BMS_CellVolt61 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt62 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt63 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt64 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483945 BMS_Cell65_68: 8 BMS
 SG_ BMS_CellVolt65 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt66 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt67 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt68 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483946 BMS_Cell69_72: 8 BMS
 SG_ BMS_CellVolt69 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt70 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt71 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt72 : 48|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147483947 BMS_OtherVolt1: 8 BMS
 SG_ BMS_Vdfuse : 0|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_VchgPlus : 32|32@1+ (1,0) [0|4294967295] "mV"  HOST

BO_ 2147483948 BMS_OtherVolt2: 8 BMS
 SG_ BMS_VpackPlus : 0|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_Vcfuse : 32|32@1+ (1,0) [0|4294967295] "mV"  HOST

BO_ 2147483949 BMS_Version: 8 BMS
 SG_ BMS_ChkSum : 0|32@1+ (1,0) [0|4294967295] "Hex"  HOST
 SG_ BMS_PublicVer : 32|24@1+ (1,0) [0|16777215] "Ver"  HOST



CM_ "Complete Classical CAN version of CANFD_IAHP_BMS.dbcAll CAN-FD messages have been split into 8-byte Classical CAN messagesMessage IDs range from 256 (0x100) to 301 (0x12D)Total 46 messages: 8 PackInfo + 5 Status + 8 Temp1 + 4 Temp2 + 18 CellVolt + 2 OtherVolt + 1 VersionAll value tables are properly mapped to corresponding signalsAll signal comments (CM_ SG_) have been migrated from original CAN-FD DBCSupports 72 cell voltages and 48 temperature sensors";
CM_ SG_ 2147483912 BMS_BatteryStatusLow "Battery Status Low Word";
CM_ SG_ 2147483912 BMS_BatteryStatusHigh "Battery Status High Word";
CM_ SG_ 2147483912 BMS_PackStatusLow "Pack Status Low Word";
CM_ SG_ 2147483912 BMS_PackStatusHigh "Pack Status High Word";
CM_ SG_ 2147483913 BMS_SafetyStatusLow "Safety Status Low Word";
CM_ SG_ 2147483913 BMS_SafetyStatusHigh "Safety Status High Word";
CM_ SG_ 2147483913 BMS_WarnStatus "Warning Status Word";
CM_ SG_ 2147483913 BMS_STStatus "Self-test Status";
CM_ SG_ 2147483914 BMS_PFStatusLow "PF Status Low Word";
CM_ SG_ 2147483914 BMS_CBS0_15 "Cell Balancing Status";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 0;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 5;
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 0;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 0;
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 0;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","IfActive","NoMsgSendType","NotUsed";
BA_DEF_ SG_  "GenSigStartValue" INT 0 0;
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 0;
BA_DEF_ SG_  "GenSigCycleTimeActive" INT 0 0;
BA_DEF_ SG_  "GenSigCycleTime" INT 0 0;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed";
BA_DEF_  "Baudrate" INT 0 1000000;
BA_DEF_  "BusType" STRING ;
BA_DEF_  "NmType" STRING ;
BA_DEF_  "Manufacturer" STRING ;
BA_DEF_ BO_  "TpTxIndex" INT 0 255;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "NmStationAddress" HEX 0 255;
BA_DEF_ BU_  "NmNode" ENUM  "no","yes";
BA_DEF_ BO_  "NmMessage" ENUM  "no","yes";
BA_DEF_  "NmAsrWaitBusSleepTime" INT 0 65535;
BA_DEF_  "NmAsrTimeoutTime" INT 1 65535;
BA_DEF_  "NmAsrRepeatMessageTime" INT 0 65535;
BA_DEF_ BU_  "NmAsrNodeIdentifier" HEX 0 255;
BA_DEF_ BU_  "NmAsrNode" ENUM  "no","yes";
BA_DEF_  "NmAsrMessageCount" INT 1 256;
BA_DEF_ BO_  "NmAsrMessage" ENUM  "no","yes";
BA_DEF_ BU_  "NmAsrCanMsgReducedTime" INT 1 65535;
BA_DEF_  "NmAsrCanMsgCycleTime" INT 1 65535;
BA_DEF_ BU_  "NmAsrCanMsgCycleOffset" INT 0 65535;
BA_DEF_  "NmAsrBaseAddress" HEX 0 2047;
BA_DEF_ BU_  "ILUsed" ENUM  "no","yes";
BA_DEF_  "ILTxTimeout" INT 0 65535;
BA_DEF_ SG_  "GenSigTimeoutValue" INT 0 65535;
BA_DEF_ SG_  "GenSigTimeoutTime" INT 0 65535;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "no","yes";
BA_DEF_ BO_  "GenMsgFastOnStart" INT 0 65535;
BA_DEF_ BO_  "DiagUudtResponse" ENUM  "false","true";
BA_DEF_ BO_  "DiagUudResponse" ENUM  "False","True";
BA_DEF_ BO_  "DiagState" ENUM  "no","yes";
BA_DEF_ BO_  "DiagResponse" ENUM  "no","yes";
BA_DEF_ BO_  "DiagRequest" ENUM  "no","yes";
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_  "DBName" STRING ;
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigCycleTimeActive" 0;
BA_DEF_DEF_  "GenSigCycleTime" 0;
BA_DEF_DEF_  "GenSigSendType" "Cyclic";
BA_DEF_DEF_  "Baudrate" 1000000;
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NmType" "";
BA_DEF_DEF_  "Manufacturer" "Vector";
BA_DEF_DEF_  "TpTxIndex" 0;
BA_DEF_DEF_  "NodeLayerModules" " ";
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NmNode" "no";
BA_DEF_DEF_  "NmMessage" "no";
BA_DEF_DEF_  "NmAsrWaitBusSleepTime" 1500;
BA_DEF_DEF_  "NmAsrTimeoutTime" 2000;
BA_DEF_DEF_  "NmAsrRepeatMessageTime" 3200;
BA_DEF_DEF_  "NmAsrNodeIdentifier" 50;
BA_DEF_DEF_  "NmAsrNode" "no";
BA_DEF_DEF_  "NmAsrMessageCount" 128;
BA_DEF_DEF_  "NmAsrMessage" "no";
BA_DEF_DEF_  "NmAsrCanMsgReducedTime" 320;
BA_DEF_DEF_  "NmAsrCanMsgCycleTime" 640;
BA_DEF_DEF_  "NmAsrCanMsgCycleOffset" 0;
BA_DEF_DEF_  "NmAsrBaseAddress" 1280;
BA_DEF_DEF_  "ILUsed" "no";
BA_DEF_DEF_  "ILTxTimeout" 0;
BA_DEF_DEF_  "GenSigTimeoutValue" 0;
BA_DEF_DEF_  "GenSigTimeoutTime" 0;
BA_DEF_DEF_  "GenMsgILSupport" "no";
BA_DEF_DEF_  "GenMsgFastOnStart" 0;
BA_DEF_DEF_  "DiagUudtResponse" "false";
BA_DEF_DEF_  "DiagUudResponse" "False";
BA_DEF_DEF_  "DiagState" "no";
BA_DEF_DEF_  "DiagResponse" "no";
BA_DEF_DEF_  "DiagRequest" "no";
BA_DEF_DEF_  "VFrameFormat" "";
BA_DEF_DEF_  "DBName" "";
BA_ "NmAsrWaitBusSleepTime" 2000;
BA_ "Baudrate" 1000000;
BA_ "BusType" "CAN";
BA_ "NmType" "NmAsr";
BA_ "Manufacturer" "Vector";
BA_ "DBName" "CAN_IAHP_BMS";
BA_ "VFrameFormat" BO_ 2147483904 1;
BA_ "GenMsgCycleTime" BO_ 2147483904 50;
BA_ "GenMsgCycleTime" BO_ 2147483905 50;
BA_ "VFrameFormat" BO_ 2147483905 1;
BA_ "GenMsgCycleTime" BO_ 2147483906 50;
BA_ "VFrameFormat" BO_ 2147483906 1;
BA_ "GenMsgCycleTime" BO_ 2147483907 50;
BA_ "VFrameFormat" BO_ 2147483907 1;
BA_ "GenMsgCycleTime" BO_ 2147483908 50;
BA_ "VFrameFormat" BO_ 2147483908 1;
BA_ "GenMsgCycleTime" BO_ 2147483909 50;
BA_ "VFrameFormat" BO_ 2147483909 1;
BA_ "GenMsgCycleTime" BO_ 2147483910 50;
BA_ "VFrameFormat" BO_ 2147483910 1;
BA_ "GenMsgCycleTime" BO_ 2147483911 50;
BA_ "VFrameFormat" BO_ 2147483911 1;
BA_ "GenMsgCycleTime" BO_ 2147483912 50;
BA_ "VFrameFormat" BO_ 2147483912 1;
BA_ "GenMsgCycleTime" BO_ 2147483913 50;
BA_ "VFrameFormat" BO_ 2147483913 1;
BA_ "GenMsgCycleTime" BO_ 2147483914 50;
BA_ "VFrameFormat" BO_ 2147483914 1;
BA_ "GenMsgCycleTime" BO_ 2147483915 50;
BA_ "VFrameFormat" BO_ 2147483915 1;
BA_ "GenMsgCycleTime" BO_ 2147483916 50;
BA_ "VFrameFormat" BO_ 2147483916 1;
BA_ "GenMsgCycleTime" BO_ 2147483917 50;
BA_ "VFrameFormat" BO_ 2147483917 1;
BA_ "GenMsgCycleTime" BO_ 2147483918 50;
BA_ "VFrameFormat" BO_ 2147483918 1;
BA_ "GenMsgCycleTime" BO_ 2147483919 50;
BA_ "VFrameFormat" BO_ 2147483919 1;
BA_ "GenMsgCycleTime" BO_ 2147483920 50;
BA_ "VFrameFormat" BO_ 2147483920 1;
BA_ "GenMsgCycleTime" BO_ 2147483921 50;
BA_ "VFrameFormat" BO_ 2147483921 1;
BA_ "GenMsgCycleTime" BO_ 2147483922 50;
BA_ "VFrameFormat" BO_ 2147483922 1;
BA_ "GenMsgCycleTime" BO_ 2147483923 50;
BA_ "VFrameFormat" BO_ 2147483923 1;
BA_ "GenMsgCycleTime" BO_ 2147483924 50;
BA_ "VFrameFormat" BO_ 2147483924 1;
BA_ "GenMsgCycleTime" BO_ 2147483925 50;
BA_ "VFrameFormat" BO_ 2147483925 1;
BA_ "GenMsgCycleTime" BO_ 2147483926 50;
BA_ "VFrameFormat" BO_ 2147483926 1;
BA_ "GenMsgCycleTime" BO_ 2147483927 50;
BA_ "VFrameFormat" BO_ 2147483927 1;
BA_ "GenMsgCycleTime" BO_ 2147483928 50;
BA_ "VFrameFormat" BO_ 2147483928 1;
BA_ "VFrameFormat" BO_ 2147483929 1;
BA_ "GenMsgCycleTime" BO_ 2147483929 50;
BA_ "GenMsgCycleTime" BO_ 2147483930 50;
BA_ "VFrameFormat" BO_ 2147483930 1;
BA_ "GenMsgCycleTime" BO_ 2147483931 50;
BA_ "VFrameFormat" BO_ 2147483931 1;
BA_ "GenMsgCycleTime" BO_ 2147483932 50;
BA_ "VFrameFormat" BO_ 2147483932 1;
BA_ "GenMsgCycleTime" BO_ 2147483933 50;
BA_ "VFrameFormat" BO_ 2147483933 1;
BA_ "GenMsgCycleTime" BO_ 2147483934 50;
BA_ "VFrameFormat" BO_ 2147483934 1;
BA_ "GenMsgCycleTime" BO_ 2147483935 50;
BA_ "VFrameFormat" BO_ 2147483935 1;
BA_ "GenMsgCycleTime" BO_ 2147483936 50;
BA_ "VFrameFormat" BO_ 2147483936 1;
BA_ "GenMsgCycleTime" BO_ 2147483937 50;
BA_ "VFrameFormat" BO_ 2147483937 1;
BA_ "GenMsgCycleTime" BO_ 2147483938 50;
BA_ "VFrameFormat" BO_ 2147483938 1;
BA_ "GenMsgCycleTime" BO_ 2147483939 50;
BA_ "VFrameFormat" BO_ 2147483939 1;
BA_ "GenMsgCycleTime" BO_ 2147483940 50;
BA_ "VFrameFormat" BO_ 2147483940 1;
BA_ "GenMsgCycleTime" BO_ 2147483941 50;
BA_ "VFrameFormat" BO_ 2147483941 1;
BA_ "GenMsgCycleTime" BO_ 2147483942 50;
BA_ "VFrameFormat" BO_ 2147483942 1;
BA_ "GenMsgCycleTime" BO_ 2147483943 50;
BA_ "VFrameFormat" BO_ 2147483943 1;
BA_ "GenMsgCycleTime" BO_ 2147483944 50;
BA_ "VFrameFormat" BO_ 2147483944 1;
BA_ "GenMsgCycleTime" BO_ 2147483945 50;
BA_ "VFrameFormat" BO_ 2147483945 1;
BA_ "GenMsgCycleTime" BO_ 2147483946 50;
BA_ "VFrameFormat" BO_ 2147483946 1;
BA_ "GenMsgCycleTime" BO_ 2147483947 50;
BA_ "VFrameFormat" BO_ 2147483947 1;
BA_ "GenMsgCycleTime" BO_ 2147483948 50;
BA_ "VFrameFormat" BO_ 2147483948 1;
BA_ "GenMsgCycleTime" BO_ 2147483949 50;

BA_ "GenMsgDelayTime" BO_ 2147483904 0;
BA_ "GenMsgDelayTime" BO_ 2147483905 0;
BA_ "GenMsgDelayTime" BO_ 2147483906 0;
BA_ "GenMsgDelayTime" BO_ 2147483907 0;
BA_ "GenMsgDelayTime" BO_ 2147483908 0;
BA_ "GenMsgDelayTime" BO_ 2147483909 0;
BA_ "GenMsgDelayTime" BO_ 2147483910 0;
BA_ "GenMsgDelayTime" BO_ 2147483911 0;
BA_ "GenMsgDelayTime" BO_ 2147483912 1;
BA_ "GenMsgDelayTime" BO_ 2147483913 1;
BA_ "GenMsgDelayTime" BO_ 2147483914 1;
BA_ "GenMsgDelayTime" BO_ 2147483915 1;
BA_ "GenMsgDelayTime" BO_ 2147483916 1;
BA_ "GenMsgDelayTime" BO_ 2147483917 3;
BA_ "GenMsgDelayTime" BO_ 2147483918 3;
BA_ "GenMsgDelayTime" BO_ 2147483919 3;
BA_ "GenMsgDelayTime" BO_ 2147483920 3;
BA_ "GenMsgDelayTime" BO_ 2147483921 3;
BA_ "GenMsgDelayTime" BO_ 2147483922 3;
BA_ "GenMsgDelayTime" BO_ 2147483923 3;
BA_ "GenMsgDelayTime" BO_ 2147483924 3;
BA_ "GenMsgDelayTime" BO_ 2147483925 3;
BA_ "GenMsgDelayTime" BO_ 2147483926 3;
BA_ "GenMsgDelayTime" BO_ 2147483927 3;
BA_ "GenMsgDelayTime" BO_ 2147483928 3;
BA_ "GenMsgDelayTime" BO_ 2147483929 2;
BA_ "GenMsgDelayTime" BO_ 2147483930 2;
BA_ "GenMsgDelayTime" BO_ 2147483931 2;
BA_ "GenMsgDelayTime" BO_ 2147483932 2;
BA_ "GenMsgDelayTime" BO_ 2147483933 2;
BA_ "GenMsgDelayTime" BO_ 2147483934 2;
BA_ "GenMsgDelayTime" BO_ 2147483935 2;
BA_ "GenMsgDelayTime" BO_ 2147483936 2;
BA_ "GenMsgDelayTime" BO_ 2147483937 2;
BA_ "GenMsgDelayTime" BO_ 2147483938 2;
BA_ "GenMsgDelayTime" BO_ 2147483939 2;
BA_ "GenMsgDelayTime" BO_ 2147483940 2;
BA_ "GenMsgDelayTime" BO_ 2147483941 2;
BA_ "GenMsgDelayTime" BO_ 2147483942 2;
BA_ "GenMsgDelayTime" BO_ 2147483943 2;
BA_ "GenMsgDelayTime" BO_ 2147483944 2;
BA_ "GenMsgDelayTime" BO_ 2147483945 2;
BA_ "GenMsgDelayTime" BO_ 2147483946 2;
BA_ "GenMsgDelayTime" BO_ 2147483947 4;
BA_ "GenMsgDelayTime" BO_ 2147483948 4;
BA_ "GenMsgDelayTime" BO_ 2147483949 5;
BA_ "VFrameFormat" BO_ 2147483949 1;
VAL_ 2147483912 BMS_BatteryStatusLow 15 "ST" 14 "RUN" 13 "COMM_PRESENTF" 12 "KEY_PRESENTF1" 11 "KEY_PRESENTF0" 10 "CHG_PRESENTF1" 9 "CHG_PRESENTF0" 8 "LTCF" 7 "LTUF" 6 "WARRANTY" 5 "BEF" 4 "BFF" 3 "CBF" 2 "DSMF" 1 "CHGF" 0 "ZEROF" ;
VAL_ 2147483912 BMS_BatteryStatusHigh 15 "VSTG1" 14 "VSTG0" 13 "ZERO_DSG" 12 "SC_RST" 11 "BMS_PST_F" 10 "BMS_11VCC_F" 9 "DCHG_OFF_F" 8 "WH_OCV_F" 7 "BMS_RESET_F" 6 "BMS_FAULT_F" 5 "CHG_EN_F" 4 "DCHG_EN_F" 3 "SC_RELEASE_F" 2 "CHG_MODE" 1 "BMS_PRESENT_F" 0 "Reserved" ;
VAL_ 2147483912 BMS_PackStatusLow 15 "FDM_EN" 14 "DFCP" 13 "CFCP" 12 "LVDR" 11 "PRE-DSGMS" 10 "PRE-CHGMS" 9 "DFDP" 8 "CFDP" 7 "FCCFlagUpdate" 6 "EventFlag" 5 "WDTRST" 4 "DFBS" 3 "DFAS" 2 "SEALDFB" 1 "SEALDFA" 0 "PrepUpdateFCC" ;
VAL_ 2147483912 BMS_PackStatusHigh 15 "Reserved" 14 "SOCB" 13 "SMCB" 12 "LT_OCV_prd" 11 "UNPLG_SD" 10 "DFDS" 9 "AccCHG" 8 "LT_OCV_EN" 7 "FLT_INJ" 6 "Data_UT" 5 "RST_CD" 4 "XDFCP" 3 "XCFCP" 2 "XDFDP" 1 "XCFDP" 0 "Pack_Idle" ;
VAL_ 2147483913 BMS_SafetyStatusLow 15 "SD_Fail" 14 "AFE_COMM" 13 "MOSOTUTF" 12 "Reserved" 11 "11V_OVP" 10 "Reserved" 9 "COCF" 8 "DOCF" 7 "Reserved" 6 "DCI" 5 "DUTF" 4 "DOTF" 3 "CUTF" 2 "COTF" 1 "UVPF" 0 "OVPF" ;
VAL_ 2147483913 BMS_SafetyStatusHigh 15 "FLT_INJ_PF" 14 "FDM" 13 "Reserved" 12 "AFEDOC" 11 "DOPP" 10 "DUP" 9 "Reserved" 8 "FETNG_ExtSCP" 7 "FETNG_FETOFF" 6 "FETNG_ALM1" 5 "DOC3" 4 "DOC2" 3 "IRAF" 2 "PDSGF" 1 "Reserved" 0 "HW_DOCF" ;
VAL_ 2147483913 BMS_WarnStatus 15 "Reserved" 14 "COTAF" 13 "MOSOTUTF" 12 "Reserved" 11 "Reserved" 10 "Reserved" 9 "COCF" 8 "DOCF" 7 "Reserved" 6 "Reserved" 5 "DUTF" 4 "DOTF" 3 "CUTF" 2 "COTF" 1 "UVPF" 0 "OVPF" ;
VAL_ 2147483913 BMS_STStatus 15 "Reserved" 14 "MCU_AD" 13 "AADC_T" 12 "INT" 11 "AOSC" 10 "AADC_C" 9 "OVD" 8 "FET" 7 "AADC_V" 6 "OSC" 5 "IDP" 4 "IOP" 3 "ROM" 2 "STA" 1 "RAM" 0 "CPU" ;
VAL_ 2147483914 BMS_PFStatusLow 15 "LBCF" 14 "FET_NG" 13 "Reserved" 12 "OTPF" 11 "Reserved" 10 "CFUSE" 9 "AFE_NG" 8 "FETF2" 7 "AFE_PATAM" 6 "UVPF" 5 "MCUF" 4 "FETF" 3 "P2ND" 2 "OVPF" 1 "FUSE" 0 "CUBF" ;
VAL_ 2147483914 BMS_PFStatusHigh 15 "Bit15" 14 "Bit14" 13 "Bit13" 12 "Bit12" 11 "Bit11" 10 "Bit10" 9 "Bit9" 8 "Bit8" 7 "Bit7" 6 "Bit6" 5 "Bit5" 4 "Bit4" 3 "Bit3" 2 "Bit2" 1 "Bit1" 0 "Bit0" ;
VAL_ 2147483914 BMS_CBS0_15 15 "Cell-16" 14 "Cell-15" 13 "Cell-14" 12 "Cell-13" 11 "Cell-12" 10 "Cell-11" 9 "Cell-10" 8 "Cell-9" 7 "Cell-8" 6 "Cell-7" 5 "Cell-6" 4 "Cell-5" 3 "Cell-4" 2 "Cell-3" 1 "Cell-2" 0 "Cell-1" ;
VAL_ 2147483916 BMS_EngMode 7 "Bit7" 6 "Bit6" 5 "Bit5" 4 "Bit4" 3 "Bit3" 2 "Bit2" 1 "Bit1" 0 "Bit0" ;

