/**
  ******************************************************************************
  * @file        AppSerialCanDavinciDetail.c
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2022/02/10
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2022 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

//#include "HalAfe.h"
//
//#include "HalCan.h"
//
#include "LibSoftwareTimerHandler.h"
#include "AppSerialCanDavinci.h"
//#include "ApiSysPar.h"
//#include "LibCalibration.h"
//#include "HalAfeADS7946.h"
//#include "HalBsp.h"
//#include "AppProject.h"
//#include "AppBms.h"
		  

void appSerialCanDavinciSendTextMessage(char *str);
#define	appSerialCanDavinciDetailMsg(str)	appSerialCanDavinciSendTextMessage(str)
/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define		canDbgScuId()		appProjectGetScuId()

/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/

/* Public function prototypes -----------------------------------------------*/
int32_t appCurrDebug(uint8_t CurrentIndex, int32_t adc);
int32_t appVbatDebug(uint8_t VbatIndex, int32_t adc);

static void DavinciCanDetailCellVoltage(tHalCanFrame *pCanPkg)
{
	uint8_t		i;
	uint8_t		scuid;
	uint16_t	subindex;
		
	scuid = SMP_CAN_GET_SCU_ID(pCanPkg -> u32Id);
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	for(i=0; i<8; i+=2)
	{
		appBmsSetCellVoltage(scuid, subindex++, GET_WORD(&pCanPkg->tUnionData.u8Data[i]));
	}	
}

static void DavinciCanDetailPackVoltage(tHalCanFrame *pCanPkg)
{
	uint8_t		i;
	uint8_t		scuid;
	uint16_t	subindex;
	
	scuid = SMP_CAN_GET_SCU_ID(pCanPkg -> u32Id);
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	for(i=0; i<8; i+=2)
	{
		appBmsSetPackVoltage(scuid, subindex++, GET_WORD(&pCanPkg->tUnionData.u8Data[i]));
#if 0
		{
			char	str[100];
			sprintf(str,"Rcv PackV:%d %d %d",scuid, subindex-1, GET_WORD(&pCanPkg->tUnionData.u8Data[i]));
				appSerialCanDavinciSendTextMessage(str);
		}
#endif		
	}	
}

static void DavinciCanDetailNtcVoltage(tHalCanFrame *pCanPkg)
{
	uint8_t		i;
	uint8_t		scuid;
	uint16_t	subindex;
		
	scuid = SMP_CAN_GET_SCU_ID(pCanPkg -> u32Id);
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	for(i=0; i<8; i+=2)
	{
		appBmsSetNtcVoltage(scuid, subindex++, GET_WORD(&pCanPkg->tUnionData.u8Data[i]));
	}	
}


SMP_CAN_DECODE_CMD_START(mDavinciDetailCanDecodeTab)
	#if 0
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 0,
									SMP_DETAIL_CELL_VOLTAGE_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDetailCellVoltage)
	#endif
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 0,
									SMP_DETAIL_PACK_VOLTAGE_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDetailPackVoltage)
	#if 0
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, 0,
									SMP_DETAIL_CELL_NTC_VOLTAGE_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDetailNtcVoltage)
	#endif

SMP_CAN_DECODE_CMD_END();


void DavinciCanFunDetailTx(tHalCanFrame *pCanPkg)
{
//	uint8_t	i,n;
	uint8_t cmdIndex;
	
//	char	str[100];
//	char	str1[100];
 	cmdIndex = 0;

	for(cmdIndex = 0; mDavinciDetailCanDecodeTab[cmdIndex].fun != 0; cmdIndex++)
	{
		if((mDavinciDetailCanDecodeTab[cmdIndex].canid & mDavinciDetailCanDecodeTab[cmdIndex].mask) == 
		   (mDavinciDetailCanDecodeTab[cmdIndex].mask & pCanPkg -> u32Id))
		{
		//	sprintf(str,"Debug ID= %d",SMP_CAN_GET_SCU_ID(pCanPkg -> u32Id));
		//	appSerialCanDavinciDebugMsg(str);
			mDavinciDetailCanDecodeTab[cmdIndex].fun(pCanPkg);
			break; 		
 		}
	}
}

/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    




