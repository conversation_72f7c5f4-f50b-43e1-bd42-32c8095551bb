/*
******************************************************************************
* @file     LibSpiFifo.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "LibSpiFifo.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
static void LibSpiFifoMemcpyPacketAndCloneTx(tLibSpiPacket *pSrcLibSpiPacket, tLibSpiPacket *pDstLibSpiPacket);
static void LibSpiFifoMemcpyPacket(tLibSpiPacket *pSrcLibSpiPacket, tLibSpiPacket *pDstLibSpiPacket);
/* Global variables ---------------------------------------------------------*/
/* Local function prototypes ------------------------------------------------*/
static void LibSpiFifoMemcpyPacketAndCloneTx(tLibSpiPacket *pSrcLibSpiPacket, tLibSpiPacket *pDstLibSpiPacket)
{
    for (uint16_t u16Index = 0; u16Index < pSrcLibSpiPacket -> u16TxSize; u16Index++)
    {
        pDstLibSpiPacket -> pu8TxData[u16Index] = pSrcLibSpiPacket -> pu8TxData[u16Index];
    }
    pDstLibSpiPacket -> u16TxSize = pSrcLibSpiPacket -> u16TxSize;
    pDstLibSpiPacket -> pu8RxData = pSrcLibSpiPacket -> pu8RxData;
    pDstLibSpiPacket -> u16RxSize = pSrcLibSpiPacket -> u16RxSize;
    pDstLibSpiPacket -> u8CsIndex = pSrcLibSpiPacket -> u8CsIndex;
    pDstLibSpiPacket -> eTransferMode = pSrcLibSpiPacket -> eTransferMode;
    pDstLibSpiPacket -> fpCallback = pSrcLibSpiPacket -> fpCallback;
    return;
}

static void LibSpiFifoMemcpyPacket(tLibSpiPacket *pSrcLibSpiPacket, tLibSpiPacket *pDstLibSpiPacket)
{
    *pDstLibSpiPacket = *pSrcLibSpiPacket;
    /*
    pDstLibSpiPacket -> pu8TxData = pSrcLibSpiPacket -> pu8TxData;
    pDstLibSpiPacket -> u16TxSize = pSrcLibSpiPacket -> u16TxSize;
    pDstLibSpiPacket -> pu8RxData = pSrcLibSpiPacket -> pu8RxData;
    pDstLibSpiPacket -> u16RxSize = pSrcLibSpiPacket -> u16RxSize;
    pDstLibSpiPacket -> u8CsIndex = pSrcLibSpiPacket -> u8CsIndex;
    pDstLibSpiPacket -> eTransferMode = pSrcLibSpiPacket -> eTransferMode;
    pDstLibSpiPacket -> fpCallback = pSrcLibSpiPacket -> fpCallback;
    */
    return;
}
/* Global function prototypes -----------------------------------------------*/
bool LibSpiFifoIsEmpty(tLibSpiFifo *pmSpiFifo)
{
    if (pmSpiFifo -> u16FifoPushInPosi == pmSpiFifo -> u16FifoPopOutPosi)
    {
        return true;
    }
    return false;
}

uint16_t LibSpiFifoGetCount(tLibSpiFifo *pmSpiFifo)
{
    uint16_t u16QueueSize;
    if (pmSpiFifo -> u16FifoPushInPosi == pmSpiFifo -> u16FifoPopOutPosi)
    {
        u16QueueSize = 0;
    }
    else if (pmSpiFifo -> u16FifoPushInPosi > pmSpiFifo -> u16FifoPopOutPosi)
    {
        u16QueueSize = (pmSpiFifo -> u16FifoPushInPosi - pmSpiFifo -> u16FifoPopOutPosi);
    }
    else
    {
        u16QueueSize = ((pmSpiFifo -> u16FifoSize - pmSpiFifo -> u16FifoPopOutPosi) + pmSpiFifo -> u16FifoPushInPosi);
    }
    return u16QueueSize;
}

int8_t LibSpiFifoPushAndCloneTx(tLibSpiFifo *pmSpiFifo, tLibSpiPacket *pmLibSpiPacket)
{
    if (((pmSpiFifo -> u16FifoPushInPosi + 1) % pmSpiFifo -> u16FifoSize) == pmSpiFifo -> u16FifoPopOutPosi)
    {
        return RES_ERROR_NULL;
    }
    LibSpiFifoMemcpyPacketAndCloneTx(pmLibSpiPacket, &(pmSpiFifo -> pmFifoStartAddr[pmSpiFifo -> u16FifoPushInPosi]));
    pmSpiFifo -> u16FifoPushInPosi = ((pmSpiFifo -> u16FifoPushInPosi + 1) % pmSpiFifo -> u16FifoSize);
    return RES_SUCCESS;
}

int8_t LibSpiFifoPush(tLibSpiFifo *pmSpiFifo, tLibSpiPacket *pmLibSpiPacket)
{
    if (((pmSpiFifo -> u16FifoPushInPosi + 1) % pmSpiFifo -> u16FifoSize) == pmSpiFifo -> u16FifoPopOutPosi)
    {
        return RES_ERROR_NULL;
    }
    LibSpiFifoMemcpyPacket(pmLibSpiPacket, &(pmSpiFifo -> pmFifoStartAddr[pmSpiFifo -> u16FifoPushInPosi]));
    pmSpiFifo -> u16FifoPushInPosi = ((pmSpiFifo -> u16FifoPushInPosi + 1) % pmSpiFifo -> u16FifoSize);
    return RES_SUCCESS;
}

int8_t LibSpiFifoPop(tLibSpiFifo *pmSpiFifo, tLibSpiPacket *pmLibSpiPacket)
{
    if (pmSpiFifo -> u16FifoPushInPosi == pmSpiFifo -> u16FifoPopOutPosi)
    {
        return RES_ERROR_RESOURCES;
    }
    LibSpiFifoMemcpyPacket(&(pmSpiFifo -> pmFifoStartAddr[pmSpiFifo -> u16FifoPopOutPosi]), pmLibSpiPacket);
    pmSpiFifo -> u16FifoPopOutPosi = ((pmSpiFifo -> u16FifoPopOutPosi + 1) % pmSpiFifo -> u16FifoSize);
    return RES_SUCCESS;
}

int8_t LibSpiFifoPeekFront(tLibSpiFifo *pmSpiFifo, tLibSpiPacket *pmLibSpiPacket)
{
    if (pmSpiFifo -> u16FifoPushInPosi == pmSpiFifo -> u16FifoPopOutPosi)
    {
        return RES_ERROR_RESOURCES;
    }
    LibSpiFifoMemcpyPacket(&(pmSpiFifo -> pmFifoStartAddr[pmSpiFifo -> u16FifoPopOutPosi]), pmLibSpiPacket);
    return RES_SUCCESS;
}