/*
******************************************************************************
* @file     ApiProtectCutp.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "Define.h"
#include "HalAfe.h"
#include "ApiProtect.h"
#include "LibSysPar.h"

void AppSerialUartSendMessage(char *str);
/* Private define ------------------------------------------------------------*/
#define	API_PROTECT_CUTP_DEBUG_MSG(pc8Msg)		AppSerialUartSendMessage(pc8Msg)
#define	API_PROTECT_CUTP_CHECK_NUM_PER_TIME	    20
#define	API_PROTECT_CUTP_GET_NTC_NUMBER()		LibSysParGetNtcNumber()
#define API_PROTECT_CUTP_GET_NTC_VOLTAGE(u16Ntcs)      HalAfeGetNtcVoltage(u16Ntcs);

#define	API_PROTECT_CUTP_GET_CUTP_PAR(u8ProtectLevel, mProtectPar) 		LibSysParGetCutpProtectPar(u8ProtectLevel, mProtectPar)
#define	API_PROTECT_CUTP_GET_LEVEL_MASK(u8ProtectLevel, mProtectFlagValue)	ApiProtectGetLevelMask(u8ProtectLevel, mProtectFlagValue)
#define API_PROTECT_CUTP_IS_OT(u16u16NtcVoltage, u32ParValue)  ApiProtectIsOverTemperter(u16u16NtcVoltage, u32ParValue)
#define API_PROTECT_CUTP_IS_UT(u16u16NtcVoltage, u32ParValue)  ApiProtectIsUnderTemperter(u16u16NtcVoltage, u32ParValue)

/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t	u8Flag[MAX_NTC_NUMBER];
	uint8_t	u8SetCount[API_PROTECT_LEVEL][MAX_NTC_NUMBER];
	uint8_t	u8ReleaseCount[API_PROTECT_LEVEL][MAX_NTC_NUMBER];
	tfpApiProtectEvtHandler  fpEvtHandler;
}tCutpProtect;

static tCutpProtect	gmCutpProtect={0};
static uint16_t gu16CutpNtcIndex = 0;
static bool bCutpEnable = 0;
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
static void ApiProtectCutpProtectIni(void)
{
	gu16CutpNtcIndex = 0;
}
/* Public function prototypes -----------------------------------------------*/
uint8_t	ApiProtectCutpGetFlag(uint16_t NtcIndex)
{
	return gmCutpProtect.u8Flag[NtcIndex];
}

uint8_t ApiProtectCutpHandler(uint8_t u8ProtectLevel)
{
	uint8_t		u8Checkcount = 0;
	uint16_t	u16NtcVoltage;
	tProtectFlagValue	mProtectFlagValue;
	tScuProtectPar		mProtectPar;

	if (bCutpEnable == 0)
	{
		return 1;
	}
	
	API_PROTECT_CUTP_GET_CUTP_PAR(u8ProtectLevel, &mProtectPar);
	API_PROTECT_CUTP_GET_LEVEL_MASK(u8ProtectLevel, &mProtectFlagValue);

	while(1)
	{			
		u16NtcVoltage = API_PROTECT_CUTP_GET_NTC_VOLTAGE(gu16CutpNtcIndex);

		if(u16NtcVoltage > NTC_ADC_UPPER_BOUND){
			gmCutpProtect.u8Flag[gu16CutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
			gmCutpProtect.u8ReleaseCount[u8ProtectLevel][gu16CutpNtcIndex] = 0;
			gmCutpProtect.u8SetCount[u8ProtectLevel][gu16CutpNtcIndex] = 0;
			goto compareNext;
		}
		
		if((mProtectPar.mSTime.u32Value != 0) &&
		   (API_PROTECT_CUTP_IS_UT(u16NtcVoltage, mProtectPar.mSetValue.u32Value) != 0)) 
		{
			if((gmCutpProtect.u8Flag[gu16CutpNtcIndex] & mProtectFlagValue.u8Mask) == 0)
			{
				gmCutpProtect.u8Flag[gu16CutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
				gmCutpProtect.u8Flag[gu16CutpNtcIndex] |= mProtectFlagValue.u8Setting;
				gmCutpProtect.u8SetCount[u8ProtectLevel][gu16CutpNtcIndex] = 1;
			}	
			else if((gmCutpProtect.u8Flag[gu16CutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
			{
				gmCutpProtect.u8SetCount[u8ProtectLevel][gu16CutpNtcIndex]++;
				if(gmCutpProtect.u8SetCount[u8ProtectLevel][gu16CutpNtcIndex] >= mProtectPar.mSTime.u32Value)
				{
					gmCutpProtect.u8Flag[gu16CutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
					gmCutpProtect.u8Flag[gu16CutpNtcIndex] |= mProtectFlagValue.u8Setted;
					gmCutpProtect.u8SetCount[u8ProtectLevel][gu16CutpNtcIndex] = 0;

					if(gmCutpProtect.fpEvtHandler)
					{
						gmCutpProtect.fpEvtHandler(0, kAPI_PROTECT_CUTP_L1_SET + u8ProtectLevel, &gu16CutpNtcIndex);	
					}
				}
			}
		}
		else if((gmCutpProtect.u8Flag[gu16CutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
		{
			gmCutpProtect.u8Flag[gu16CutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
		}
		//--------------------------------------------------------------------------
		//	Level	Release
		if((mProtectPar.mRTime.u32Value != 0) && 
		   (API_PROTECT_CUTP_IS_OT(u16NtcVoltage, mProtectPar.mRelValue.u32Value) != 0))
		{
			if((gmCutpProtect.u8Flag[gu16CutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setted)
			{
				gmCutpProtect.u8Flag[gu16CutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
				gmCutpProtect.u8Flag[gu16CutpNtcIndex] |= mProtectFlagValue.u8Releasing;
				gmCutpProtect.u8ReleaseCount[u8ProtectLevel][gu16CutpNtcIndex] = 1;
			}	
			else if((gmCutpProtect.u8Flag[gu16CutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
			{
				gmCutpProtect.u8ReleaseCount[u8ProtectLevel][gu16CutpNtcIndex]++;
				if(gmCutpProtect.u8ReleaseCount[u8ProtectLevel][gu16CutpNtcIndex] >= mProtectPar.mRTime.u32Value)
				{
					gmCutpProtect.u8Flag[gu16CutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
					gmCutpProtect.u8ReleaseCount[u8ProtectLevel][gu16CutpNtcIndex] = 0;
					if(gmCutpProtect.fpEvtHandler)
					{
						gmCutpProtect.fpEvtHandler(0, kAPI_PROTECT_CUTP_L1_RELEASE + u8ProtectLevel, &gu16CutpNtcIndex);	
					}
				}
			}
		}
		else if((gmCutpProtect.u8Flag[gu16CutpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
		{
			gmCutpProtect.u8Flag[gu16CutpNtcIndex] &= mProtectFlagValue.u8ClearMask;
			gmCutpProtect.u8Flag[gu16CutpNtcIndex] |= mProtectFlagValue.u8Setted;
		}
compareNext:
		gu16CutpNtcIndex++;
		if(gu16CutpNtcIndex >= API_PROTECT_CUTP_GET_NTC_NUMBER())
		{
			gu16CutpNtcIndex = 0;
			return 1;
		}
		u8Checkcount++;
		if(u8Checkcount >= API_PROTECT_CUTP_CHECK_NUM_PER_TIME)
			break;
	}
	return 0;
}

void ApiProtectCutpOpen(tfpApiProtectEvtHandler fpEvtHandler)
{
	ApiProtectCutpProtectIni();
	
	bCutpEnable = 1;
	
	gmCutpProtect.fpEvtHandler = fpEvtHandler;
}

/************************ (C) COPYRIGHT *****END OF FILE****/    

