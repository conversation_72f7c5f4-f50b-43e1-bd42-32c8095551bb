/*
******************************************************************************
* @file     ApiProtect.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

#ifndef _API_PROTECT_H_
#define _API_PROTECT_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "ApiProtectEvent.h"
#include "LibFunctionPointerRegister.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Public define ------------------------------------------------------------*/
#define tfpApiProtectEvtHandler tLibFuncPtrRegEvtHandler

#define	API_PROTECT_LEVEL	3

#define	API_PROTECT_FLAG_L1_MASK		(0x03 << 0)
#define	API_PROTECT_FLAG_L1_NORMAL		(0x00 << 0)
#define	API_PROTECT_FLAG_L1_SETTING		(0x01 << 0)
#define	API_PROTECT_FLAG_L1_SETTED		(0x02 << 0)
#define	API_PROTECT_FLAG_L1_REALSING	(0x03 << 0)

#define	API_PROTECT_FLAG_L2_MASK		(0x03 << 2)
#define	API_PROTECT_FLAG_L2_NORMAL		(0x00 << 2)
#define	API_PROTECT_FLAG_L2_SETTING		(0x01 << 2)
#define	API_PROTECT_FLAG_L2_SETTED		(0x02 << 2)
#define	API_PROTECT_FLAG_L2_REALSING	(0x03 << 2)

#define	API_PROTECT_FLAG_L3_MASK		(0x03 << 4)
#define	API_PROTECT_FLAG_L3_NORMAL		(0x00 << 4)
#define	API_PROTECT_FLAG_L3_SETTING		(0x01 << 4)
#define	API_PROTECT_FLAG_L3_SETTED		(0x02 << 4)
#define	API_PROTECT_FLAG_L3_REALSING	(0x03 << 4)

#define	API_PROTECT_FLAG_L4_MASK		(0x03 << 6)
#define	API_PROTECT_FLAG_L4_NORMAL		(0x00 << 6)
#define	API_PROTECT_FLAG_L4_SETTING		(0x01 << 6)
#define	API_PROTECT_FLAG_L4_SETTED		(0x02 << 6)
#define	API_PROTECT_FLAG_L4_REALSING	(0x03 << 6)

/* Public typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t		u8Mask;
	uint8_t		u8ClearMask;
	uint8_t		u8Setting;
	uint8_t		u8Setted;
	uint8_t		u8Releasing;
}tProtectFlagValue;

/* Public macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
void ApiProtectGetLevelMask(uint8_t u8Level, tProtectFlagValue *pmProtectFlagValue);
uint8_t	ApiProtectIsUnderTemperter(uint16_t u16NtcVoltage, uint16_t u16CompareVoltage);
uint8_t	ApiProtectIsOverTemperter(uint16_t u16NtcVoltage, uint16_t u16CompareVoltage);
void ApiProtectOpen(tfpApiProtectEvtHandler _fpEvtHandler);
void ApiProtectHandler(uint16_t u16Evt);

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */


#ifdef __cplusplus
}
#endif


#endif /* _API_PROTECT_H_ */


/************************ (C) COPYRIGHT *****END OF FILE****/    





