#!/usr/bin/env python3
"""
CAN_IAHP_BMS Code Generator using cantools
专门为CAN_IAHP_BMS.dbc生成C代码

使用方法:
python generate_can_code.py

自动查找并使用CAN_IAHP_BMS.dbc文件
"""

import os
import sys
import cantools
from pathlib import Path

def generate_c_header(db, dbc_name):
    """生成C头文件内容"""

    header_content = f"""/*
 * {dbc_name.upper()} - CAN Database Header File
 * Generated from CAN_IAHP_BMS.dbc using cantools
 *
 * Total Messages: {len(db.messages)}
 * Generated on: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
 */

#ifndef {dbc_name.upper()}_H
#define {dbc_name.upper()}_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {{
#endif

/* Message IDs */
"""

    # 添加消息ID定义
    for msg in db.messages:
        header_content += f"#define {dbc_name.upper()}_{msg.name.upper()}_ID 0x{msg.frame_id:08X}U\n"

    header_content += f"\n/* Message Structures */\n"

    # 添加消息结构体定义
    for msg in db.messages:
        struct_name = f"{dbc_name}_{msg.name.lower()}_t"
        header_content += f"\ntypedef struct {{\n"

        for signal in msg.signals:
            if signal.is_signed:
                data_type = "int32_t" if signal.length > 16 else ("int16_t" if signal.length > 8 else "int8_t")
            else:
                data_type = "uint32_t" if signal.length > 16 else ("uint16_t" if signal.length > 8 else "uint8_t")

            header_content += f"    {data_type} {signal.name.lower()};\n"

        header_content += f"}} {struct_name};\n"

    header_content += f"\n/* Function Prototypes */\n"

    # 添加函数声明 - 只需要pack函数，BMS只发送不接收
    for msg in db.messages:
        func_prefix = f"{dbc_name}_{msg.name.lower()}"
        struct_name = f"{dbc_name}_{msg.name.lower()}_t"

        header_content += f"int {func_prefix}_pack(uint8_t *data, const {struct_name} *msg, uint8_t size);\n"

    # 添加定时器管理相关函数
    header_content += f"""
/* Timer Management Functions */
void {dbc_name}_timer_init(void);
void {dbc_name}_timer_1ms_handler(void);
void {dbc_name}_process_delayed_messages(uint8_t current_ms);
void {dbc_name}_broadcast_all_messages(void);

/* Message timing control */
typedef struct {{
    uint8_t delay_time;     // GenMsgDelayTime (0-5ms)
    uint8_t cycle_time;     // GenMsgCycleTime (50ms)
    uint8_t delay_counter;  // 当前延迟计数器
    uint8_t cycle_counter;  // 当前周期计数器
    uint8_t enabled;        // 消息使能标志
}} {dbc_name}_msg_timing_t;

extern {dbc_name}_msg_timing_t {dbc_name}_msg_timings[{len(db.messages)}];
"""

    header_content += f"""
#ifdef __cplusplus
}}
#endif

#endif /* {dbc_name.upper()}_H */
"""

    return header_content

def generate_c_source(db, dbc_name):
    """生成C源文件内容"""

    source_content = f"""/*
 * {dbc_name.upper()} - CAN Database Source File
 * Generated from CAN_IAHP_BMS.dbc using cantools
 */

#include "{dbc_name}.h"
#include <string.h>

/* Helper macros for bit manipulation */
#define SET_SIGNAL(data, value, start_bit, length) \\
    do {{ \\
        uint64_t temp_val = (uint64_t)(value); \\
        for (int i = 0; i < (length); i++) {{ \\
            if (temp_val & (1ULL << i)) {{ \\
                (data)[((start_bit) + i) / 8] |= (1U << (((start_bit) + i) % 8)); \\
            }} else {{ \\
                (data)[((start_bit) + i) / 8] &= ~(1U << (((start_bit) + i) % 8)); \\
            }} \\
        }} \\
    }} while(0)

/* Message timing configuration based on GenMsgDelayTime */
{dbc_name}_msg_timing_t {dbc_name}_msg_timings[{len(db.messages)}] = {{
"""

    # 按物理量类型分组分配延迟时间
    def get_message_delay_by_type(msg_name):
        """根据消息名称的物理量类型分配延迟时间"""
        msg_name_upper = msg_name.upper()

        # PackInfo类消息 - 0ms延迟
        if 'PACKINFO' in msg_name_upper:
            return 0

        # Status类消息 - 1ms延迟
        elif 'STATUS' in msg_name_upper:
            return 1

        # Cell电压类消息 - 2ms延迟
        elif 'CELL' in msg_name_upper:
            return 2

        # Temperature温度类消息 - 3ms延迟
        elif 'TEMP' in msg_name_upper:
            return 3

        # OtherVolt其他电压类消息 - 4ms延迟
        elif 'OTHERVOLT' in msg_name_upper:
            return 4

        # Version版本信息类消息 - 5ms延迟
        elif 'VERSION' in msg_name_upper:
            return 5

        # 默认延迟时间
        else:
            return 0

    # 为每个消息分配基于物理量类型的延迟时间
    for i, msg in enumerate(db.messages):
        delay_time = get_message_delay_by_type(msg.name)
        source_content += f"    {{ {delay_time}, 50, 0, 0, 1 }},  // {msg.name}: DelayTime={delay_time}ms (物理量分组), CycleTime=50ms\n"

    source_content += f"""}};

/* Timer initialization */
void {dbc_name}_timer_init(void)
{{
    // 初始化所有消息的定时器
    for (int i = 0; i < {len(db.messages)}; i++) {{
        {dbc_name}_msg_timings[i].delay_counter = {dbc_name}_msg_timings[i].delay_time;
        {dbc_name}_msg_timings[i].cycle_counter = 0;
    }}
}}

/* 1ms定时器处理函数 - 实现GenMsgDelayTime延迟 */
void {dbc_name}_timer_1ms_handler(void)
{{
    static uint8_t ms_counter = 0;

    ms_counter++;

    // 每50ms重置计数器，开始新的发送周期
    if (ms_counter >= 50) {{
        ms_counter = 0;
    }}

    // 在每个1ms时刻，检查并发送对应延迟时间的消息
    {dbc_name}_process_delayed_messages(ms_counter);
}}

/* 处理延迟消息发送 - 在每个1ms时刻检查应该发送的消息 */
void {dbc_name}_process_delayed_messages(uint8_t current_ms)
{{
    uint8_t data[8];

    // 遍历所有消息，检查是否应该在当前时刻发送
    for (int i = 0; i < {len(db.messages)}; i++) {{
        if ({dbc_name}_msg_timings[i].enabled &&
            {dbc_name}_msg_timings[i].delay_time == current_ms) {{

            // 发送消息
            switch(i) {{
"""

    # 为每个消息添加发送case
    for i, msg in enumerate(db.messages):
        func_prefix = f"{dbc_name}_{msg.name.lower()}"
        struct_name = f"{dbc_name}_{msg.name.lower()}_t"

        source_content += f"""                case {i}: // {msg.name}
                {{
                    struct {struct_name} msg_data;
                    // 用户需要在这里设置实际的BMS数据
                    // 示例：msg_data.signal_name = actual_value;

                    if ({func_prefix}_pack(data, &msg_data, sizeof(data)) == 0) {{
                        // 用户需要实现CAN发送函数
                        // can_broadcast(0x{msg.frame_id:08X}, data, {msg.length});
                    }}
                    break;
                }}
"""

    source_content += f"""            }}
        }}
    }}
}}

/* 广播所有消息 - 兼容性函数，用于测试 */
void {dbc_name}_broadcast_all_messages(void)
{{
    // 直接调用延迟处理函数，发送所有消息
    for (uint8_t delay = 0; delay <= 5; delay++) {{
        {dbc_name}_process_delayed_messages(delay);
    }}
}}

"""

    # 为每个消息生成pack函数 - BMS只需要发送功能
    for msg in db.messages:
        func_prefix = f"{dbc_name}_{msg.name.lower()}"
        struct_name = f"{dbc_name}_{msg.name.lower()}_t"

        # Pack函数 - BMS广播数据打包
        source_content += f"""
/* Pack function for {msg.name} - BMS广播数据打包 */
int {func_prefix}_pack(uint8_t *data, const {struct_name} *msg, uint8_t size)
{{
    if (data == NULL || msg == NULL || size < {msg.length}) {{
        return -1;
    }}

    memset(data, 0, {msg.length});

"""

        for signal in msg.signals:
            source_content += f"    SET_SIGNAL(data, msg->{signal.name.lower()}, {signal.start}, {signal.length});\n"

        source_content += f"""
    return 0;
}}
"""

    return source_content

def generate_can_code(dbc_file_path=None, output_dir=None):
    """
    使用cantools生成C代码，专门针对CAN_IAHP_BMS.dbc

    Args:
        dbc_file_path: DBC文件路径，默认为CAN_IAHP_BMS.dbc
        output_dir: 输出目录，默认为当前目录
    """

    # 设置默认路径 - 专门查找CAN_IAHP_BMS.dbc
    if dbc_file_path is None:
        # 查找CAN_IAHP_BMS.dbc文件的可能位置
        possible_paths = [
            "../../../CAN_IAHP_BMS.dbc",
            "CAN_IAHP_BMS.dbc",
            "../CAN_IAHP_BMS.dbc",
            "../../CAN_IAHP_BMS.dbc"
        ]

        dbc_file_path = None
        for path in possible_paths:
            if os.path.exists(path):
                dbc_file_path = path
                break

        if dbc_file_path is None:
            print("❌ 未找到CAN_IAHP_BMS.dbc文件")
            print("请确保CAN_IAHP_BMS.dbc文件在以下位置之一:")
            for path in possible_paths:
                print(f"   - {os.path.abspath(path)}")
            return False
    
    if output_dir is None:
        output_dir = "."
    
    try:
        print(f"📂 加载DBC文件: {dbc_file_path}")
        
        # 加载DBC文件
        db = cantools.database.load_file(dbc_file_path)
        print(f"✅ 成功加载 {len(db.messages)} 个消息")
        
        # 固定使用can_iahp_bms作为文件名前缀
        dbc_name = "can_iahp_bms"
        
        # 生成C代码
        print(f"🔧 生成C代码...")

        # 手动生成C代码，因为cantools的C生成功能可能不可用
        # 生成头文件
        header_content = generate_c_header(db, dbc_name)
        header_file = os.path.join(output_dir, f"{dbc_name}.h")

        with open(header_file, 'w', encoding='utf-8') as f:
            f.write(header_content)
        print(f"✅ 生成头文件: {header_file}")

        # 生成源文件
        source_content = generate_c_source(db, dbc_name)
        source_file = os.path.join(output_dir, f"{dbc_name}.c")

        with open(source_file, 'w', encoding='utf-8') as f:
            f.write(source_content)
        print(f"✅ 生成源文件: {source_file}")
        
        # 生成消息统计信息
        generate_message_info(db, dbc_name, output_dir)
        
        # 生成使用示例
        generate_usage_example(db, dbc_name, output_dir)
        
        print(f"🎉 代码生成完成!")
        print(f"📁 输出目录: {os.path.abspath(output_dir)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return False

def generate_message_info(db, dbc_name, output_dir):
    """生成消息信息文档"""
    
    info_content = f"""# {dbc_name.upper()} 消息信息

## 消息统计
- 总消息数: {len(db.messages)}
- 扩展帧数: {sum(1 for msg in db.messages if msg.is_extended_frame)}
- 标准帧数: {sum(1 for msg in db.messages if not msg.is_extended_frame)}

## 消息列表

| 消息名称 | CAN ID | 帧类型 | 长度 | 周期时间 | 信号数 |
|----------|--------|--------|------|----------|--------|
"""
    
    for msg in db.messages:
        frame_type = "扩展帧" if msg.is_extended_frame else "标准帧"
        cycle_time = getattr(msg, 'cycle_time', 'N/A')
        cycle_str = f"{cycle_time}ms" if cycle_time != 'N/A' else 'N/A'
        
        info_content += f"| {msg.name} | 0x{msg.frame_id:08X} | {frame_type} | {msg.length} | {cycle_str} | {len(msg.signals)} |\n"
    
    info_content += f"""
## 信号统计
- 总信号数: {sum(len(msg.signals) for msg in db.messages)}

## 使用说明

### 包含头文件
```c
#include "{dbc_name}.h"
```

### BMS端：打包并广播消息
```c
uint8_t data[8];
struct {dbc_name}_bms_packinfo1_t msg;

// 设置BMS数据
msg.bms_vpack = 3600;        // 电池包电压 (0.01V)
msg.bms_packvoltage = 3600;  // 包电压 (0.01V)

// 打包消息
{dbc_name}_bms_packinfo1_pack(data, &msg, sizeof(data));

// 广播到CAN总线
can_broadcast(CAN_IAHP_BMS_BMS_PACKINFO1_ID, data, 8);
```

### 重要说明
- **BMS专用代码**：只包含pack函数，用于数据打包和广播
- **无接收功能**：BMS系统不需要接收其他消息，因此无unpack函数
- **广播模式**：BMS作为唯一发送方，向CAN总线广播数据
- **50ms周期**：延迟时间0~5ms分散发送，避免总线拥塞
- **扩展帧格式**：29位CAN ID，符合汽车CAN标准

### 其他ECU接收说明
其他ECU如需接收BMS广播数据，请：
1. 使用相同的DBC文件生成对应的unpack函数
2. 或根据信号定义手动实现数据解析
3. 本代码专注于BMS端功能，不包含接收相关代码
"""
    
    info_file = os.path.join(output_dir, f"{dbc_name}_info.md")
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(info_content)
    print(f"✅ 生成信息文档: {info_file}")

def generate_usage_example(db, dbc_name, output_dir):
    """生成使用示例代码"""

    example_content = f"""/*
 * {dbc_name.upper()} 使用示例
 * BMS广播消息处理示例代码
 *
 * 注意：BMS系统中所有消息都是广播消息
 * - BMS作为发送方：打包并广播数据
 * - 其他ECU作为接收方：接收并解包数据
 */

#include "{dbc_name}.h"
#include <stdio.h>
#include <string.h>

/* BMS端：手动打包并发送广播消息示例 */
void bms_manual_broadcast_example(void)
{{
    uint8_t data[8];

"""
    
    # 为前3个消息生成示例代码
    for i, msg in enumerate(db.messages[:3]):
        struct_name = f"{dbc_name}_{msg.name.lower()}_t"
        pack_func = f"{dbc_name}_{msg.name.lower()}_pack"
        
        example_content += f"""    /* 示例 {i+1}: {msg.name} */
    {{
        struct {struct_name} msg_{i+1};
        
        /* 设置信号值 */
"""
        
        # 为前2个信号生成示例
        for j, signal in enumerate(msg.signals[:2]):
            if signal.is_signed:
                example_value = "100" if j == 0 else "-50"
            else:
                example_value = "200" if j == 0 else "150"
            
            example_content += f"        msg_{i+1}.{signal.name.lower()} = {example_value};\n"
        
        example_content += f"""
        /* 打包消息 */
        {pack_func}(data, &msg_{i+1}, sizeof(data));

        /* 广播CAN消息 (用户需要实现CAN发送函数) */
        // can_broadcast(0x{msg.frame_id:08X}, data, {msg.length});

        // BMS广播消息 {msg.name}: ID=0x{msg.frame_id:08X}
    }}

"""
    
    example_content += f"""}}

/* BMS端：使用定时器自动广播消息 */
void bms_timer_broadcast_example(void)
{{
    // 初始化定时器系统
    {dbc_name}_timer_init();

    // 在1ms定时器中断中调用
    // {dbc_name}_timer_1ms_handler();

    // 或者直接调用广播函数（用于测试）
    {dbc_name}_broadcast_all_messages();
}}

/* 用户需要实现的CAN发送函数 */
void can_broadcast(uint32_t can_id, uint8_t *data, uint8_t length)
{{
    // 用户实现CAN硬件发送
    // 示例：
    // HAL_CAN_AddTxMessage(&hcan, &TxHeader, data, &TxMailbox);
}}

/* 1ms定时器中断服务程序示例 */
void TIM_1ms_IRQHandler(void)
{{
    // 调用BMS定时器处理函数
    {dbc_name}_timer_1ms_handler();
}}

/*
 * 注意：此代码专用于BMS端广播消息
 * - 包含基于GenMsgDelayTime的1ms定时器延迟实现
 * - 自动处理50ms周期和0~5ms延迟分散发送
 * - 其他ECU的接收功能需要根据具体需求单独实现
 */

/* 主函数示例 */
int main(void)
{{
    // {dbc_name.upper()} BMS广播消息示例
    // BMS消息总数: {len(db.messages)}
    // 所有消息都是BMS广播消息

    /* BMS端：手动广播示例 */
    // === BMS端手动广播示例 ===
    bms_manual_broadcast_example();

    /* BMS端：定时器自动广播示例 */
    // === BMS端定时器广播示例 ===
    // 包含GenMsgDelayTime的1ms定时器延迟实现
    bms_timer_broadcast_example();

    return 0;
}}
"""
    
    example_file = os.path.join(output_dir, f"{dbc_name}_example.c")
    with open(example_file, 'w', encoding='utf-8') as f:
        f.write(example_content)
    print(f"✅ 生成示例代码: {example_file}")

def main():
    """主函数"""
    print("🚀 CAN_IAHP_BMS Code Generator using cantools")
    print("=" * 55)
    print("专门为CAN_IAHP_BMS.dbc生成C代码")
    print()

    # 直接生成代码，不需要命令行参数
    success = generate_can_code()
    
    if success:
        print("\n✅ CAN_IAHP_BMS 代码生成成功!")
        print("📝 生成的文件:")
        print("   - can_iahp_bms.h (头文件)")
        print("   - can_iahp_bms.c (源文件)")
        print("   - can_iahp_bms_info.md (消息信息)")
        print("   - can_iahp_bms_example.c (使用示例)")
        print("\n🎯 使用方法:")
        print("   1. 将can_iahp_bms.h和can_iahp_bms.c添加到项目中")
        print("   2. #include \"can_iahp_bms.h\"")
        print("   3. 参考can_iahp_bms_example.c中的示例代码")
        return 0
    else:
        print("\n❌ 代码生成失败!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
