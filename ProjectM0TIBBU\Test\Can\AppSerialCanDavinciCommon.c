/**
  ******************************************************************************
  * @file        AppSerialCanDavinciCommon.c
  * <AUTHOR>
  * @version     v0.0.2
  * @date        2022/10/07
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

#include "HalAfe.h"

#include "HalCan.h"

#include "LibSoftwareTimerHandler.h"
#include "AppSerialCanDavinci.h"
#include "AppSerialCanDavinciNotification.h"
//#include "AppBms.h"
//#include "AppScuIdAssign.h"
//#include "ApiRelayControl.h"
//#include "SEGGER_RTT.h"


void appSerialCanDavinciSendTextMessage(char *str);
#define	canCommonDebugMsg(str)	appSerialCanDavinciSendTextMessage(str)

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/


static void DavinciCommonPCAppRequestData(tHalCanFrame *pCanPkg)
{
	if(memcmp(&pCanPkg->tUnionData.u8Data, "PCReqInf", 8) == 0){
		appSerialCanDavinciNotificationEnableDetailData();
	}
}

//DavinciCommonResponseScuId
//----------------------------------------------------------
SMP_CAN_DECODE_CMD_START(mDavinciCommonCanDecodeTab)
		

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_COMMON_RX, 0,
								SMP_COMMON_PC_SCUDATA_REQUEST_INDEX,
								0),
								CHECK_SMP_CAN_FUN | CHECK_SMP_CAN_OBJ,
								DavinciCommonPCAppRequestData)					
								

SMP_CAN_DECODE_CMD_END();


/* Public function prototypes -----------------------------------------------*/

void DavinciCanFunCommonRx(tHalCanFrame *pCanPkg)
{
//	uint8_t	i,n;
	uint8_t cmdIndex;
	
//	char	str[100];
//	char	str1[100];
 	cmdIndex = 0;

	for(cmdIndex = 0; mDavinciCommonCanDecodeTab[cmdIndex].fun != 0; cmdIndex++)
	{
		if((mDavinciCommonCanDecodeTab[cmdIndex].canid & mDavinciCommonCanDecodeTab[cmdIndex].mask) == 
		   (mDavinciCommonCanDecodeTab[cmdIndex].mask & pCanPkg -> u32Id))
		{
			mDavinciCommonCanDecodeTab[cmdIndex].fun(pCanPkg);
		///	canCommonDebugMsg("RRR");
			break; 		
 		}
	}
}


/* Public function prototypes -----------------------------------------------*/

/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/         
