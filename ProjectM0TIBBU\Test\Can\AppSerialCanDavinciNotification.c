/**
  ******************************************************************************
  * @file        AppSerialCanDavinciNotification.c
  * <AUTHOR>
  * @version     v0.0.3
  * @date        2022/10/07
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

#include "HalAfe.h"

#include "HalCan.h"

#include "LibSoftwareTimerHandler.h"
#include "LibNtc.h"
#include "AppSerialCanDavinci.h"
#include "AppSerialCanDavinciParameter.h"
#include "AppSerialCanDavinciNotification.h"
#include "AppSerialCanDavinciBaseCmd.h"
//#include "ApiModbusTCPIP.h"
#include "ApiProtectOvp.h"
#include "ApiProtectUvp.h"
#include "ApiProtectCotp.h"
#include "ApiProtectCutp.h"
#include "ApiProtectDotp.h"
#include "ApiProtectDutp.h"
#include "ApiProtectCocp.h"
#include "ApiProtectDocp.h"
//#include "AppBalance.h"
#include "ApiSystemFlag.h"
//#include "AppGauge.h"
//#include "AppBms.h"
//#include "App_ekfsoc.h"
//#include "AppProject.h"
//#include "ApiSysPar.h"
//#include "ApiScuTemp.h"
//#include "AppSerialUartRS485Slave.h"
//#include "ApiSignalFeedback.h"
#if 0
#include "SEGGER_RTT.h"
#endif

//#define	WAIT_LAST_SCU_BROCAST_FINISH

void appSerialCanDavinciSendTextMessage(char *str);
#define	notiDebugMsg(str)	//appSerialCanDavinciSendTextMessage(str)

/* Private typedef -----------------------------------------------------------*/
typedef uint8_t (* tNotificationRunTable)(void);

/* Private define ------------------------------------------------------------*/
#define 	BASIC_PKG_NUM			30
#define 	PUT_MAX_PKG_NUM			10
#define     POV_CANBUS_SEND_PACKAGE_ONE_CYCLE (20)
#define		POV1_CANBUS_SEND_NUM_ONE_PACKAGE  (8)
#define		POV2_CANBUS_SEND_NUM_ONE_PACKAGE  (4)
#define		POV1_CANBUS_SEND_NUM_ONE_CYCLE	  (POV1_CANBUS_SEND_NUM_ONE_PACKAGE * POV_CANBUS_SEND_PACKAGE_ONE_CYCLE)
#define		POV2_CANBUS_SEND_NUM_ONE_CYCLE	  (POV2_CANBUS_SEND_NUM_ONE_PACKAGE * POV_CANBUS_SEND_PACKAGE_ONE_CYCLE) 

#define		notifyScuId()			appProjectGetScuId()
#define		notifyNtcNumber()		apiSysParGetNtcNumber()
#define		notifyCellNumber()		apiSysParGetCellNumber()
#define		notifyPackNumber()		apiSysParGetPackNum()

#define		notifyCellBusbarNumber()		apiSysParGetCellBusbarNumber()
#define		notifyNtcBusbarNumber()			apiSysParGetNtcBusbarNumber()
#define		notifyNtcAmbientNumber()		apiSysParGetNtcAmbientNumber()
#define		notifyNtcOtherNumber()			apiSysParGetNtcOtherNumber()

#define 	STOP_NOTIFICATION_TIMEOUT	5

/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
static uint8_t	LastBroadcastBmuId = 0xff;
/* Private variables ---------------------------------------------------------*/
static uint8_t NotificationDetailEnableFlag = 0;
static	uint8_t CanDavinciNotificationFunIndex = 0;
static 	uint16_t	NotificationSubIndex = 0;
static 	uint16_t	NotificationPov1SubIndex = 0;
static 	uint16_t	NotificationPov2SubIndex = 0;

/* Private function prototypes -----------------------------------------------*/
static uint16_t notifyGetFreeFifoSize(void)
{
	uint16_t	size;
	
	
	size = AppSerialCan0DavinciGetTxFifoFreeSize();
	
#if 0	
	char	str[100];
	sprintf(str, "Tx FIFO Free Size: %d", size);
	appSerialCanDavinciSendTextMessage(str);
#endif		
	
	return size;
}

static void notifyNextFunction(void)
{
	NotificationSubIndex = 0;
	CanDavinciNotificationFunIndex++;
}

static uint8_t notifyFunNone(void)
{
	notifyNextFunction();
	
	return 0;
}

static void notifyBaseScuId(void)
{
	tHalCanFrame	CanPkg;
	tUnion16Bits	subindex;
			
	
#define	CHIP_ID0	(DWORD)(*(DWORD*)(0x1FFF7590UL))
#define	CHIP_ID1	(DWORD)(*(DWORD*)(0x1FFF7594UL)) 
#define	CHIP_ID2	(DWORD)(*(DWORD*)(0x1FFF7598UL)) 

	subindex.u16 = appProjectGetTimerCount();
	subindex.u16 ^= subindex.u8[1];
		
//	subindex = CHIP_ID0;
//	subindex ^= CHIP_ID1;
//	subindex ^= CHIP_ID2;
	subindex.u16 &= 0x3fe;

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_SCU_ID_OBJ_INDEX,
									subindex.u16);
	CanPkg.u8Dlc = 0;
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
#if 0
	{
		char  str[100];
		#define	CHIP_ID0	(DWORD)(*(DWORD*)(0x1FFF7590UL))

		sprintf(str,"Brocast ID %.8lX:%d %.3X", 
				CHIP_ID0,
				notifyScuId(), subindex.u16);
		appSerialCanDavinciSendTextMessage(str);
	}
#endif	
}


static void notifyBaseSystemFlag(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_SYSTEM_FLAG_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
	
	Lbyte.u32 = apiSystemFlagGetFlag1();
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];

	Lbyte.u32 = apiSystemFlagGetFlag2();
	CanPkg.tUnionData.u8Data[4] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[6] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[7] = Lbyte.u8[3];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_SYSTEM_FLAG_OBJ_INDEX,
									1);
	CanPkg.u8Dlc = 8;
	
	Lbyte.u32 = apiSystemFlagGetFlag3();
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];

	Lbyte.u32 = apiSystemFlagGetFlag4();
	CanPkg.tUnionData.u8Data[4] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[6] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[7] = Lbyte.u8[3];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_SYSTEM_FLAG_OBJ_INDEX,
									2);
	CanPkg.u8Dlc = 8;
	
	Lbyte.u32 = apiSystemFlagGetFlag5();
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
	
	Lbyte.u32 = apiSystemFlagGetFlag6();
	CanPkg.tUnionData.u8Data[4] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[6] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[7] = Lbyte.u8[3];

	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyBaseRmQmax(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_RM_QMAX_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
								
	//Lbyte.u32 = appGaugeGetRM();
	#if MAO_DISSABLE
	appBmsGetScuRM(notifyScuId(), &Lbyte.u32);
	#endif
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
	
	#if MAO_DISSABLE
	Lbyte.u32 = appGaugeGetQmax();
	#endif
	CanPkg.tUnionData.u8Data[4] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[6] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[7] = Lbyte.u8[3];

	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);

}

static void notifyBaseCurrent(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Curr1,Curr2;
//	char	str[100];
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_CURRENT_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
	#if MAO_DISSABLE
	appBmsGetScuCurrent(appBmsGetScuId(), &Curr1.i32, &Curr2.i32);
	#endif
	//Curr1.l = halAfeGetCurrentValue(P_CURRENT);
	CanPkg.tUnionData.u8Data[0] = Curr1.u8[0];
	CanPkg.tUnionData.u8Data[1] = Curr1.u8[1];
	CanPkg.tUnionData.u8Data[2] = Curr1.u8[2];
	CanPkg.tUnionData.u8Data[3] = Curr1.u8[3];
	
	//Curr2.l = halAfeGetCurrentValue(N_CURRENT);
	CanPkg.tUnionData.u8Data[4] = Curr2.u8[0];
	CanPkg.tUnionData.u8Data[5] = Curr2.u8[1];
	CanPkg.tUnionData.u8Data[6] = Curr2.u8[2];
	CanPkg.tUnionData.u8Data[7] = Curr2.u8[3];

	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
//	sprintf(str,"I1 = %d I2 = %d",Curr1.l, Curr2.l);
//	notiDebugMsg(str);
}

static void notifyBaseFCC(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_FCC_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 4;
								
	//Lbyte.u32 = appGaugeGetFCC();
	#if MAO_DISSABLE
	appBmsGetScuFCC(notifyScuId(), &Lbyte.u32);
	#endif
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void notifyBaseVBatVoltage(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_VB_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
	
	/// [CH] : 所有cell相加
	Lbyte.u32 = HalAfeGetVBatVoltage(kAFE_VBAT_INDEX);
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
	
	/// [CH] : Rack電壓過Relay
	Lbyte.u32 = HalAfeGetVBatVoltage(kAFE_VPACK_INDEX);
	CanPkg.tUnionData.u8Data[4] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[6] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[7] = Lbyte.u8[3];

	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	//---------------------------------------
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_VB_OBJ_INDEX,
									1);
	CanPkg.u8Dlc = 4;
	
	/// [CH] : Rack電壓不過Relay	
	Lbyte.u32 = HalAfeGetVBatVoltage(kAFE_VBCAL_INDEX);
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyBaseMinMaxValue(void)
{
	uint8_t		bmu,posi;
	tHalCanFrame	CanPkg;
	tUnion16Bits	Ibyte;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_MIN_MAX_VALUE_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
											
	Ibyte.u16 = HalAfeGetMinCellVoltage(&bmu, &posi, 0);
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	CanPkg.tUnionData.u8Data[4] = bmu;
	CanPkg.tUnionData.u8Data[5] = posi;

	Ibyte.u16 = HalAfeGetMaxCellVoltage(&bmu, &posi, 0);
	CanPkg.tUnionData.u8Data[2] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[3] = Ibyte.u8[1];
	CanPkg.tUnionData.u8Data[6] = bmu;
	CanPkg.tUnionData.u8Data[7] = posi;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	//----------------------------------------
	//	MinT & Max T
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_MIN_MAX_VALUE_OBJ_INDEX,
									1);
	CanPkg.u8Dlc = 8;
		
	Ibyte.u16 = HalAfeGetMinNtcTemp(&bmu, &posi);
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	CanPkg.tUnionData.u8Data[4] = bmu;
	CanPkg.tUnionData.u8Data[5] = posi;

	Ibyte.u16 = HalAfeGetMaxNtcTemp(&bmu, &posi);
	CanPkg.tUnionData.u8Data[2] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[3] = Ibyte.u8[1];
	CanPkg.tUnionData.u8Data[6] = bmu;
	CanPkg.tUnionData.u8Data[7] = posi;
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}


static void notifyBaseQStartPackage(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
	tUnion16Bits	Ibyte;
//	uint8_t		u8;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_QSTART_RSOC_SOH_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
	
	Lbyte.u32 = appGaugeGetQStart();
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
	
	//Ibyte.u16 = appGaugeGetRSoc();
	#if MAO_DISSABLE
	appBmsGetScuRSoc(notifyScuId(), &Ibyte.u16);
	#endif
	CanPkg.tUnionData.u8Data[4] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Ibyte.u8[1];
	
	//Ibyte.u16=appGaugeGetSOH();
	#if MAO_DISSABLE
	appBmsGetScuSoh(notifyScuId(), &Ibyte.u16);
	#endif
	CanPkg.tUnionData.u8Data[6] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[7] = Ibyte.u8[1];

	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyBaseRamSocPackage(void)
{
	tHalCanFrame	CanPkg;
//	tUnion32Bits	Lbyte;
	tUnion16Bits	Ibyte;
//	uint8_t		u8;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_RAMSOC_ENDSOC_DSOC_SOC0_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
	
	Ibyte.u16 = appGaugeGetRamSoc();
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	
	Ibyte.u16 = appGaugeGetEndOfSoc();
	CanPkg.tUnionData.u8Data[2] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[3] = Ibyte.u8[1];
	

	Ibyte.u16 = appGaugeGetDisplaySoc();
	CanPkg.tUnionData.u8Data[4] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Ibyte.u8[1];

	Ibyte.u16 = appGaugeGetSoc0();
	CanPkg.tUnionData.u8Data[6] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[7] = Ibyte.u8[1];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyBasePassCharge(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
//	uint8_t		u8;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_QPASS_RPASS_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
	
	Lbyte.i32 = appGaugeGetQPassCharge();
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
	
	Lbyte.i32 = appGaugeGetRPassCharge();
	CanPkg.tUnionData.u8Data[4] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[6] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[7] = Lbyte.u8[3];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}


static void notifyBaseCycleCount(void)
{
	tHalCanFrame	CanPkg;
	tUnion16Bits	Ibyte;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_CYCLE_COUNT_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 2;
	
	//Ibyte.u16 = appGaugeGetCycleCount();
	#if MAO_DISSABLE
	appBmsGetCycleCount(notifyScuId(), &Ibyte.u16);
	#endif
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyBaseIrValue(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits		Rp,Rn;
	uint8_t		i;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_IR_VALUE_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
	#if MAO_DISSABLE
	appProjcetGetIrValue(&Rp.u32, &Rn.u32);
	#endif
	for(i=0; i<4; i++)
	{
		CanPkg.tUnionData.u8Data[i] = Rp.u8[i];
		CanPkg.tUnionData.u8Data[4 + i] = Rn.u8[i];
	}
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyAccPower(void)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits		AccChg,AccDhg;
	uint8_t		i;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_ACCPOWER_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 8;
	#if MAO_DISSABLE
	appBmsGetScuAccChgPower(notifyScuId(), &AccChg.u32);
	appBmsGetScuAccDhgPower(notifyScuId(), &AccDhg.u32);
	#endif
	for(i=0; i<4; i++)
	{
		CanPkg.tUnionData.u8Data[i] = AccChg.u8[i];
		CanPkg.tUnionData.u8Data[4 + i] = AccDhg.u8[i];
	}
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyAfeStatus(void){
	tHalCanFrame CanPkg;
	// tUnion32Bits Lbyte;
	tUnion16Bits Ibyte;
	bool ring_flag;
	uint8_t AfeNum;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_AFESTATUS_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 4;
	#if MAO_DISSABLE
	HalAfeGetDeviceCntandRingStatus(&Ibyte.u8[0],&Ibyte.u8[1],&ring_flag, &AfeNum);
	#endif
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	if(ring_flag){
		CanPkg.tUnionData.u8Data[2] = 1;
	}else{
		CanPkg.tUnionData.u8Data[2] = 0;
	}
	CanPkg.tUnionData.u8Data[3] = AfeNum;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyPcsBmsSysyemStatus(void)
{
	tHalCanFrame	CanPkg;
	
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_PCS_BMS_SYSTEM_STATUS_OBJ_INDEX,
									0);
	CanPkg.u8Dlc = 1;
	#if MAO_DISSABLE
	CanPkg.tUnionData.u8Data[0] = GetPcsBmsSystemStatus();
	#endif
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyDiTogglingCount(void)
{
	tHalCanFrame	CanPkg;
	tUnion16Bits Ibyte;
	
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_DI_TOGGLING_COUNT_OBJ_INDEX,
									0);
	#if MAO_DISSABLE	
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_EPO);
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI2);
	CanPkg.tUnionData.u8Data[2] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[3] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI3);
	CanPkg.tUnionData.u8Data[4] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI4);
	CanPkg.tUnionData.u8Data[6] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[7] = Ibyte.u8[1];
	#endif
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	#if 0
	char	str[100];
	sprintf(str, "Test = %d", Ibyte.u16);
	appSerialCanDavinciSendTextMessage(str);
	#endif
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_DI_TOGGLING_COUNT_OBJ_INDEX,
									1);
	#if MAO_DISSABLE
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI5);
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI6);
	CanPkg.tUnionData.u8Data[2] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[3] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI7);
	CanPkg.tUnionData.u8Data[4] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI8);
	CanPkg.tUnionData.u8Data[6] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[7] = Ibyte.u8[1];
	#endif
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_DI_TOGGLING_COUNT_OBJ_INDEX,
									2);
	#if MAO_DISSABLE	
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI9);
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI10);
	CanPkg.tUnionData.u8Data[2] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[3] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI11);
	CanPkg.tUnionData.u8Data[4] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI12);
	CanPkg.tUnionData.u8Data[6] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[7] = Ibyte.u8[1];
	#endif
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_DI_TOGGLING_COUNT_OBJ_INDEX,
									3);
	#if MAO_DISSABLE
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI13);
	CanPkg.tUnionData.u8Data[0] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI14);
	CanPkg.tUnionData.u8Data[2] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[3] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI15);
	CanPkg.tUnionData.u8Data[4] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Ibyte.u8[1];
	Ibyte.u16 = apiSignalFeedbackGetTogglingCount(APP_SIGNAL_ID_DI16);
	CanPkg.tUnionData.u8Data[6] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[7] = Ibyte.u8[1];
	#endif
	CanPkg.u8Dlc = 8;
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyBaseEkfFccSoc(void){
  tHalCanFrame	CanPkg;
  tUnion32Bits Lbyte;
  tUnion16Bits Ibyte;
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_EKF_OBJ_INDEX,
									SMP_BASE_EKF_SYS_FCC_SOC_SUB_INDEX);
	CanPkg.u8Dlc = 6;
#if MAO_DISSABLE
  appBmsGetEkfRackFcc(0, &Lbyte.u32);
#endif
  LibFifoMemcpy(&CanPkg.tUnionData.u8Data[0], &Lbyte.u8[0], sizeof(Lbyte));	
  #if MAO_DISSABLE	
	appBmsGetEkfRackSoc(0, &Ibyte.u16);
	#endif
  LibFifoMemcpy(&CanPkg.tUnionData.u8Data[4], &Ibyte.u8[0], sizeof(Ibyte));		
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyBaseEkfSysMinRmDhgCap(void){
  tHalCanFrame	CanPkg;
  tUnion32Bits Lbyte;
  tUnion16Bits Ibyte;
  
  CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_EKF_OBJ_INDEX,
									SMP_BASE_EKF_MIN_DHG_CAP_SUB_INDEX);
	CanPkg.u8Dlc = 6;
	#if MAO_DISSABLE
  appBmsGetEkfRackDhgRemainCapacity(0, &Lbyte.u32);
  LibFifoMemcpy(&CanPkg.tUnionData.u8Data[0], &Lbyte.u8[0], sizeof(Lbyte));
  appBmsGetEkfSysMinDhgRcCellId(0, &Ibyte.u16);
  LibFifoMemcpy(&CanPkg.tUnionData.u8Data[4], &Ibyte.u8[0], sizeof(Ibyte));		
  #endif
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void notifyBaseEkfSysMinRmChgCap(void){
  tHalCanFrame	CanPkg;
  tUnion32Bits Lbyte;
  tUnion16Bits Ibyte;
  
  CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
									SMP_BASE_EKF_OBJ_INDEX,
									SMP_BASE_EKF_MIN_CHG_CAP_SUB_INDEX);
	CanPkg.u8Dlc = 6;
	#if MAO_DISSABLE
  appBmsGetEkfRackChgRemainCapacity(0, &Lbyte.u32);
  LibFifoMemcpy(&CanPkg.tUnionData.u8Data[0], &Lbyte.u8[0], sizeof(Lbyte));
  appBmsGetEkfSysMinChgRcCellId(0, &Ibyte.u16);
  LibFifoMemcpy(&CanPkg.tUnionData.u8Data[4], &Ibyte.u8[0], sizeof(Ibyte));		
  #endif
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}



static void notifyBaseInfoPackage(void)
{
	notifyBaseScuId();	//1
	notifyBaseSystemFlag();	//3
	notifyBaseRmQmax();		//1
	notifyBaseCurrent();	//1
	notifyBaseFCC();		//1
}
static void notifyBaseInfoPackage2(void)
{
	notifyBaseIrValue();        //1
	notifyBaseVBatVoltage();	//2
	notifyBaseMinMaxValue();	//2
	notifyBaseEkfFccSoc();  //1
	notifyBaseEkfSysMinRmDhgCap(); //1
	notifyBaseEkfSysMinRmChgCap(); //1
}
static void notifyBaseInfoPackage3(void)
{
//  notifyBaseScuTemp();		//1
	/// [CH] : For RSOC、SOH
	notifyBaseQStartPackage();	//1
//	notifyBaseRamSocPackage();	//1
//	notifyBasePassCharge();		//1
	notifyBaseCycleCount();		//1
	notifyAccPower();			//1
	notifyAfeStatus();			//1
	notifyPcsBmsSysyemStatus(); //1
	notifyDiTogglingCount();    //4
	
//	notifyNextFunction();
}


/// [CH] : Cell Voltage
static uint8_t appSerialCanDavinciNotificationCellVoltage(void)
{
	tUnion16Bits		Ibyte;
	tUnion16Bits		DiffV;
	tUnion16Bits		BaseV;
	tUnion16Bits		CellV;
	uint8_t		DatIndex = 0;
	uint8_t 	ValidCnt = 0;
	int8_t		ArrayDiffV[6] = {0};
	bool 			MbmsBalanceFlag = false;
	
	tHalCanFrame	CanPkg;
	
	
	if (NotificationSubIndex >= notifyCellNumber())
	{
		notifyNextFunction();
		return 0;	
	}
	
	if(apiSysParGetSystemActiveFlag() & (SYS_ACTIVE_FLAG_CELL_BALANCE_BY_MBMS | SYS_ACTIVE_FLAG_BALANCE_BY_SOC)){
		MbmsBalanceFlag = true;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_CELL_VOLTAGE_COMPRESS_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	BaseV.u16 = HalAfeGetCellVoltage(NotificationSubIndex);
	if(MbmsBalanceFlag){
		if(appBalanceIsBalanceSet(NotificationSubIndex)){
			BaseV.u16 |= 1;
		}else{
			BaseV.u16 &= ~1;
		}
	}
	
	for (int i = 1; i < 7; i++){
		
		if(NotificationSubIndex + i >= notifyCellNumber()){
			break;
		}
		
		CellV.u16 = HalAfeGetCellVoltage(NotificationSubIndex + i);
		
		if(MbmsBalanceFlag){
			if(appBalanceIsBalanceSet(NotificationSubIndex + i)){
				CellV.u16 |= 1;
			}else{
				CellV.u16 &= ~1;
			}
		
		}
		
		DiffV.i16 = CellV.i16 - BaseV.i16;
		if(abs(DiffV.i16) > 127){
			break;
		}else{
			ArrayDiffV[ValidCnt] = (int8_t)DiffV.i16;
			ValidCnt++;
		}
	}
	
	if(ValidCnt > 2){
		CanPkg.tUnionData.u8Data[0] = BaseV.u8[0];
		CanPkg.tUnionData.u8Data[1] = BaseV.u8[1];
		LibFifoMemcpy(&CanPkg.tUnionData.u8Data[2], ArrayDiffV, ValidCnt);		
		NotificationSubIndex += ValidCnt+1;
		#if 0
		SEGGER_RTT_SetTerminal(1);
		SEGGER_RTT_printf(0, RTT_CTRL_TEXT_BRIGHT_GREEN "\r\nVolt CAN DLC %d Next Notification Index %d", ValidCnt+2, NotificationSubIndex);
		#endif
		CanPkg.u8Dlc = ValidCnt+2;
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
		return 1;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_CELL_VOLTAGE_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyCellNumber())
			break;
		
		Ibyte.u16 = HalAfeGetCellVoltage(NotificationSubIndex);
		if(MbmsBalanceFlag){
			if(appBalanceIsBalanceSet(NotificationSubIndex)){
				Ibyte.u16 |= 1;
			}else{
				Ibyte.u16 &= ~1;
			}
		
		}
		
		NotificationSubIndex++;
		
		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[0];
		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[1];
	}
	#if 0
	SEGGER_RTT_SetTerminal(1);
	SEGGER_RTT_printf(0, RTT_CTRL_TEXT_BRIGHT_RED "\r\nVolt CAN DLC %d Next Notification Index %d", DatIndex, NotificationSubIndex);
	#endif
	CanPkg.u8Dlc = DatIndex;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
			
	return 1;
}

/// [CH] : NTC Voltage
static uint8_t appSerialCanDavinciNotificationNtcVoltage(void)
{
	tUnion16Bits		Ibyte;
	tUnion16Bits		BaseV;
	tUnion16Bits		DiffV;
	uint8_t		DatIndex = 0;
	uint8_t 	ValidCnt = 0;
	int8_t		ArrayDiffV[6] = {0};
	tHalCanFrame	CanPkg;

	
	if (NotificationSubIndex >= notifyNtcNumber())
	{
		notifyNextFunction();
		return 0;	
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_NTC_VOLTAGE_COMPRESS_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	BaseV.u16 = HalAfeGetNtcVoltage(NotificationSubIndex);
	for (int i = 1; i < 7; i++){
		if(NotificationSubIndex + i >= notifyNtcNumber()){
			break;
		}
		
		DiffV.i16 = HalAfeGetNtcVoltage(NotificationSubIndex + i) - BaseV.i16;
		if( abs(DiffV.i16) > 127){
			break;
		}else{
			ArrayDiffV[ValidCnt] = (int8_t)DiffV.i16;
			ValidCnt++;
		}
	}
	
	if(ValidCnt > 2){
		CanPkg.tUnionData.u8Data[0] = BaseV.u8[0];
		CanPkg.tUnionData.u8Data[1] = BaseV.u8[1];
		LibFifoMemcpy(&CanPkg.tUnionData.u8Data[2], ArrayDiffV, ValidCnt);		
		NotificationSubIndex += ValidCnt+1;
		#if 0
		SEGGER_RTT_SetTerminal(2);
		SEGGER_RTT_printf(0, RTT_CTRL_TEXT_BRIGHT_GREEN "\r\nNTC CAN DLC %d Next Notification Index %d", ValidCnt+2, NotificationSubIndex);
		#endif
		CanPkg.u8Dlc = ValidCnt+2;
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
		return 1;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_CELL_NTC_VOLTAGE_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);

	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyNtcNumber())
			break;
		
		Ibyte.u16 = HalAfeGetNtcVoltage(NotificationSubIndex++);		
		
		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[0];
		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[1];
	}
	#if 0
	SEGGER_RTT_SetTerminal(2);
	SEGGER_RTT_printf(0, RTT_CTRL_TEXT_BRIGHT_RED "\r\nNTC CAN DLC %d NEXT Notification Index %d", DatIndex, NotificationSubIndex);
	#endif
	CanPkg.u8Dlc = DatIndex;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	return 1;
}

/// [CH] : OVP Flag
static uint8_t	appSerialCanDavinciNotificationOvpFlag(void)
{
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;
	
	
	if ((NotificationDetailEnableFlag == 0) || 
		(NotificationSubIndex >= notifyCellNumber()))
	{
		notifyNextFunction();
		return 0;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_OVP_FLAG_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
		
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyCellNumber())
			break;
		
		CanPkg.tUnionData.u8Data[DatIndex] = ApiProtectOvpGetFlag(NotificationSubIndex);
		
		if (appBalanceIsBalanceSet(NotificationSubIndex))
			CanPkg.tUnionData.u8Data[DatIndex] |= 0x80;
		
		NotificationSubIndex++;
		DatIndex++;
	}
		
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	return 1;
}

/// [CH] : UVP Flag
static uint8_t appSerialCanDavinciNotificationUvpFlag(void)
{
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;
	
	
	if ((NotificationDetailEnableFlag == 0) || 
		(NotificationSubIndex >= notifyCellNumber()))
	{
		notifyNextFunction();
		return 0;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_UVP_FLAG_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyCellNumber())
			break;
		
		CanPkg.tUnionData.u8Data[DatIndex++] = ApiProtectUvpGetFlag(NotificationSubIndex++);
	}
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	return 1;

}

/// [CH] : COTP Flag
static uint8_t appSerialCanDavinciNotificationCotpFlag(void)
{
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;
	
	
	if ((NotificationDetailEnableFlag == 0) || 
		(NotificationSubIndex >= notifyNtcNumber()))
	{
		notifyNextFunction();
		return 0;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_COTP_FLAG_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyNtcNumber())
			break;
		
		CanPkg.tUnionData.u8Data[DatIndex++] = ApiProtectCotpGetFlag(NotificationSubIndex++);
	}
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	return 1;
}

/// [CH] : CUTP Flag
static uint8_t appSerialCanDavinciNotificationCutpFlag(void)
{
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;
	
	
	if ((NotificationDetailEnableFlag == 0) || 
		(NotificationSubIndex >= notifyNtcNumber()))
	{
		notifyNextFunction();
		return 0;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_CUTP_FLAG_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyNtcNumber())
			break;
		
		CanPkg.tUnionData.u8Data[DatIndex++] = ApiProtectCutpGetFlag(NotificationSubIndex++);
	}
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	return 1;
}

/// [CH] : DOTP Flag
static uint8_t appSerialCanDavinciNotificationDotpFlag(void)
{
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;
	
	
	if ((NotificationDetailEnableFlag == 0) || 
		(NotificationSubIndex >= notifyNtcNumber()))
	{
		notifyNextFunction();
		return 0;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_DOTP_FLAG_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{		
		if (NotificationSubIndex >= notifyNtcNumber())
			break;
		
		CanPkg.tUnionData.u8Data[DatIndex++] = ApiProtectDotpGetFlag(NotificationSubIndex++);
	}
		
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	return 1;
}	

/// [CH] : DUTP Flag
static uint8_t appSerialCanDavinciNotificationDutpFlag(void)
{
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;
	
	
	if ((NotificationDetailEnableFlag == 0) || 
		(NotificationSubIndex >= notifyNtcNumber()))
	{
		notifyNextFunction();
		return 0;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_DUTP_FLAG_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyNtcNumber())
			break;
		
		CanPkg.tUnionData.u8Data[DatIndex++] = ApiProtectDutpGetFlag(NotificationSubIndex++);
	}
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	return 1;
}

/// [CH] : Pack Voltage
static uint8_t appSerialCanDavinciNotificationPackVoltage(void)
{
	tUnion16Bits		PackVoltage;
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;

	
	if (NotificationSubIndex >= notifyPackNumber())
	{
		notifyNextFunction();
		return 0;	
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_PACK_VOLTAGE_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyPackNumber())
			break;
		#if MAO_DISSABLE
		HalAfeGetPackVoltage(NotificationSubIndex++, &PackVoltage.u16);
		#endif
		CanPkg.tUnionData.u8Data[DatIndex++] = PackVoltage.u8[0];
		CanPkg.tUnionData.u8Data[DatIndex++] = PackVoltage.u8[1];
	}	
		
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
			
	return 1;
}

/// [CH] : Cell Busbar Voltage
static uint8_t appSerialCanDavinciNotificationCellBusbarVoltage(void)
{
	tUnion16Bits		Ibyte;
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;

	
	if (NotificationSubIndex >= notifyCellBusbarNumber())
	{
		notifyNextFunction();
		return 0;	
	}

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
							SMP_DETAIL_CELL_BUSBAR_VOLTAGE_OBJ_INDEX,
							NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
		
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyCellBusbarNumber())
			break;
		
		Ibyte.u16 = HalAfeGetCellBusbarVoltage(NotificationSubIndex++);

		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[0];
		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[1];
	}
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
			
	return 1;
}

/// [CH] : NTC Busbar Voltage 
static uint8_t appSerialCanDavinciNotificationNtcBusbarVoltage(void)
{
	tUnion16Bits		Ibyte;
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;

	
	if (NotificationSubIndex >= notifyNtcBusbarNumber())
	{
		notifyNextFunction();
		return 0;	
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_BUSBAR_NTC_VOLTAGE_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyNtcBusbarNumber())
			break;
		
		Ibyte.u16 = HalAfeGetNtcBusbarVoltage(NotificationSubIndex++);

		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[0];
		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[1];
	}
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
			
	return 1;
}

/// [CH] : NTC Ambient Voltage 
static uint8_t appSerialCanDavinciNotificationNtcAmbientVoltage(void)
{
	tUnion16Bits		Ibyte;
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;
	
	
	if (NotificationSubIndex >= notifyNtcAmbientNumber())
	{
		notifyNextFunction();
		return 0;	
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_AMBIENT_NTC_VOLTAGE_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyNtcAmbientNumber())
			break;
		
		Ibyte.u16 = HalAfeGetNtcAmbientVoltage(NotificationSubIndex++);

		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[0];
		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[1];
	}
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
			
	return 1;
}

/// [CH] : NTC Other Voltage 
static uint8_t appSerialCanDavinciNotificationNtcOtherVoltage(void)
{
	tUnion16Bits		Ibyte;
	uint8_t		DatIndex = 0;
	tHalCanFrame	CanPkg;
	
	
	if (NotificationSubIndex >= notifyNtcOtherNumber())
	{
		notifyNextFunction();
		return 0;	
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_OTHER_NTC_VOLTAGE_OBJ_INDEX,
									NotificationSubIndex);
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (DatIndex < 8)
	{
		if (NotificationSubIndex >= notifyNtcOtherNumber())
			break;
		
		Ibyte.u16 = HalAfeGetNtcOtherVoltage(NotificationSubIndex++);

		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[0];
		CanPkg.tUnionData.u8Data[DatIndex++] = Ibyte.u8[1];
	}
	
	CanPkg.u8Dlc = 8;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
			
	return 1;
}

// =============================================================================

/// [CH] : Cell EKF SOC
static uint8_t appSerialCanDavinciNotificationCellEkfSoc(void)
{
	tUnion16Bits		mIbyte;
	uint8_t		u8DatIndex = 0;
	
	tUnion16Bits		mDiffEkfSoc;
	tUnion16Bits		mBaseEkfSoc;
	tUnion16Bits		mCellEkfSoc;
	uint8_t 	u8ValidCnt = 0;
	int8_t		i8ArrayDiffEkfSoc[6] = {0};
	
	tHalCanFrame	CanPkg;
	
	
	/// [CH] : 如果已經掃完全部Cell
	if (NotificationSubIndex >= notifyCellNumber())
	{
		notifyNextFunction();
		return 0;	
	}
	
	/// [CH] : 設定CAN ID
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_EKF_SOC_COMPRESS_OBJ_INDEX,
									NotificationSubIndex);
	
	/// [CH] : 先清掉Can Data
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	/// [CH] : 獲得Base EKF SOC
	#if MAO_DISSABLE
	appBmsGetEkfSoc(appBmsGetScuId(), NotificationSubIndex, &mBaseEkfSoc.u16);
	#endif
	
	/// [CH] : Delta protocol (1 ~ 6)，由Base往後算6個
	for (int i = 1; i < 7; i++)
	{
		/// [CH] : 如果這次的Delta數量會 >= Cell數目 
		if (NotificationSubIndex + i >= notifyCellNumber())
		{
			break;
		}
		
		/// [CH] : 獲得Cell EKF SOC
		#if MAO_DISSABLE
		appBmsGetEkfSoc(appBmsGetScuId(), NotificationSubIndex + i, &mCellEkfSoc.u16);
		#endif
		
		/// [CH] : 計算Delta EKF SOC
		mDiffEkfSoc.i16 = mCellEkfSoc.i16 - mBaseEkfSoc.i16;
		
		#if 0
		SEGGER_RTT_SetTerminal(1);
		SEGGER_RTT_printf(0, RTT_CTRL_TEXT_BRIGHT_GREEN "u8ValidCnt = %d, NotificationSubIndex = %d, abs(mDiffEkfSoc.i16) = %d\r\n", u8ValidCnt, NotificationSubIndex, abs(mDiffEkfSoc.i16));
		#endif
		
		/// [CH] : 如果其中一個Delta EKF SOC > 127
		if (abs(mDiffEkfSoc.i16) > 127)
		{
			break;
		}
		/// [CH] : 如果Delta EKF SOC <= 127
		else
		{
			/// [CH] : 將Delta EKF SOC放入矩陣
			i8ArrayDiffEkfSoc[u8ValidCnt] = (int8_t)mDiffEkfSoc.i16;
			
			/// [CH] : Counter++
			u8ValidCnt ++;
		}
	}
	
	/// [CH] : 如果Counter > 2，超過3個Delta就送
	/// [CH] : 即因為舊的協議一筆封包最多是送四筆電壓數值，代表如果一樣也是一筆封包送四筆電壓數值，會優先以Delta格式為主
	if (u8ValidCnt > 2)
	{
		/// [CH] : 前兩個Byte放Base EKF SOC
		CanPkg.tUnionData.u8Data[0] = mBaseEkfSoc.u8[0];
		CanPkg.tUnionData.u8Data[1] = mBaseEkfSoc.u8[1];
		
		/// [CH] : 複製 6個Byte
		LibFifoMemcpy(&CanPkg.tUnionData.u8Data[2], i8ArrayDiffEkfSoc, u8ValidCnt);	

		/// [CH] : 跳Base(1) + Delta
		NotificationSubIndex += u8ValidCnt + 1;
		
		/// [CH] : 設定CAN封包長度
		CanPkg.u8Dlc = u8ValidCnt + 2;
		
		/// [CH] : 送Can封包出去
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
		return 1;
	}
	
	/// [CH] : 以下是非Delta型態的Protocol
	
	/// [CH] : 設定CAN ID
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_EKF_SOC_OBJ_INDEX,
									NotificationSubIndex);
	
	/// [CH] : 先清掉Can Data
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	/// [CH] : 掃8個Byte
	while (u8DatIndex < 8)
	{
		/// [CH] : 如果已經掃完全部Cell
		if (NotificationSubIndex >= notifyCellNumber())
			break;
		
		/// [CH] : 獲得Cell之EKF SOC
		#if MAO_DISSABLE
		appBmsGetEkfSoc(appBmsGetScuId(), NotificationSubIndex, &mIbyte.u16);
		#endif
		NotificationSubIndex++;
		
		/// [CH] : 先Low Byte
		CanPkg.tUnionData.u8Data[u8DatIndex++] = mIbyte.u8[0];
		
		/// [CH] : 再High Byte
		CanPkg.tUnionData.u8Data[u8DatIndex++] = mIbyte.u8[1];
	}
	
	/// [CH] : 設定CAN封包長度
	CanPkg.u8Dlc = u8DatIndex;
	
	/// [CH] : 送CAN封包出去
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
			
	return 1;
}

// =============================================================================

/// [CH] : Cell EKF PCOV1
static uint8_t appSerialCanDavinciNotificationCellEkfPcov1(void)
{
	tHalCanFrame	CanPkg;
	uint8_t		u8DatIndex = 0;
	#if MAO_DISSABLE
	ekf_type	etEkfPcov1;	
	#endif
	
	if (NotificationPov1SubIndex >= notifyCellNumber())
	{
		NotificationPov1SubIndex = 0;
		notifyNextFunction();
		return 0;
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_EKF_POV1_OBJ_INDEX,
									NotificationPov1SubIndex);
	
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (u8DatIndex < 8)
	{
		if (NotificationPov1SubIndex >= notifyCellNumber())
			break;
		#if MAO_DISSABLE
		appBmsGetEkfPcov1(appBmsGetScuId(), NotificationPov1SubIndex, &etEkfPcov1);
		#endif
		NotificationPov1SubIndex++;
		#if MAO_DISSABLE
		CanPkg.tUnionData.u8Data[u8DatIndex++] = AlgAppCvtPov1ToByte(etEkfPcov1);
		#else
		u8DatIndex++;
		#endif
	}
	
	CanPkg.u8Dlc = u8DatIndex;
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	if((NotificationPov1SubIndex % POV1_CANBUS_SEND_NUM_ONE_CYCLE) == 0)
		notifyNextFunction();
	
	return (PUT_MAX_PKG_NUM); 
}

// =============================================================================

/// [CH] : Cell EKF PCOV2
static uint8_t appSerialCanDavinciNotificationCellEkfPcov2(void)
{
	tHalCanFrame	CanPkg;
	uint8_t		u8DatIndex = 0;
	uint16_t u16Pov = 0;
	tUnion16Bits mIbytePov;
	#if MAO_DISSABLE
	ekf_type	etEkfPcov2;
	#endif

	
	if (NotificationPov2SubIndex >= notifyCellNumber())
	{
		NotificationPov2SubIndex = 0;
		notifyNextFunction();
		return 0;	
	}
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DETAIL_TX, notifyScuId(),
									SMP_DETAIL_EKF_POV2_OBJ_INDEX,
									NotificationPov2SubIndex);
	
	memset(&CanPkg.tUnionData.u8Data, 0, 8);
	
	while (u8DatIndex < 8)
	{
		if (NotificationPov2SubIndex >= notifyCellNumber())
			break;
		#if MAO_DISSABLE
		appBmsGetEkfPcov2(appBmsGetScuId(), NotificationPov2SubIndex, &etEkfPcov2);
		#endif
		NotificationPov2SubIndex++;
		#if MAO_DISSABLE
		mIbytePov.u16 = AlgAppCvtPov2ToWord(etEkfPcov2);
		#endif
		CanPkg.tUnionData.u8Data[u8DatIndex] = mIbytePov.u8[0];
		CanPkg.tUnionData.u8Data[u8DatIndex+1] = mIbytePov.u8[1];
		u8DatIndex += 2;
	}
	
	CanPkg.u8Dlc = u8DatIndex;
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	
	if((NotificationPov2SubIndex % POV2_CANBUS_SEND_NUM_ONE_CYCLE) == 0)
		notifyNextFunction();
	
	return (PUT_MAX_PKG_NUM);
}

// =============================================================================

/// [CH] : End
static uint8_t appSerialCanDavinciNotificationEnd(void)
{
	static uint8_t sn_num = 0;
	tHalCanFrame	CanPkg;

	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, notifyScuId(),
								SMP_BASE_DETAIL_MSG_SEND_END_OBJ_INDEX,
							   notifyScuId());
	
	DavinciCanFunBaseSetDetailSnID(0);
	
	CanPkg.u8Dlc = 0;
	for(int i=0; i <5;i++){
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}
	LastBroadcastBmuId = notifyScuId();

	CanDavinciNotificationFunIndex = 0;
	NotificationSubIndex = 0;

	return 0;
}

/// [CH] : 10ms broadcast
const tNotificationRunTable	NotificationFunctionTable[]={
	notifyFunNone,
	
//	notifyBaseInfoPackage,
	appSerialCanDavinciNotificationCellVoltage,
	appSerialCanDavinciNotificationNtcVoltage,
	
	appSerialCanDavinciNotificationOvpFlag,
	appSerialCanDavinciNotificationUvpFlag,
	appSerialCanDavinciNotificationCotpFlag,
	appSerialCanDavinciNotificationCutpFlag,
	appSerialCanDavinciNotificationDotpFlag,
	appSerialCanDavinciNotificationDutpFlag,
	
	appSerialCanDavinciNotificationPackVoltage,
	
	appSerialCanDavinciNotificationCellBusbarVoltage,
	appSerialCanDavinciNotificationNtcBusbarVoltage,
	appSerialCanDavinciNotificationNtcAmbientVoltage,
	appSerialCanDavinciNotificationNtcOtherVoltage,
	
	appSerialCanDavinciNotificationCellEkfSoc,
	appSerialCanDavinciNotificationCellEkfPcov1,
	appSerialCanDavinciNotificationCellEkfPcov2,
	appSerialCanDavinciNotificationEnd
	
};

static void StartBroadcastDetailMessage(void)
{
	CanDavinciNotificationFunIndex = 1;
	NotificationSubIndex = 0;
}
static uint16_t	T1secCount = 0;

void canNotiHwTimerHandler(__far void *dest, uint16_t evt, void *vDataPtr)
{
	static uint8_t T1msCount =0;
	T1msCount++;
	if(T1msCount >= 10)
	{ 
		T1msCount = 0;
		if(T1secCount < 0xfff0){
			T1secCount++;			
		}
	}
}

#define	_NOTI_DISABLE_SECOND	5
static uint8_t notificationDisableCount = 0;
static void resetNotificationDisableCount(void)
{
	notificationDisableCount = _NOTI_DISABLE_SECOND;
	
}
static void checkNotificationDisableCount(void)
{
	if(notificationDisableCount)
	{
		notificationDisableCount--;
		if(notificationDisableCount == 0)
			appSerialCanDavinciNotificationDisableDetailData();
	}
}
/* Public function prototypes -----------------------------------------------*/
uint8_t appSerialCanDavinciNotificationGetLastBroadcastScuId(void)
{
	return LastBroadcastBmuId;
}
void appSerialCanDavinciNotificationSetLastBroadcastScuId(uint8_t scuid)
{
	LastBroadcastBmuId = scuid;
}

void appSerialCanDavinciNotificationEnableDetailData(void){

	resetNotificationDisableCount();
	NotificationDetailEnableFlag = 1;
}

void appSerialCanDavinciNotificationDisableDetailData(void){

	NotificationDetailEnableFlag = 0;
}

void appSerialCanDavinciNotificationHandler(uint16_t evt)
{
	static	uint8_t	count = 0;
	uint8_t	pkgnum = 0;
	
	
	if (CanDavinciNotificationFunIndex)
	{	
		//if (notifyGetFreeFifoSize() >= (BASIC_PKG_NUM + PUT_MAX_PKG_NUM))
		{
			while (CanDavinciNotificationFunIndex)
			{
				pkgnum += NotificationFunctionTable[CanDavinciNotificationFunIndex]();
				
				if (pkgnum >= PUT_MAX_PKG_NUM)
					break;
			}
		}
	}
	
	#define		NOTI_1_SEC_COUNT	100
	count++;
	if(count == (NOTI_1_SEC_COUNT-2))
	{
		if(notifyScuId() != 0xff)
		{
			notifyBaseInfoPackage();
		}
	}
	else if(count == (NOTI_1_SEC_COUNT-1))
	{
		if(notifyScuId() != 0xff)
		{
			notifyBaseInfoPackage2();
		}
	}
	else if(count >= NOTI_1_SEC_COUNT)
	{
		count = 0;
		if(notifyScuId() != 0xff)
		{
			notifyBaseInfoPackage3();
		}
		checkNotificationDisableCount();
				
#ifndef WAIT_LAST_SCU_BROCAST_FINISH		
		if(!CanDavinciNotificationFunIndex)
		{
			StartBroadcastDetailMessage();	
		}
#endif		
	}
}



/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    


