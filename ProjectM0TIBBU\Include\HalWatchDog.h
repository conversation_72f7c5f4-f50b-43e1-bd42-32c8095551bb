/*
******************************************************************************
* @file     HalWatchDog.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HALWATCHDOG_H__
#define __HALWATCHDOG_H__

#ifdef __cplusplus
extern "C" {
#endif
/* Includes -----------------------------------------------------------------*/
#include "Main.h"
/* Global define ------------------------------------------------------------*/
#define HALWDT_WWDT0                   WWDT0
/* Global typedef -----------------------------------------------------------*/
typedef enum { 
    kHAL_WDT_TIMEOUT_500MS = 0,
    kHAL_WDT_TIMEOUT_1000MS
} eTypeHalWdtTimeout;

typedef WWDT_Regs tHalWWDTHandle;
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void HalWdtInit(tHalWWDTHandle *pWdt, eTypeHalWdtTimeout mTimeout);
void HalWdtFeedDog(tHalWWDTHandle *pWdt);
void HalWdtStart(tHalWWDTHandle *pWdt);
void HalWdtStop(tHalWWDTHandle *pWdt);
bool HalIsWdtReset(tHalWWDTHandle *pWdt);

#ifdef __cplusplus
}
#endif

#endif