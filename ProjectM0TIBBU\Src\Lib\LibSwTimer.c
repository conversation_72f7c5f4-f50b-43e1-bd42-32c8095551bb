/*
******************************************************************************
* @file     LibSwTimer.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include "LibFunctionPointerRegister.h"
#include "LibSoftwareTimerHandler.h"

//#include <stdbool.h>
/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define LIB_SWTIMER_10MS_CNT    (10)
#define LIB_SWTIMER_100MS_CNT   (100)
#define LIB_SWTIMER_500MS_CNT   (500)
#define LIB_SWTIMER_1000MS_CNT  (1000)
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint16_t count1ms = 0;
static uint16_t count1s = 0;
static uint16_t delayCount = 0;


static tLibRegister EvtHandlerRegister={0}, EvtHandlerRegisterTask={0};
/* Private function prototypes -----------------------------------------------*/
uint16_t LibGetSwTimer(void)
{
	return count1ms;
}

void LibSwTimerClearCount(void)
{
	count1ms = 0;
	count1s = 0;
	delayCount = 0;
}


tErrCode LibSwTimerOpen(tLibSwTimerEvtHandler handler, __far void *dest){
static bool initFlag = false; 
  if(LibRegisterIsMemberNull(&EvtHandlerRegister) == true){
	  if(initFlag == true){
          count1ms = 0;
          count1s = 0;
	  }
  }
  
  if(initFlag == false){
	  initFlag = true;  
  }
  return LibRegisterAdd(&EvtHandlerRegister, handler, dest);
}

tErrCode LibSwTimerClose(tLibSwTimerEvtHandler handler, __far void *dest){
  return LibRegisterRm(&EvtHandlerRegister, handler, dest);
}

void LibSwTimerHwHandler(tLibSwTimerEvt evt, __far void *data){
  if(evt == kLIB_SW_TIMER_EVT_HW_1MS){
    count1ms++;
    if(delayCount > 0){
      delayCount--;
    }
  }
}

void LibSwTimerHwDelay(uint16_t ms){
 //   uint32_t delayTimeout = 300000 
    delayCount = ms;
    while(delayCount >0);
}

void LibSWTimerHandler(void)
{     
    const uint16_t  u16Evt10MsTable[]={
        kLIB_SW_TIMER_EVT_10_0_MS,
        kLIB_SW_TIMER_EVT_10_1_MS,
        kLIB_SW_TIMER_EVT_10_2_MS,
        kLIB_SW_TIMER_EVT_10_3_MS,
        kLIB_SW_TIMER_EVT_10_4_MS,
        kLIB_SW_TIMER_EVT_10_5_MS,
        kLIB_SW_TIMER_EVT_10_6_MS,
        kLIB_SW_TIMER_EVT_10_7_MS,
        kLIB_SW_TIMER_EVT_10_8_MS,
        kLIB_SW_TIMER_EVT_10_9_MS        
    }; 
    uint16_t    u16EvtFlag;
      
    if(count1ms ==0)
        return;
	count1ms = 0;
	
	if(LibRegisterIsMemberNull(&EvtHandlerRegister) == true)
	{
		return;
	}
    u16EvtFlag = u16Evt10MsTable[count1s % LIB_SWTIMER_10MS_CNT];
    u16EvtFlag |= kLIB_SW_TIMER_EVT_1_MS;

	count1s++;
	if(count1s%100 == 0){
	    u16EvtFlag |= kLIB_SW_TIMER_EVT_100_MS;
	}
	if(count1s%500 == 0){
	    u16EvtFlag |= kLIB_SW_TIMER_EVT_500_MS;
	} 
	if(count1s >= 1000){
	    u16EvtFlag |= kLIB_SW_TIMER_EVT_1_S;
		count1s -= 1000;
	}
    LibRegisterTypeHandlerExe(&EvtHandlerRegister, u16EvtFlag, 0);
}

tErrCode LibSwTimerTaskOpen(tLibSwTimerEvtHandler handler, __far void *dest){
  return LibRegisterAdd(&EvtHandlerRegisterTask, handler, dest);
}

tErrCode LibSwTimerTaskClose(tLibSwTimerEvtHandler handler, __far void *dest){
  return LibRegisterRm(&EvtHandlerRegisterTask, handler, dest);
}
