/*
******************************************************************************
* @file     ApiProtect.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/


/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "Main.h"
#include "ApiProtect.h"
#include "LibSysPar.h"
#include "HalAfe.h"
#include "LibSoftwareTimerHandler.h"
#include "ApiProtectOvp.h"
#include "ApiProtectUvp.h"
#include "ApiProtectCotp.h"
#include "ApiProtectCutp.h"
#include "ApiProtectDotp.h"
#include "ApiProtectDutp.h"
#include "ApiProtectDocp.h"

/* Private define ------------------------------------------------------------*/
#define	API_PROTECT_INTERVAL_10MS	50
#define	API_PROTECT_IS_IN_SIMU_MODE()	(1)	//appProjectIsInSimuMode
#define	API_PROTECT_GET_AFE_STATE()	(kAFE_STATE_NORMAL)	//halAfeGetState

//#define	API_PROTECT_USE_TEMP_VALUE_COMPARE	

#define	API_PROTECT_MAX_FP_BUF_SIZE		50
/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef void (* tfpProtectRunTable)(void);

/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint8_t	gu8ProtectRunTableIndex = API_PROTECT_MAX_FP_BUF_SIZE;
static uint8_t	gu8ProtectFunctionTableIndex = 0;
static tfpProtectRunTable	gmfpProtectRunTable[API_PROTECT_MAX_FP_BUF_SIZE]={0};


/* Private function prototypes -----------------------------------------------*/
static uint8_t ApiProtectAddToProtectTable(tfpProtectRunTable fpProtectFun)
{
	if(gu8ProtectFunctionTableIndex >= API_PROTECT_MAX_FP_BUF_SIZE)
		return 0;
	gmfpProtectRunTable[gu8ProtectFunctionTableIndex++] = fpProtectFun;	
	return 1;
}
static void ApiProtectGoToNextProtectFunction(void)
{
	gu8ProtectRunTableIndex++;
	
	if(gu8ProtectRunTableIndex >= gu8ProtectFunctionTableIndex)
		gu8ProtectRunTableIndex = API_PROTECT_MAX_FP_BUF_SIZE;
}

static void ApiProtectNone(void)
{
	ApiProtectGoToNextProtectFunction();
}
static void ApiProtectOvp_L1(void)
{
	if(ApiProtectOvpHandler(0))
		ApiProtectGoToNextProtectFunction();
}
static void ApiProtectOvp_L2(void)
{
	if(ApiProtectOvpHandler(1))
		ApiProtectGoToNextProtectFunction();
}

static void ApiProtectOvp_L3(void)
{
	if(ApiProtectOvpHandler(2))
		ApiProtectGoToNextProtectFunction();
}
static void ApiProtectOvpPf(void)
{
	ApiProtectOvpPfHandler();
	ApiProtectGoToNextProtectFunction();
}
static void ApiProtectUvp_L1(void)
{
	if(ApiProtectUvpHandler(0))
		ApiProtectGoToNextProtectFunction();
}
static void ApiProtectUvp_L2(void)
{
	if(ApiProtectUvpHandler(1))
		ApiProtectGoToNextProtectFunction();
}

static void ApiProtectUvp_L3(void)
{
	if(ApiProtectUvpHandler(2))
		ApiProtectGoToNextProtectFunction();
}
static void ApiProtectUvpPf(void)
{
	ApiProtectUvpPfHandler();
	ApiProtectGoToNextProtectFunction();
}
//---------------------------------
static void ApiProtectCotp_L1(void)
{
	if(ApiProtectCotpHandler(0) != 0)
		ApiProtectGoToNextProtectFunction();
}
static void ApiProtectCotp_L2(void)
{
	if(ApiProtectCotpHandler(1) != 0)
		ApiProtectGoToNextProtectFunction();
}

static void ApiProtectCotp_L3(void)
{
	if(ApiProtectCotpHandler(2) != 0)
		ApiProtectGoToNextProtectFunction();
}
//---------------------------------
//	cutp
static void ApiProtectCutp_L1(void)
{
	if(ApiProtectCutpHandler(0) != 0)
		ApiProtectGoToNextProtectFunction();
}
static void ApiProtectCutp_L2(void)
{
	if(ApiProtectCutpHandler(1) != 0)
		ApiProtectGoToNextProtectFunction();
}

static void ApiProtectCutp_L3(void)
{
	if(ApiProtectCutpHandler(2) != 0)
		ApiProtectGoToNextProtectFunction();
}
//---------------------------------
//	dotp
static void ApiProtectDotp_L1(void)
{
	if(ApiProtectDotpHandler(0) != 0)
		ApiProtectGoToNextProtectFunction();
}
static void ApiProtectDotp_L2(void)
{
	if(ApiProtectDotpHandler(1) != 0)
		ApiProtectGoToNextProtectFunction();
}

static void ApiProtectDotp_L3(void)
{
	if(ApiProtectDotpHandler(2) != 0)
		ApiProtectGoToNextProtectFunction();
}
//---------------------------------
//	dutp
static void ApiProtectDutp_L1(void)
{
	if(ApiProtectDutpHandler(0) != 0)
		ApiProtectGoToNextProtectFunction();
}
static void ApiProtectDutp_L2(void)
{
	if(ApiProtectDutpHandler(1) != 0)
		ApiProtectGoToNextProtectFunction();
}

static void ApiProtectDutp_L3(void)
{
	if(ApiProtectDutpHandler(2) != 0)
		ApiProtectGoToNextProtectFunction();
}
//---------------------------------
//	docp
static void ApiProtectDocp_L1(void)
{
	ApiProtectDocpHandler(0);
    ApiProtectGoToNextProtectFunction();
}
static void ApiProtectDocp_L2(void)
{
	ApiProtectDocpHandler(1);
	ApiProtectGoToNextProtectFunction();
}

static void ApiProtectDocp_L3(void)
{
	ApiProtectDocpHandler(2);
	ApiProtectGoToNextProtectFunction();
}
#if 0
//---------------------------------
//	cocp
static void ApiProtectCocp_L1(void)
{
	ApiProtectCocpHandler(0);
	ApiProtectGoToNextProtectFunction();
}
static void ApiProtectCocp_L2(void)
{
	ApiProtectCocpHandler(1);
	ApiProtectGoToNextProtectFunction();
}

static void ApiProtectCocp_L3(void)
{
	ApiProtectCocpHandler(2);
	ApiProtectGoToNextProtectFunction();
}

//---------------------------------

//	scuot
static void ApiProtectScuOt_L1(void)
{
	ApiProtectScuOtHandler(0);
	ApiProtectGoToNextProtectFunction();
}
static void ApiProtectScuOt_L2(void)
{
	ApiProtectScuOtHandler(1);
	ApiProtectGoToNextProtectFunction();
}

static void ApiProtectScuOt_L3(void)
{
	ApiProtectScuOtHandler(2);
	ApiProtectGoToNextProtectFunction();
}
//---------------------------------
static void ApiProtectDvp(void)
{
	ApiProtectDvpHandler();
	ApiProtectGoToNextProtectFunction();
}
//---------------------------------
static void ApiProtectDtp(void)
{
	ApiProtectDtpHandler();
	ApiProtectGoToNextProtectFunction();
}

//---------------------------------
static void ApiProtectIrUrp(void)
{
	ApiProtectIrUrpHandler();
	ApiProtectGoToNextProtectFunction();
}
//---------------------------------
static void ApiProtectMDvp(void)
{
	ApiProtectMDvpHandler();
	ApiProtectGoToNextProtectFunction();
}
//---------------------------------
static void ApiProtectDip(void)
{
	ApiProtectDipHandler();
	ApiProtectGoToNextProtectFunction();
}
//---------------------------------
static void ApiProtectVbDvp(void)
{
	ApiProtectVbDvpHandler();
	ApiProtectGoToNextProtectFunction();
}
//---------------------------------

static void ApiProtectCellOwp(void){
	ApiProtectCellOwpHandler();
	ApiProtectGoToNextProtectFunction();
}
#endif

static void ApiProtectEnd(void)
{
	gu8ProtectRunTableIndex	= API_PROTECT_MAX_FP_BUF_SIZE;
}
//---------------------------------
#if 0
const tfpProtectRunTable	gmfpProtectRunTable[]={
	ApiProtectNone,
#if	1	
	ApiProtectOvp_L1,
	ApiProtectOvp_L2,
	ApiProtectOvp_L3,
	ApiProtectOvPf,
#endif
#if	1	
	ApiProtectUvp_L1,
	ApiProtectUvp_L2,
	ApiProtectUvp_L3,
	ApiProtectUvpPf,
#endif	
#if	1
	ApiProtectCotp_L1,
	ApiProtectCotp_L2,
	ApiProtectCotp_L3,
#endif	
#if 1
	ApiProtectCutp_L1,
	ApiProtectCutp_L2,
	ApiProtectCutp_L3,
#endif
#if	1
	ApiProtectDotp_L1,
	ApiProtectDotp_L2,
	ApiProtectDotp_L3,
#endif	
#if 1
	ApiProtectDutp_L1,
	ApiProtectDutp_L2,
	ApiProtectDutp_L3,
#endif
#if 1
	ApiProtectCocp_L1,
	ApiProtectCocp_L2,
	ApiProtectCocp_L3,
#endif
#if 1
	ApiProtectDocp_L1,
	ApiProtectDocp_L2,
	ApiProtectDocp_L3,
#endif
#if 1
	ApiProtectScuOt_L1,
	ApiProtectScuOt_L2,
	ApiProtectScuOt_L3,
#endif
#if 1
	ApiProtectDvp,
	ApiProtectDtp,
#endif
#if 1
	ApiProtectMDvp,
	//ApiProtectDtp,
#endif
#if 1
	ApiProtectDip,
#endif

#if 1
	ApiProtectVbDvp,
#endif

#if 0	
	ApiProtectCellOwp,
#endif

	ApiProtectEnd
};
#endif

static void ApiProtectAddOvp(tfpApiProtectEvtHandler fpEvtHandler)
{
	ApiProtectOvpOpen(fpEvtHandler);
	ApiProtectAddToProtectTable(ApiProtectOvp_L1);
	ApiProtectAddToProtectTable(ApiProtectOvp_L2);
	ApiProtectAddToProtectTable(ApiProtectOvp_L3);
	ApiProtectAddToProtectTable(ApiProtectOvpPf);
}
static void ApiProtectAddUvp(tfpApiProtectEvtHandler fpEvtHandler)
{
	ApiProtectUvpOpen(fpEvtHandler);
	ApiProtectAddToProtectTable(ApiProtectUvp_L1);
	ApiProtectAddToProtectTable(ApiProtectUvp_L2);
	ApiProtectAddToProtectTable(ApiProtectUvp_L3);
	ApiProtectAddToProtectTable(ApiProtectUvpPf);
}								
static void ApiProtectAddCotp(tfpApiProtectEvtHandler fpEvtHandler)
{
    ApiProtectCotpOpen(fpEvtHandler);
	ApiProtectAddToProtectTable(ApiProtectCotp_L1);
	ApiProtectAddToProtectTable(ApiProtectCotp_L2);
	ApiProtectAddToProtectTable(ApiProtectCotp_L3);
}	
static void ApiProtectAddCutp(tfpApiProtectEvtHandler fpEvtHandler)
{
    ApiProtectCutpOpen(fpEvtHandler);
	ApiProtectAddToProtectTable(ApiProtectCutp_L1);
	ApiProtectAddToProtectTable(ApiProtectCutp_L2);
	ApiProtectAddToProtectTable(ApiProtectCutp_L3);
}	
static void ApiProtectAddDotp(tfpApiProtectEvtHandler fpEvtHandler)
{
    ApiProtectDotpOpen(fpEvtHandler);
	ApiProtectAddToProtectTable(ApiProtectDotp_L1);
	ApiProtectAddToProtectTable(ApiProtectDotp_L2);
	ApiProtectAddToProtectTable(ApiProtectDotp_L3);
}
static void ApiProtectAddDutp(tfpApiProtectEvtHandler fpEvtHandler)
{
    ApiProtectDutpOpen(fpEvtHandler);
	ApiProtectAddToProtectTable(ApiProtectDutp_L1);
	ApiProtectAddToProtectTable(ApiProtectDutp_L2);
	ApiProtectAddToProtectTable(ApiProtectDutp_L3);
}
static void ApiProtectAddDocp(tfpApiProtectEvtHandler fpEvtHandler)
{
    ApiProtectDocpOpen(fpEvtHandler);
	ApiProtectAddToProtectTable(ApiProtectDocp_L1);
	ApiProtectAddToProtectTable(ApiProtectDocp_L2);
	ApiProtectAddToProtectTable(ApiProtectDocp_L3);
}


/* Public function prototypes -----------------------------------------------*/
void ApiProtectGetLevelMask(uint8_t Level, tProtectFlagValue *pProtectFlagValue)
{
	const uint8_t u8LevelTable[][4]={
			{API_PROTECT_FLAG_L1_MASK,	API_PROTECT_FLAG_L1_SETTING,	API_PROTECT_FLAG_L1_SETTED,	API_PROTECT_FLAG_L1_REALSING},
			{API_PROTECT_FLAG_L2_MASK,	API_PROTECT_FLAG_L2_SETTING,	API_PROTECT_FLAG_L2_SETTED,	API_PROTECT_FLAG_L2_REALSING},
			{API_PROTECT_FLAG_L3_MASK,	API_PROTECT_FLAG_L3_SETTING,	API_PROTECT_FLAG_L3_SETTED,	API_PROTECT_FLAG_L3_REALSING},
			{API_PROTECT_FLAG_L4_MASK,	API_PROTECT_FLAG_L4_SETTING,	API_PROTECT_FLAG_L4_SETTED,	API_PROTECT_FLAG_L4_REALSING}
		};
	
	pProtectFlagValue->u8Mask = u8LevelTable[Level][0];
	pProtectFlagValue->u8ClearMask = ~ pProtectFlagValue->u8Mask;
	pProtectFlagValue->u8Setting = u8LevelTable[Level][1];
	pProtectFlagValue->u8Setted = u8LevelTable[Level][2];
	pProtectFlagValue->u8Releasing = u8LevelTable[Level][3];
}

uint8_t	ApiProtectIsUnderTemperter(uint16_t u16NtcVoltage, uint16_t u16CompareVoltage)
{
#ifdef API_PROTECT_USE_TEMP_VALUE_COMPARE	
	if(u16NtcVoltage < u16CompareVoltage)
#else
	if(u16NtcVoltage > u16CompareVoltage)
#endif	
	{
		return 1;
	}
	else
		return 0;
}

uint8_t	ApiProtectIsOverTemperter(uint16_t u16NtcVoltage, uint16_t u16CompareVoltage)
{
#ifdef API_PROTECT_USE_TEMP_VALUE_COMPARE	
	if(u16NtcVoltage > u16CompareVoltage)
#else	
	if(u16NtcVoltage < u16CompareVoltage)
#endif
	{	
		return 1;
	}
	else
		return 0;
}

static void ApiProtectSwTimerHandler(__far void *pvDest, uint16_t u16Evt, void *pvDataPtr)
{

	static uint8_t	u8Count = 0;
	
    if(u16Evt & kLIB_SW_TIMER_EVT_1_MS)
    {
    	if(gu8ProtectRunTableIndex < API_PROTECT_MAX_FP_BUF_SIZE)
		{
//			DL_GPIO_togglePins(GPIOA, DL_GPIO_PIN_9);
			(*gmfpProtectRunTable[gu8ProtectRunTableIndex])();	
		}
	}
	if(u16Evt & kLIB_SW_TIMER_EVT_10_8_MS)
	{
		u8Count++;
		if(u8Count >= API_PROTECT_INTERVAL_10MS)
		{
			u8Count = 0;
			if(API_PROTECT_IS_IN_SIMU_MODE() == 0 &&  API_PROTECT_GET_AFE_STATE() != kAFE_STATE_NORMAL)
				return;
			else			
				gu8ProtectRunTableIndex = 0;
		}
	}
}
void ApiProtectOpen(tfpApiProtectEvtHandler fpEvtHandler)
{	
	gu8ProtectFunctionTableIndex = 0;
  	LibSoftwareTimerHandlerOpen(ApiProtectSwTimerHandler, 0);
  	ApiProtectAddOvp(fpEvtHandler);
  	ApiProtectAddUvp(fpEvtHandler);
  	ApiProtectAddCotp(fpEvtHandler);
  	ApiProtectAddCutp(fpEvtHandler);
	ApiProtectAddDotp(fpEvtHandler);
	ApiProtectAddDutp(fpEvtHandler);
	ApiProtectAddDocp(fpEvtHandler);

#if 0  	
	ApiProtectCocpOpen(evtHandler);
	ApiProtectDocpOpen(evtHandler);
	ApiProtectScuOtOpen(evtHandler);
	ApiProtectDvpOpen(evtHandler);
	ApiProtectDtpOpen(evtHandler);
	ApiProtectMDvpOpen(evtHandler);
	ApiProtectDipOpen(evtHandler);
	ApiProtectVbDvpOpen(evtHandler);
#endif	
}

/************************ (C) COPYRIGHT *****END OF FILE****/    


