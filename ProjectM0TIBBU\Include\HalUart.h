/*
******************************************************************************
* @file     HalUart.h
* <AUTHOR>
* @brief    This file include MSPM0G3519 UART Hardwarre Abstraction Layer Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_UART_H__
#define	__HAL_UART_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h" 
#include "HalGpio.h"
#include "LibUartFifo.h"

/* Global define ------------------------------------------------------------*/
#define UART_IBRD_32_MHZ_9600_BAUD               (208)
#define UART_FBRD_32_MHZ_9600_BAUD               (21)
#define UART_IBRD_32_MHZ_19200_BAUD              (104)
#define UART_FBRD_32_MHZ_19200_BAUD              (11)
#define UART_IBRD_32_MHZ_38400_BAUD              (52)
#define UART_FBRD_32_MHZ_38400_BAUD              (5)
#define UART_IBRD_32_MHZ_57600_BAUD              (34)
#define UART_FBRD_32_MHZ_57600_BAUD              (29)
#define UART_IBRD_32_MHZ_115200_BAUD             (17)
#define UART_FBRD_32_MHZ_115200_BAUD             (15)

#define UART_IBRD_40_MHZ_9600_BAUD               (260)
#define UART_FBRD_40_MHZ_9600_BAUD               (11)
#define UART_IBRD_40_MHZ_19200_BAUD              (130)
#define UART_FBRD_40_MHZ_19200_BAUD              (5)
#define UART_IBRD_40_MHZ_38400_BAUD              (65)
#define UART_FBRD_40_MHZ_38400_BAUD              (3)
#define UART_IBRD_40_MHZ_57600_BAUD              (43)
#define UART_FBRD_40_MHZ_57600_BAUD              (15)
#define UART_IBRD_40_MHZ_115200_BAUD             (21)
#define UART_FBRD_40_MHZ_115200_BAUD             (23)

#define UART_IBRD_80_MHZ_9600_BAUD               (520)
#define UART_FBRD_80_MHZ_9600_BAUD               (21)
#define UART_IBRD_80_MHZ_19200_BAUD              (260)
#define UART_FBRD_80_MHZ_19200_BAUD              (11)
#define UART_IBRD_80_MHZ_38400_BAUD              (130)
#define UART_FBRD_80_MHZ_38400_BAUD              (5)
#define UART_IBRD_80_MHZ_57600_BAUD              (86)
#define UART_FBRD_80_MHZ_57600_BAUD              (29)
#define UART_IBRD_80_MHZ_115200_BAUD             (43)
#define UART_FBRD_80_MHZ_115200_BAUD             (15)

#define UART0_TX_FIFO_SIZE                       (4)
#define UART0_RX_FIFO_SIZE                       (1)
#define UART1_TX_FIFO_SIZE                       (4)
#define UART1_RX_FIFO_SIZE                       (1)

/* Global typedef -----------------------------------------------------------*/
typedef enum
{
	kHAL_UART_BAUD_9600 = 0,										
	kHAL_UART_BAUD_19200,										
	kHAL_UART_BAUD_38400,
    kHAL_UART_BAUD_57600,
    kHAL_UART_BAUD_115200,
}eTypeHalUartBaud;

typedef enum
{
	kHAL_UART_MAIN_MODE_NORMAL = DL_UART_MAIN_MODE_NORMAL,
	kHAL_UART_MAIN_MODE_RS485 = DL_UART_MAIN_MODE_RS485,
    kHAL_UART_MAIN_MODE_IDLE_LINE = DL_UART_MAIN_MODE_IDLE_LINE,
    kHAL_UART_MAIN_MODE_ADDR_9_BIT = DL_UART_MAIN_MODE_ADDR_9_BIT,					
}eTypeHalUartMainMode;

typedef enum
{
	kHAL_UART_MAIN_DIRECTION_TX = DL_UART_MAIN_DIRECTION_TX,
	kHAL_UART_MAIN_DIRECTION_RX = DL_UART_MAIN_DIRECTION_RX,
    kHAL_UART_MAIN_DIRECTION_TX_RX = DL_UART_MAIN_DIRECTION_TX_RX,	
    kHAL_UART_MAIN_DIRECTION_NONE = DL_UART_MAIN_DIRECTION_NONE,				
}eTypeHalUartDirection;

typedef enum
{
	kHAL_UART_MAIN_FLOW_CONTROL_NONE = DL_UART_MAIN_FLOW_CONTROL_NONE,		
	kHAL_UART_MAIN_FLOW_CONTROL_RTS = DL_UART_MAIN_FLOW_CONTROL_RTS,
    kHAL_UART_MAIN_FLOW_CONTROL_CTS = DL_UART_MAIN_FLOW_CONTROL_CTS,
    kHAL_UART_MAIN_FLOW_CONTROL_RTS_CTS = DL_UART_MAIN_FLOW_CONTROL_RTS_CTS,
}eTypeHalUartFlowControl;

typedef enum
{
	kHAL_UART_MAIN_PARITY_NONE = DL_UART_MAIN_PARITY_NONE,						
	kHAL_UART_MAIN_PARITY_EVEN = DL_UART_MAIN_PARITY_EVEN,								
	kHAL_UART_MAIN_PARITY_ODD = DL_UART_MAIN_PARITY_ODD,
    kHAL_UART_MAIN_PARITY_STICK_ONE = DL_UART_MAIN_PARITY_STICK_ONE,
    kHAL_UART_MAIN_PARITY_STICK_ZERO = DL_UART_MAIN_PARITY_STICK_ZERO,									
}eTypeHalUartParity;

typedef enum
{
	kHAL_UART_MAIN_WORD_LENGTH_8_BITS = DL_UART_MAIN_WORD_LENGTH_8_BITS,							
	kHAL_UART_MAIN_WORD_LENGTH_7_BITS = DL_UART_MAIN_WORD_LENGTH_7_BITS,									
	kHAL_UART_MAIN_WORD_LENGTH_6_BITS = DL_UART_MAIN_WORD_LENGTH_6_BITS,
    kHAL_UART_MAIN_WORD_LENGTH_5_BITS = DL_UART_MAIN_WORD_LENGTH_5_BITS,								
}eTypeHalUartWordLength;

typedef enum
{
	kHAL_UART_MAIN_STOP_BITS_ONE = DL_UART_MAIN_STOP_BITS_ONE,							
	kHAL_UART_MAIN_STOP_BITS_TWO = DL_UART_MAIN_STOP_BITS_TWO,																	
}eTypeHalStopBit;

typedef enum
{
	kHAL_UART_MAIN_OVERSAMPLING_RATE_16X = DL_UART_MAIN_OVERSAMPLING_RATE_16X,							
	kHAL_UART_MAIN_OVERSAMPLING_RATE_8X = DL_UART_MAIN_OVERSAMPLING_RATE_8X,
    kHAL_UART_MAIN_OVERSAMPLING_RATE_3X = DL_UART_MAIN_OVERSAMPLING_RATE_3X,																	
}eTypeHalOversamplingRate;

typedef struct 
{
    uint16_t u16Ibrd;
    uint16_t u16Fbrd;
}tHalUartBaudConfig;

typedef struct
{
	uint8_t			*pu8HalUartRxFifo;		
	uint16_t		u16HalUartRxFifoSize;			
	uint8_t			*pu8HalUartTxFifo;				
	uint16_t		u16HalUartTxFifoSize;		
}tHalUartFifoConfig;

typedef enum
{
	kHAL_UART_DATA_READY = 0,				/* UART data has been received */
	kHAL_UART_BUFFER_FULL,       			/* An error in the FIFO buffer full. The FIFO error code is stored in eTypeHalUartEvt. */
	kHAL_UART_COMMUNICATION_ERR,			/* UART has occurred during reception */
	kHAL_UART_TX_EMPTY,						/* UART has complete transmission of all variable data */ 
	kHAL_UART_TX_READY_TO_SEND              /* UART data TX for RS485 directiion use.*/
}eTypeHalUartEvt;

typedef void(*tfpHalUartEvent)(eTypeHalUartEvt eUartEvt);
 
typedef struct 
{
    UART_Regs* pmUartChannel;
    tHalUartFifoConfig	mUartFifoConfig;
    tfpHalUartEvent fpUartEvent;
    eTypeHalUartBaud eBaud;
    eTypeHalUartMainMode eMode;
    eTypeHalUartDirection eDirection;
    eTypeHalUartFlowControl eFlowControl;
    eTypeHalUartParity eParity;
    eTypeHalUartWordLength eWordLength;
    eTypeHalStopBit eStopBits;
    eTypeHalOversamplingRate eOversamplingRate;
}tHalUartConfig;

typedef struct 
{
    GPIO_Regs* pmTxPort;
    uint32_t u32TxPin;
    uint32_t u32TxIomux;
    uint32_t u32TxIomuxPf;
    GPIO_Regs* pmRxPort;
    uint32_t u32RxPin;
    uint32_t u32RxIomux;
    uint32_t u32RxIomuxPf;
    uint8_t u8IntIrqn;
    tLibUartFifoStatus* pmUartTxFifoStatus;
    tLibUartFifoStatus* pmUartRxFifoStatus;
}tHalUartChannelConfig;

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void HalUartInit(tHalUartConfig* pmHalUartConfig);
int8_t HalUartPut(tHalUartConfig* pmHalUartConfig, uint8_t u8Data);
int8_t HalUartGet(tHalUartConfig* pmHalUartConfig, uint8_t* pu8Data);
void HalUartInitExample(void);
void HalUartTxRxExample(void);
void HalUartSendTxExample(uint8_t* pu8TxData, uint8_t u8TxSize);

#ifdef __cplusplus
}
#endif

#endif