/*
******************************************************************************
* @file     LibSysPar.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef _LIB_SYS_PAR_H_
#define _LIB_SYS_PAR_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "LibFunctionPointerRegister.h"


#ifdef __cplusplus
extern "C" {
#endif

#define	API_SYS_PAR_MAX_OCV_TABLE_NUM	25
#define	API_SYS_PAR_MAX_RA_TABLE_NUM	25


#define	CALI_P1_INDEX	0
#define	CALI_P2_INDEX	1

#define	SYS_ACTIVE_FLAG_FULL_CHG_BY_MAX_CELL	0x0001
#define	SYS_ACTIVE_FLAG_FULL_DSG_BY_MIN_CELL	0x0002
#define	SYS_ACTIVE_FLAG_RELAY_BY_COMMAND		0x0004
#define SYS_ACTIVE_FLAG_RELAY_BY_BMS			0x0008
#define	SYS_ACTIVE_FLAG_CELL_BALANCE_BY_MBMS	0x0010
#define	SYS_ACTIVE_FLAG_CANCEL_IT_ALGORITHM		0x0020


typedef struct{
    int32_t 	i32ValL;
    int32_t 	i32ValH;
    int32_t 	i32AdcL;
    int32_t 	i32AdcH;
}tCaliPar;

typedef union{
  uint8_t 	u8Buf[4];
  uint32_t 	u32Value;
}tLbyte;
typedef union{
  uint8_t 	u8Buf[2];
  uint16_t 	u16Value;
  int16_t	  i16Value;
}tIbyte;

typedef struct{
	tLbyte			mSetValue;
	tLbyte			mSTime;
	tLbyte			mRelValue;
	tLbyte			mRTime;
}tScuProtectPar;

typedef struct{
	tLbyte			mChgCurrent;
	tLbyte			mDsgCurrent;
}tSysCurrentPar;

typedef struct{
	tLbyte			mChgPeakCurrent;
	tLbyte			mChgPeakSecond;
  tLbyte			mDsgPeakCurrent;
	tLbyte			mDsgPeakSecond;
}tSysPeakCurrentPar;

typedef struct{
	tLbyte			mVoltage;
	tLbyte			mMaxVoltage;
  tLbyte			mMinVoltage;
}tSysRateVoltagePar;

typedef struct{
	tLbyte			mMaxFloatVoltage;
	tLbyte			mMinFloatVoltage;
}tSysFloatVoltagePar;

typedef struct{
	tLbyte			mCurrent;
	tLbyte			mVoltage;
  tLbyte			mTime;
}tSysFullChargePar;

typedef struct{
	tLbyte			mL1Time;
	tLbyte			mL2Time;
}tSysAfeCommTimePar;

typedef struct{
	tLbyte			mSetValue;
	tLbyte			mSetRealse;
  tLbyte			mDeltaValue;
	tLbyte			mDeltaRealse;
}tSysBalancePar;

typedef struct{
	tLbyte			mSetDuty;
	tLbyte			mSetDutyRest;
  tLbyte			mSetTemp;
	tLbyte			mSetTempRealse;
}tSysBalanceDutyPar;

typedef struct{
		uint8_t		u8Level;
		uint16_t	u16Value;
}tOcvRaTable;

/* Public define ------------------------------------------------------------*/
#define	MAX_NOTE_MESSAGE_STRING_ITEM	100

/* Public macro -------------------------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
//	Battery Info
uint8_t LibSysParIsOvpPfSet(void);
uint8_t LibSysParIsUvpPfSet(void);
uint8_t LibSysParIsCDvpPfSet(void);
uint8_t LibSysParIsMDvpPfSet(void);

void LibSysParOvpPfClean(void);
void LibSysParOvpPfSet(void);
void LibSysParUvpPfClean(void);
void LibSysParUvpPfSet(void);
void LibSysParCDvpPfClean(void);
void LibSysParCDvpPfSet(void);
void LibSysParMDvpPfClean(void);
void LibSysParMDvpPfSet(void);



//	Cali
void ApiCaliParSetCurrentValue(uint8_t u8CurrentNum, uint8_t u8PointIndex,int32_t _i32Value, int32_t _i32Adc);
void ApiCaliParGetCurrentValue(uint8_t u8CurrentNum, uint8_t u8PointIndex,int32_t *pi32Value, int32_t *pi32Adc);
void ApiCaliParSetVbatValue(uint8_t u8CurrentIndex, uint8_t u8PointIndex,int32_t _i32Value, int32_t _i32Adc);
void ApiCaliParGetVbatValue(uint8_t u8CurrentNum, uint8_t u8PointIndex,int32_t *pi32Value, int32_t *pi32Adc);
uint32_t apiCaliParGetChecksum(void);


// system par
uint32_t LibSysParGetHwVersion(void);
void LibSysParSetHwVersion(uint32_t u32Version);
uint32_t LibSysParGetFwVersion(void);
void appSysParSetFwVersion(uint32_t u32Version);

uint8_t LibSysParGetTotalAfeNumber(void);
void LibSysParSetAfeNumber(uint8_t u8TotalAfes);

uint32_t LibSysParGetCellFlag(uint8_t u8BmuIndex);
void LibSysParSetCellFlag(uint8_t u8BmuIndex,uint32_t u32CellFlag);
uint32_t LibSysParGetNtcFlag(uint8_t u8BmuIndex);
void LibSysParSetNtcFlag(uint8_t u8BmuIndex,uint32_t u32NtcFlag);

uint16_t LibSysParGetZeroCurrentValue(void);
void LibSysParSetZeroCurrentValue(uint16_t u16Current);
uint16_t LibSysParGetMinChargeCurrentValue(void);
void LibSysParSetMinChargeCurrentValue(uint16_t current);

uint32_t LibSysParGetDesignedCapacity(void);
void LibSysParSetDesignedCapacity(uint32_t u32Dc);

uint32_t LibSysParGetSystemActiveFlag(void);
void LibSysParSetSystemActiveFlag(uint32_t u32Flag);

void LibSysParGetMaxCurrentValue(tSysCurrentPar *pmPar);
void LibSysParSetMaxCurrentValue(tSysCurrentPar *pmPar);

void LibSysParGetMaxPeakCurrentValue(tSysPeakCurrentPar *pmPar);
void LibSysParSetMaxPeakCurrentValue(tSysPeakCurrentPar *pmPar);

void LibSysParGetRateVoltage(tSysRateVoltagePar *pmPar);
void LibSysParSetRateVoltage(tSysRateVoltagePar *pmPar);

void LibSysParGetFullChargeCondition(tSysFullChargePar *pmPar);
void LibSysParSetFullChargeCondition(tSysFullChargePar *pmPar);


uint16_t LibSysParGetMinFlatVoltage(void);
uint16_t LibSysParGetMaxFlatVoltage(void);

void LibSysParGetFlatVoltage(tSysFloatVoltagePar *pmPar);
void LibSysParSetFlatVoltage(tSysFloatVoltagePar *pmPar);

uint16_t LibSysParGetLogicOffset(uint8_t u8AfeIndex);

uint16_t LibSysParGetCellNumber(void);
uint16_t LibSysParGetNtcNumber(void);

void LibSysParGetOcvTable(uint8_t u8Index ,tOcvRaTable *pmOcvTable);
void LibSysParGetRaTable(uint8_t u8Index ,tOcvRaTable *pmOcvTable);
void LibSysParSetOcvTable(uint8_t u8Index ,tOcvRaTable *pmOcvTable);
void LibSysParSetRaTable(uint8_t u8Index ,tOcvRaTable *pmOcvTable);

void LibSysParGetAfeCommTime(tSysAfeCommTimePar *pmPar);
void LibSysParSetAfeCommTime(tSysAfeCommTimePar *pmPar);
uint8_t LibSysParGetAfeCommL1Time(void);
uint8_t LibSysParGetAfeCommL2Time(void);

void LibSysParGetInsulationResistance(tScuProtectPar *pmPar);
void LibSysParSetInsulationResistance(tScuProtectPar *pmPar);

uint16_t LibSysParGetTerminateVoltage(void);
void LibSysParSetTerminateVoltage(uint16_t u16Voltage);
uint16_t LibSysParGetPreDischargeTime(void);
void LibSysParSetPreDischargeTime(uint16_t u16Time);
uint16_t LibSysParGetPreDischargeThreshold(void);
void LibSysParSetPreDischargeThreshold(uint16_t u16Time);

uint16_t LibSysParGetRelayOnDiffVoltage(void);
void LibSysParSetRelayOnDiffVoltage(uint16_t u16Voltage);

uint8_t LibSysParGetScuId(void);
void saveScuIdPar(uint8_t u16Scuid);

//protect
//uint16_t appSysParGetOvpSetValue(uint8_t u8Level);
uint16_t appSysParGetOvpSetTime(uint8_t u8Level);
uint16_t appSysParGetOvpReleaseValue(uint8_t u8Level);
uint16_t appSysParGetOvpReleaseTime(uint8_t u8Level);

void LibSysParGetOvpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetOvpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetUvpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetUvpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetDvpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetDvpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetModuleDvpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetModuleDvpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGet2ndOtProtectPar(tScuProtectPar *pmPar);
void LibSysParSet2ndOtProtectPar(tScuProtectPar *pmPar);
void LibSysParGet2ndUtProtectPar(tScuProtectPar *pmPar);
void LibSysParSet2ndUtProtectPar(tScuProtectPar *pmPar);

void LibSysParGetCotpProtectPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParGetCotpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetCotpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetCutpProtectPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParGetCutpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetCutpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetDotpProtectPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParGetDotpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetDotpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetDutpProtectPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParGetDutpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetDutpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetDtpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetDtpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetModuleDtpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetModuleDtpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetCocpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetCocpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetDocpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetDocpPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetOvpPfPar(tScuProtectPar *pmPar);
void LibSysParSetOvpPfPar(tScuProtectPar *pmPar);

void LibSysParGetUvpPfPar(tScuProtectPar *pmPar);
void LibSysParSetUvpPfPar(tScuProtectPar *pmPar);

void LibSysParGetDvpPfPar(tScuProtectPar *pmPar);
void LibSysParSetDvpPfPar(tScuProtectPar *pmPar);

void LibSysParGetMDvpPfPar(tScuProtectPar *pmPar);
void LibSysParSetMDvpPfPar(tScuProtectPar *pmPar);


void LibSysParGetBalanceDuty(tSysBalanceDutyPar *pmPar);
void LibSysParSetBalanceDuty(tSysBalanceDutyPar *pmPar);
void LibSysParGetBalanceChg(tSysBalancePar *pmPar);
void LibSysParSetBalanceChg(tSysBalancePar *pmPar);
void LibSysParGetBalanceDhg(tSysBalancePar *pmPar);
void LibSysParSetBalanceDhg(tSysBalancePar *pmPar);
void LibSysParGetBalanceRlx(tSysBalancePar *pmPar);
void LibSysParSetBalanceRlx(tSysBalancePar *pmPar);

void LibSysParGetNotwMessageString(uint8_t *pu8Msg);
void LibSysParSetNotwMessageString(uint8_t *pu8Msg);

void LibSysParGetIrUrpSetPar(uint8_t u8IrIndex, uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetIrUrpSetPar(uint8_t u8IrIndex, uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParGetIrUrpRlxPar(uint8_t u8IrIndex, uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetIrUrpRlxPar(uint8_t u8IrIndex, uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetLineLossPar(uint8_t u8BufIndex, uint16_t *pu16Channel,uint32_t *pu32RValue);
void LibSysParSetLineLossPar(uint8_t u8BufIndex, uint16_t u16Channel,uint32_t u32RValue);

uint32_t LibSysParGetQmax(void);
void LibSysParSetQmax(uint32_t u32Qmax);
uint16_t LibSysParGetQmaxUpdateTimes(void);
void LibSysParSetQmaxUpdateTimes(uint16_t u16Times);
uint16_t LibSysParGetCycleCount(void);
void LibSysParSetCycleCount(uint16_t u16Count);
uint16_t LibSysParGetPfFlag(void);
void LibSysParSetPfFlag(uint16_t u16Flag);
uint32_t LibSysParGetChecksum(void);

void LibSysParSetScuOtPar(uint8_t u8Index, uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParGetScuOtPar(uint8_t u8Index, uint8_t u8Level, tScuProtectPar *pmPar);

uint8_t LibSysParGetBmuType(void);
void LibSysParSetBmuType(uint8_t u8Type);

void LibSysParGetDipPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParSetDipPar(uint8_t u8Level, tScuProtectPar *pmPar);

void LibSysParGetDvpPfCellVoltagePar(tScuProtectPar *pmPar);
void LibSysParSetDvpPfCellVoltagePar(tScuProtectPar *pmPar);

void LibSysParSetVbDvpPar(uint8_t u8Level, tScuProtectPar *pmPar);
void LibSysParGetVbDvpPar(uint8_t u8Level, tScuProtectPar *pmPar);

uint8_t LibSysParGetCellBusbarNumber(void);
uint8_t LibSysParGetNtcBusbarNumber(void);
uint8_t LibSysParGetNtcAmbientNumber(void);
uint8_t LibSysParGetNtcOtherNumber(void);

uint8_t LibSysParGetCellNumInPack(void);

void LibSysParGetCellOWpPar(tScuProtectPar *pmPar);
void LibSysParSetCellOWpPar(tScuProtectPar *pmPar);

uint32_t LibSysParGetCellBusBarFlag(uint8_t u8BmuIndex);
void LibSysParSetCellBusBarFlag(uint8_t u8BmuIndex,uint32_t u32Flag);
uint32_t LibSysParGetNtcAmbientFlag(uint8_t u8BmuIndex);
void LibSysParSetNtcAmbientFlag(uint8_t u8BmuIndex,uint32_t u32Flag);
uint32_t LibSysParGetNtcBusBarFlag(uint8_t u8BmuIndex);
void LibSysParSetNtcBusBarFlag(uint8_t u8BmuIndex,uint32_t u32Flag);
uint32_t LibSysParGetNtcOtherFlag(uint8_t u8BmuIndex);
void LibSysParSetNtcOtherFlag(uint8_t u8BmuIndex,uint32_t u32Flag);
	
uint16_t LibSysParGetBmuPassiveBalR(void);
void LibSysParSetBmuPassiveBalR(uint16_t u16Val);
	
uint16_t LibSysParOpen(void);


#ifdef __cplusplus
}
#endif


	

#endif /* _API_SYS_PAR_H_ */

/************************ (C) COPYRIGHT  *****END OF FILE****/    


