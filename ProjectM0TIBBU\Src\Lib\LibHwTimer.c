/*
******************************************************************************
* @file     LibHwTimer.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include "LibFunctionReturnValueDefine.h"
#include "LibFunctionPointerRegister.h"
#include "LibHardwareTimerHandler.h"

#include <stdbool.h>
/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static tLibRegister HwTimerEvtHandlerRegister;
/* Private function prototypes -----------------------------------------------*/

tErrCode LibHwTimerOpen(tLibHwTimerEvtHandler handler, __far void *dest){
static bool initFlag = false; 
  if(LibRegisterIsMemberNull(&HwTimerEvtHandlerRegister) == true){
	  if(initFlag == true){
//          count1ms = 0;
 //         count1s = 0;
	  }
  }
  
  if(initFlag == false){
	  initFlag = true;  
  }
  return LibRegisterAdd(&HwTimerEvtHandlerRegister, handler, dest);
}

tErrCode LibHwTimerClose(tLibHwTimerEvtHandler handler, __far void *dest){
  return LibRegisterRm(&HwTimerEvtHandlerRegister, handler, dest);
}

void LibHwTimerHandle(void){
	LibRegisterTypeHandlerExe(&HwTimerEvtHandlerRegister, kLIB_HW_TIMER_EVT_1_MS, 0);
}

