/*
******************************************************************************
* @file     HalI2cEeProm.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_I2C_EEPROM_H__
#define	__HAL_I2C_EEPROM_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "HalI2c.h"
#include "Define.h"
#include "LibFunctionReturnValueDefine.h"

/* Global define ------------------------------------------------------------*/
/* Global typedef -----------------------------------------------------------*/
typedef struct{
	tHalI2cMasterConfig* pmHalI2cConfig;
	uint32_t 	u32DeviceAddress;
	uint32_t	u32StartAddress;
	uint16_t	u16Length;
	uint8_t		*pu8DataBuffer;
}tHalI2cEeProm;

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
tFunRetunCode HalI2cEePromBlockWrite(tHalI2cEeProm *pmI2cEeProm);
tFunRetunCode HalI2cEePromBlockRead(tHalI2cEeProm *pmI2cEeProm);

void HalI2cEePromExample(void);

#ifdef __cplusplus
}
#endif

#endif