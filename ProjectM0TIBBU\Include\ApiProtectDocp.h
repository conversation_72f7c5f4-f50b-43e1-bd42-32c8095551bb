/*
******************************************************************************
* @file     ApiProtectDocp.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

#ifndef _API_PROTECT_DOCP_H_
#define _API_PROTECT_DOCP_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "ApiProtect.h"
#include "LibFunctionPointerRegister.h"
#ifdef __cplusplus
extern "C" {
#endif

/* Public define ------------------------------------------------------------*/
/* Public typedef -----------------------------------------------------------*/
/* Public macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
void ApiProtectDocpOpen(tfpApiProtectEvtHandler fpEvtHandler);
uint8_t	ApiProtectDocpGetFlag(void);
uint8_t ApiProtectDocpHandler(uint8_t u8ProtectLevel);


/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */


#ifdef __cplusplus
}
#endif


#endif /* _API_PROTECT_DOCP_H_ */


/************************ (C) COPYRIGHT *****END OF FILE****/    


