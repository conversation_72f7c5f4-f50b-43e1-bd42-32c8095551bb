/*
******************************************************************************
* @file     ApiSystemFlag.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

#ifndef _API_SYSTEM_FLAG_H_
#define _API_SYSTEM_FLAG_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Public define ------------------------------------------------------------*/
#define	API_SYSTEM_FLAG1_OVP_L1			(1U << 0)
#define	API_SYSTEM_FLAG1_OVP_L2			(1U << 1)
#define	API_SYSTEM_FLAG1_OVP_L3			(1U << 2)
#define	API_SYSTEM_FLAG1_OVP_2nd		(1U << 3)
#define	API_SYSTEM_FLAG1_UVP_L1			(1U << 4)
#define	API_SYSTEM_FLAG1_UVP_L2			(1U << 5)
#define	API_SYSTEM_FLAG1_UVP_L3			(1U << 6)
#define	API_SYSTEM_FLAG1_UVP_2nd		(1U << 7)
#define	API_SYSTEM_FLAG1_COTP_L1		(1U << 8)
#define	API_SYSTEM_FLAG1_COTP_L2		(1U << 9)
#define	API_SYSTEM_FLAG1_COTP_L3		(1U << 10)
#define	API_SYSTEM_FLAG1_CUTP_L1		(1U << 11)
#define API_SYSTEM_FLAG1_CUTP_L2		(1U << 12)
#define API_SYSTEM_FLAG1_CUTP_L3		(1U << 13)
#define API_SYSTEM_FLAG1_DOTP_L1		(1U << 14)
#define API_SYSTEM_FLAG1_DOTP_L2		(1U << 15)
#define API_SYSTEM_FLAG1_DOTP_L3		(1U << 16)
#define API_SYSTEM_FLAG1_DUTP_L1		(1U << 17)
#define API_SYSTEM_FLAG1_DUTP_L2		(1U << 18)
#define API_SYSTEM_FLAG1_DUTP_L3		(1U << 19)
#define API_SYSTEM_FLAG1_UT_2nd			(1U << 20)
#define API_SYSTEM_FLAG1_OT_2nd			(1U << 21)
#define API_SYSTEM_FLAG1_COCP_L1		(1U << 22)
#define API_SYSTEM_FLAG1_COCP_L2		(1U << 23)
#define API_SYSTEM_FLAG1_COCP_L3		(1U << 24)
#define API_SYSTEM_FLAG1_COCP_LATCH		(1U << 25)
#define API_SYSTEM_FLAG1_DOCP_L1		(1U << 26)
#define API_SYSTEM_FLAG1_DOCP_L2		(1U << 27)
#define API_SYSTEM_FLAG1_DOCP_L3		(1U << 28)
#define API_SYSTEM_FLAG1_DOCP_LATCH		(1U << 29)
#define	API_SYSTEM_FLAG1_CANID_READY	(1U << 30)
#define API_SYSTEM_FLAG1_SYSTEM_READY	(1U << 31)

#define API_SYSTEM_FLAG2_AFE_L1			(1U << 0)
#define API_SYSTEM_FLAG2_AFE_L2			(1U << 1)
#define API_SYSTEM_FLAG2_EPO_ENABLE		(1U << 2)
#define API_SYSTEM_FLAG2_OVP_PF			(1U << 3)
#define API_SYSTEM_FLAG2_UVP_PF			(1U << 4)
#define API_SYSTEM_FLAG2_P_RELAY_FAIL	(1U << 5)
#define API_SYSTEM_FLAG2_M_RELAY_FAIL	(1U << 6)
#define API_SYSTEM_FLAG2_MASTER			(1U << 7)
#define API_SYSTEM_FLAG2_RELAY_ON		(1U << 8)
#define API_SYSTEM_FLAG2_RTC_VALID		(1U << 9)
#define API_SYSTEM_FLAG2_PS1_FAIL		(1U << 10)
#define API_SYSTEM_FLAG2_PS2_FAIL		(1U << 11)
#define API_SYSTEM_FLAG2_PS3_FAIL		(1U << 12)
#define API_SYSTEM_FLAG2_K1				(1U << 13)
#define API_SYSTEM_FLAG2_K2				(1U << 14)
#define API_SYSTEM_FLAG2_K3				(1U << 15)
#define API_SYSTEM_FLAG2_K4				(1U << 16)
#define API_SYSTEM_FLAG2_SP_FB			(1U << 17)
#define API_SYSTEM_FLAG2_DI1			(1U << 18)
#define API_SYSTEM_FLAG2_DI2			(1U << 19)
#define API_SYSTEM_FLAG2_OD_IN			(1U << 20)
#define API_SYSTEM_FLAG2_NFAULT			(1U << 21)
#define	API_SYSTEM_FLAG2_ENG_MODE		(1U << 22)
#define	API_SYSTEM_FLAG2_AFE_INI_STATE	(1U << 23)
#define	API_SYSTEM_FLAG2_RP_URP_L1		(1U << 24)
#define	API_SYSTEM_FLAG2_RP_URP_L2		(1U << 25)
#define	API_SYSTEM_FLAG2_RP_URP_L3		(1U << 26)
#define	API_SYSTEM_FLAG2_RN_URP_L1		(1U << 27)
#define	API_SYSTEM_FLAG2_RN_URP_L2		(1U << 28)
#define	API_SYSTEM_FLAG2_RN_URP_L3		(1U << 29)
#define	API_SYSTEM_FLAG2_CDVP_PF		(1U << 30)
#define	API_SYSTEM_FLAG2_MDVP_PF		(1U << 31)


#define	API_SYSTEM_FLAG3_RLY1_OT_L1			(1U << 0)
#define	API_SYSTEM_FLAG3_RLY1_OT_L2			(1U << 1)
#define	API_SYSTEM_FLAG3_RLY1_OT_L3			(1U << 2)
#define	API_SYSTEM_FLAG3_RLY2_OT_L1			(1U << 3)
#define	API_SYSTEM_FLAG3_RLY2_OT_L2			(1U << 4)
#define	API_SYSTEM_FLAG3_RLY2_OT_L3			(1U << 5)
#define	API_SYSTEM_FLAG3_AMBI_OT_L1			(1U << 6)
#define	API_SYSTEM_FLAG3_AMBI_OT_L2			(1U << 7)
#define	API_SYSTEM_FLAG3_AMBI_OT_L3			(1U << 8)
#define	API_SYSTEM_FLAG3_BUSBARP_OT_L1		(1U << 9)
#define	API_SYSTEM_FLAG3_BUSBARP_OT_L2		(1U << 10)
#define	API_SYSTEM_FLAG3_BUSBARP_OT_L3		(1U << 11)
#define	API_SYSTEM_FLAG3_BUSBARN_OT_L1		(1U << 12)
#define	API_SYSTEM_FLAG3_BUSBARN_OT_L2		(1U << 13)
#define	API_SYSTEM_FLAG3_BUSBARN_OT_L3		(1U << 14)
#define	API_SYSTEM_FLAG3_DVP_L1				(1U << 15)
#define	API_SYSTEM_FLAG3_DVP_L2				(1U << 16)
#define	API_SYSTEM_FLAG3_DVP_L3				(1U << 17)
#define	API_SYSTEM_FLAG3_DTP_L1				(1U << 18)
#define	API_SYSTEM_FLAG3_DTP_L2				(1U << 19)
#define	API_SYSTEM_FLAG3_DTP_L3				(1U << 20)
#define	API_SYSTEM_FLAG3_MDVP_L1			(1U << 21)
#define	API_SYSTEM_FLAG3_MDVP_L2			(1U << 22)
#define	API_SYSTEM_FLAG3_MDVP_L3			(1U << 23)
#define	API_SYSTEM_FLAG3_MDTP_L1			(1U << 24)
#define	API_SYSTEM_FLAG3_MDTP_L2			(1U << 25)
#define	API_SYSTEM_FLAG3_MDTP_L3			(1U << 26)
#define	API_SYSTEM_FLAG3_PRE_CHG_FAIL		(1U << 27)
#define	API_SYSTEM_FLAG3_DIP_L1				(1U << 28)
#define	API_SYSTEM_FLAG3_DIP_L2				(1U << 29)
#define	API_SYSTEM_FLAG3_DIP_L3				(1U << 30)
#define	API_SYSTEM_FLAG3_AFE_BRIDGE_FAIL	(1U << 31)

#define	API_SYSTEM_FLAG4_IR_VALID			(1U << 0)
#define	API_SYSTEM_FLAG4_IR_BALANCE			(1U << 1)
#define	API_SYSTEM_FLAG4_FULL_CHARGE		(1U << 2)
#define	API_SYSTEM_FLAG4_FULL_DISCHARGE		(1U << 3)
#define	API_SYSTEM_FLAG4_VB_DVP_L1			(1U << 4)
#define	API_SYSTEM_FLAG4_VB_DVP_L2			(1U << 5)
#define	API_SYSTEM_FLAG4_VB_DVP_L3			(1U << 6)
#define API_SYSTEM_FLAG4_CELL_OPENWIRE		(1U << 7)
#define API_SYSTEM_FLAG4_CELL_OPENWIRE_WARN (1U << 8)
#define API_SYSTEM_FLAG4_CELL_BAL_ENABLE	(1U << 9)
#define API_SYSTEM_FLAG4_AFE_DIR			(1U << 10)
#define API_SYSTEM_FLAG4_VOLT_LASTBIT_EQL_BAL_STATE			(1U << 11)

/* Public typedef -----------------------------------------------------------*/
/* Public macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Public function prototypes -----------------------------------------------*/
void AppSystemFlagSetSystemReady(void);
void AppSystemFlagClearReadOnFlag(void);

uint32_t ApiSystemFlagGetFlag1(void);
uint32_t ApiSystemFlagGetFlag2(void);
uint32_t ApiSystemFlagGetFlag3(void);
uint32_t ApiSystemFlagGetFlag4(void);

void ApiSystemFlagOpen(void);

#endif /* _API_SYSTEM_FLAG_H_ */

/************************ (C) COPYRIGHT *****END OF FILE****/    

