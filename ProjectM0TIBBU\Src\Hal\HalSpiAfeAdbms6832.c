/*
******************************************************************************
* @file     HalSpiAfeAdbms6832.c
* <AUTHOR>
* @brief    XXXXXXXXXX

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "HalSpiAfeAdbms6832.h"
#include "HalSpi.h"
#include "LibSoftwareTimerHandler.h"
#include "HalGpio.h"
#include "HalUart.h" 
#include "AppSerialUartHvBBU.h"
#include <stdio.h>

/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
#define CRC_TABLE_SIZE  (256)
#define AFE_WAKEUP_DELAY_400US (32000)

/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
static tHalSpi gmHalSpi0Config = {0};
static uint8_t gu8ReadAfeRxDataBuf[HAL_ADBMS6832_SPI_RX_BUFFER_SIZE] = {0};
static eTypeHalAfeAdbms6832CommDirection geAfeCommDir = kHAL_AFE_ADBMS6832_COMM_DIR_NORTH;
static tfpHalAfeAdbms6832CbFun gfpHalAfeAdbms6832CbFun = NULL;
static uint16_t gu16DecodeAfeRxDataBuf[HAL_ADBMS6832_DECODE_BUFFER_SIZE] = {0}; 
static bool gbPecErrorBuf[HAL_ADBMS6832_MAX_AFE_IC_NUM] = {0}; 
static uint8_t gu8AfeOnlineNum = HAL_ADBMS6832_MAX_AFE_IC_NUM;
static uint8_t gu8TxCmdData[2] = {0};
static bool gbSpiCbEnd = false;
static bool gbSpiRdcfgaCbEnd = false;
static bool gbSpiRdcfgbCbEnd = false;
static uint8_t gu8AfeCfgaBuf[HAL_ADBMS6832_SPI_RX_BUFFER_SIZE] = {0};
static uint8_t gu8AfeCfgbBuf[HAL_ADBMS6832_SPI_RX_BUFFER_SIZE] = {0};

/* Local function prototypes ------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/

/* Precomputed CRC15 Table */
const uint16_t Crc15Table[CRC_TABLE_SIZE] =
{
    0x0000, 0xc599, 0xceab, 0x0b32, 0xd8cf, 0x1d56, 0x1664, 0xd3fd, 0xf407, 0x319e, 0x3aac, 0xff35, 0x2cc8, 0xe951, 0xe263, 0x27fa,
    0xad97, 0x680e, 0x633c, 0xa6a5, 0x7558, 0xb0c1, 0xbbf3, 0x7e6a, 0x5990, 0x9c09, 0x973b, 0x52a2, 0x815f, 0x44c6, 0x4ff4, 0x8a6d,
    0x5b2e, 0x9eb7, 0x9585, 0x501c, 0x83e1, 0x4678, 0x4d4a, 0x88d3, 0xaf29, 0x6ab0, 0x6182, 0xa41b, 0x77e6, 0xb27f, 0xb94d, 0x7cd4,
    0xf6b9, 0x3320, 0x3812, 0xfd8b, 0x2e76, 0xebef, 0xe0dd, 0x2544, 0x02be, 0xc727, 0xcc15, 0x098c, 0xda71, 0x1fe8, 0x14da, 0xd143,
    0xf3c5, 0x365c, 0x3d6e, 0xf8f7, 0x2b0a, 0xee93, 0xe5a1, 0x2038, 0x07c2, 0xc25b, 0xc969, 0x0cf0, 0xdf0d, 0x1a94, 0x11a6, 0xd43f,
    0x5e52, 0x9bcb, 0x90f9, 0x5560, 0x869d, 0x4304, 0x4836, 0x8daf, 0xaa55, 0x6fcc, 0x64fe, 0xa167, 0x729a, 0xb703, 0xbc31, 0x79a8,
    0xa8eb, 0x6d72, 0x6640, 0xa3d9, 0x7024, 0xb5bd, 0xbe8f, 0x7b16, 0x5cec, 0x9975, 0x9247, 0x57de, 0x8423, 0x41ba, 0x4a88, 0x8f11,
    0x057c, 0xc0e5, 0xcbd7, 0x0e4e, 0xddb3, 0x182a, 0x1318, 0xd681, 0xf17b, 0x34e2, 0x3fd0, 0xfa49, 0x29b4, 0xec2d, 0xe71f, 0x2286,
    0xa213, 0x678a, 0x6cb8, 0xa921, 0x7adc, 0xbf45, 0xb477, 0x71ee, 0x5614, 0x938d, 0x98bf, 0x5d26, 0x8edb, 0x4b42, 0x4070, 0x85e9,
    0x0f84, 0xca1d, 0xc12f, 0x04b6, 0xd74b, 0x12d2, 0x19e0, 0xdc79, 0xfb83, 0x3e1a, 0x3528, 0xf0b1, 0x234c, 0xe6d5, 0xede7, 0x287e,
    0xf93d, 0x3ca4, 0x3796, 0xf20f, 0x21f2, 0xe46b, 0xef59, 0x2ac0, 0x0d3a, 0xc8a3, 0xc391, 0x0608, 0xd5f5, 0x106c, 0x1b5e, 0xdec7,
    0x54aa, 0x9133, 0x9a01, 0x5f98, 0x8c65, 0x49fc, 0x42ce, 0x8757, 0xa0ad, 0x6534, 0x6e06, 0xab9f, 0x7862, 0xbdfb, 0xb6c9, 0x7350,
    0x51d6, 0x944f, 0x9f7d, 0x5ae4, 0x8919, 0x4c80, 0x47b2, 0x822b, 0xa5d1, 0x6048, 0x6b7a, 0xaee3, 0x7d1e, 0xb887, 0xb3b5, 0x762c,
    0xfc41, 0x39d8, 0x32ea, 0xf773, 0x248e, 0xe117, 0xea25, 0x2fbc, 0x0846, 0xcddf, 0xc6ed, 0x0374, 0xd089, 0x1510, 0x1e22, 0xdbbb,
    0x0af8, 0xcf61, 0xc453, 0x01ca, 0xd237, 0x17ae, 0x1c9c, 0xd905, 0xfeff, 0x3b66, 0x3054, 0xf5cd, 0x2630, 0xe3a9, 0xe89b, 0x2d02,
    0xa76f, 0x62f6, 0x69c4, 0xac5d, 0x7fa0, 0xba39, 0xb10b, 0x7492, 0x5368, 0x96f1, 0x9dc3, 0x585a, 0x8ba7, 0x4e3e, 0x450c, 0x8095
};

/* Pre-computed CRC10 Table */
static const uint16_t crc10Table[CRC_TABLE_SIZE] =
{
    0x000, 0x08f, 0x11e, 0x191, 0x23c, 0x2b3, 0x322, 0x3ad, 0x0f7, 0x078, 0x1e9, 0x166, 0x2cb, 0x244, 0x3d5, 0x35a,
    0x1ee, 0x161, 0x0f0, 0x07f, 0x3d2, 0x35d, 0x2cc, 0x243, 0x119, 0x196, 0x007, 0x088, 0x325, 0x3aa, 0x23b, 0x2b4,
    0x3dc, 0x353, 0x2c2, 0x24d, 0x1e0, 0x16f, 0x0fe, 0x071, 0x32b, 0x3a4, 0x235, 0x2ba, 0x117, 0x198, 0x009, 0x086,
    0x232, 0x2bd, 0x32c, 0x3a3, 0x00e, 0x081, 0x110, 0x19f, 0x2c5, 0x24a, 0x3db, 0x354, 0x0f9, 0x076, 0x1e7, 0x168,
    0x337, 0x3b8, 0x229, 0x2a6, 0x10b, 0x184, 0x015, 0x09a, 0x3c0, 0x34f, 0x2de, 0x251, 0x1fc, 0x173, 0x0e2, 0x06d,
    0x2d9, 0x256, 0x3c7, 0x348, 0x0e5, 0x06a, 0x1fb, 0x174, 0x22e, 0x2a1, 0x330, 0x3bf, 0x012, 0x09d, 0x10c, 0x183,
    0x0eb, 0x064, 0x1f5, 0x17a, 0x2d7, 0x258, 0x3c9, 0x346, 0x01c, 0x093, 0x102, 0x18d, 0x220, 0x2af, 0x33e, 0x3b1,
    0x105, 0x18a, 0x01b, 0x094, 0x339, 0x3b6, 0x227, 0x2a8, 0x1f2, 0x17d, 0x0ec, 0x063, 0x3ce, 0x341, 0x2d0, 0x25f,
    0x2e1, 0x26e, 0x3ff, 0x370, 0x0dd, 0x052, 0x1c3, 0x14c, 0x216, 0x299, 0x308, 0x387, 0x02a, 0x0a5, 0x134, 0x1bb,
    0x30f, 0x380, 0x211, 0x29e, 0x133, 0x1bc, 0x02d, 0x0a2, 0x3f8, 0x377, 0x2e6, 0x269, 0x1c4, 0x14b, 0x0da, 0x055,
    0x13d, 0x1b2, 0x023, 0x0ac, 0x301, 0x38e, 0x21f, 0x290, 0x1ca, 0x145, 0x0d4, 0x05b, 0x3f6, 0x379, 0x2e8, 0x267,
    0x0d3, 0x05c, 0x1cd, 0x142, 0x2ef, 0x260, 0x3f1, 0x37e, 0x024, 0x0ab, 0x13a, 0x1b5, 0x218, 0x297, 0x306, 0x389,
    0x1d6, 0x159, 0x0c8, 0x047, 0x3ea, 0x365, 0x2f4, 0x27b, 0x121, 0x1ae, 0x03f, 0x0b0, 0x31d, 0x392, 0x203, 0x28c,
    0x038, 0x0b7, 0x126, 0x1a9, 0x204, 0x28b, 0x31a, 0x395, 0x0cf, 0x040, 0x1d1, 0x15e, 0x2f3, 0x27c, 0x3ed, 0x362,
    0x20a, 0x285, 0x314, 0x39b, 0x036, 0x0b9, 0x128, 0x1a7, 0x2fd, 0x272, 0x3e3, 0x36c, 0x0c1, 0x04e, 0x1df, 0x150,
    0x3e4, 0x36b, 0x2fa, 0x275, 0x1d8, 0x157, 0x0c6, 0x049, 0x313, 0x39c, 0x20d, 0x282, 0x12f, 0x1a0, 0x031, 0x0be
};

/**
*******************************************************************************
* Function: pec15_Calc
* @brief CRC15 Pec Calculation Function
*
* @details This function calculates and return the CRC15 value
*
* Parameters:
* @param [in]	Len	Data length
*
* @param [in] *data    Data pointer
*
* @return CRC15_Value
*
*******************************************************************************
*/
static uint16_t pec15_Calc(uint8_t len, uint8_t *data)
{
    uint16_t remainder,addr;
    remainder = 16; /* initialize the PEC */
    for (uint8_t i = 0; i<len; i++) /* loops for each byte in data array */
    {
        addr = (((remainder>>7)^data[i])&0xff);/* calculate PEC table address */
        remainder = ((remainder<<8)^Crc15Table[addr]);
    }
    return(remainder*2);/* The CRC15 has a 0 in the LSB so the remainder must be multiplied by 2 */
}

// Calculate CRC10 as DPEC for ADBMS6832 response data 6 Bytes
// The polynomial is (x10 + x7 + x3 + x2 + x + 1)
// The initial value is 0x0010 = 16
//
// Example1: [0x00 0x80 0x00 0x80 0x00 0x80]  is Data, Calc DPEC = [0x02 0xCE]
// Example2: [0x01 0x00 0x00 0xFF 0x03 0x00]  is Data, Calc DPEC = [0x00 0x5A]
//-----------------------------------------------------------------------------------
static uint16_t pec10_calc(bool blSrXCmd, int nLength, uint8_t *pDataBuf)
{
    uint16_t nRemainder = 16u; /* PEC_SEED */
    /* x10 + x7 + x3 + x2 + x + 1 <- the CRC10 polynomial 100 1000 1111 */
    uint16_t nPolynomial = 0x8Fu;
    uint8_t nByteIndex, nBitIndex;

    for (nByteIndex = 0u; nByteIndex < nLength; ++nByteIndex)
    {
        /* Bring the next byte into the remainder. */
        nRemainder ^= (uint16_t)(((uint16_t)pDataBuf[nByteIndex]) << 2u);

        /* Perform modulo-2 division, a bit at a time.*/
        for (nBitIndex = 8u; nBitIndex > 0u; --nBitIndex)
        {
            /* Try to divide the current data bit. */
            if ((nRemainder & 0x200u) > 0u)
            { /* equivalent to remainder & 2^14 simply check for MSB */
                nRemainder = (uint16_t)((nRemainder << 1u));
                nRemainder = (uint16_t)(nRemainder ^ nPolynomial);
            }
            else
            {
                nRemainder = (uint16_t)(nRemainder << 1u);
            }
        }
    }

    /* If array is from received buffer add command counter to crc calculation */
    if (blSrXCmd == true)
    {
        nRemainder ^= (uint16_t)(((uint16_t)pDataBuf[nLength] & (uint8_t)0xFC) << 2u);
    }

    /* Perform modulo-2 division, a bit at a time */
    for (nBitIndex = 6u; nBitIndex > 0u; --nBitIndex)
    {
        /* Try to divide the current data bit */
        if ((nRemainder & 0x200u) > 0u)
        {
            nRemainder = (uint16_t)((nRemainder << 1u));
            nRemainder = (uint16_t)(nRemainder ^ nPolynomial);
        }
        else
        {
            nRemainder = (uint16_t)((nRemainder << 1u));
        }
    }
    return ((uint16_t)(nRemainder & 0x3FFu));
}

/*
Example
u8cmd[0] = 0x00;
u8cmd[1] = 0x04;
u16cmdpec = pec15_Calc(2, u8cmd);

uint8_t testData[8] = {0x80, 0x00, 0x00, 0x55, 0x00, 0x00, 0x00, 0x00};
pec = pec10_calc(false, 6, testData);
pec = pec10_calc(true, 6, testData);
*/

//=============================================================================================

static void spiReadByte(uint8_t u8TxData)
{
    if (HalSpiIsReady(&gmHalSpi0Config) == false)
    {
        return;
    }
	
    HalSpiSendRecvBlockingHalfDuplex(&gmHalSpi0Config, &u8TxData, 1, NULL, 0, (uint8_t)geAfeCommDir);
}

static void spiWriteAndRead(uint8_t* pu8TxData,
                    uint16_t  u16TxLen, 
                    uint8_t* pu8RxData, 
                    uint16_t  u16RxLen)
{
	HalSpiSendBlockingRecvDma(&gmHalSpi0Config, pu8TxData, u16TxLen, pu8RxData, u16RxLen, (uint8_t)geAfeCommDir);
}

//=============================================================================================

static void writeAdbms6832SpiCmd(uint8_t u8TxCmdData[2])
{
    uint16_t u16CmdPecData;
    uint8_t u8TxCmdAndPecData[4];


    u16CmdPecData = pec15_Calc(2, u8TxCmdData);
    u8TxCmdAndPecData[0] = u8TxCmdData[0];
    u8TxCmdAndPecData[1] = u8TxCmdData[1];
    u8TxCmdAndPecData[2] = (uint8_t)(u16CmdPecData >> 8);
    u8TxCmdAndPecData[3] = (uint8_t)(u16CmdPecData);
		
    spiWriteAndRead(u8TxCmdAndPecData, 4, NULL, 0);
}

static void writeAdbms6832SpiCmdAndReadData(uint8_t u8TxCmdData[2], uint8_t u8TotalAfeNum, uint8_t *pu8RxData)
{
    uint16_t u16CmdPecData;
    uint8_t u8TxCmdAndPecData[4];


    u16CmdPecData = pec15_Calc(2, u8TxCmdData);
    u8TxCmdAndPecData[0] = u8TxCmdData[0];
    u8TxCmdAndPecData[1] = u8TxCmdData[1];
    u8TxCmdAndPecData[2] = (uint8_t)(u16CmdPecData >> 8);
    u8TxCmdAndPecData[3] = (uint8_t)(u16CmdPecData);

    spiWriteAndRead(u8TxCmdAndPecData, 4, pu8RxData, (8 * u8TotalAfeNum));
}

static void writeAdbms6832SpiCmdAndData(uint8_t u8TxCmdData[2], uint8_t u8TotalAfeNum, uint8_t *pu8RxData)
{
    uint16_t u16CmdPecData;
    uint8_t u8TxCmdAndPecAndDataData[4 + HAL_ADBMS6832_SPI_RX_BUFFER_SIZE];
    uint8_t u8AfeIdx;
    uint8_t u8ByteIdx;
    uint8_t u8AfeDataIdx;


    u16CmdPecData = pec15_Calc(2, u8TxCmdData);
    u8TxCmdAndPecAndDataData[0] = u8TxCmdData[0];
    u8TxCmdAndPecAndDataData[1] = u8TxCmdData[1];
    u8TxCmdAndPecAndDataData[2] = (uint8_t)(u16CmdPecData >> 8);
    u8TxCmdAndPecAndDataData[3] = (uint8_t)(u16CmdPecData);

    for (u8AfeIdx = 0; u8AfeIdx < u8TotalAfeNum; u8AfeIdx++)
    {
        u8AfeDataIdx = u8AfeIdx * HAL_ADBMS6832_DATA_BYTE_NUM;

        for (u8ByteIdx = 0; u8ByteIdx < HAL_ADBMS6832_DATA_BYTE_NUM; u8ByteIdx++)
        {
            u8TxCmdAndPecAndDataData[4 + u8AfeDataIdx + u8ByteIdx] = pu8RxData[0 + u8AfeDataIdx + u8ByteIdx];
        }
    }

    spiWriteAndRead(u8TxCmdAndPecAndDataData, 4 + u8TotalAfeNum * HAL_ADBMS6832_DATA_BYTE_NUM, NULL, 0);
}

//=============================================================================================

static void writeAdbms6832RegData(uint16_t u16AfeCmd, 
                       uint8_t u8TotalAfeNum, 
                       uint8_t *pu8RxData
                      )
{
    uint8_t u8TxCmdData[2];


    u8TxCmdData[0] = (uint8_t)(u16AfeCmd >> 8);
    u8TxCmdData[1] = (uint8_t)(u16AfeCmd);
    gu8TxCmdData[0] = u8TxCmdData[0];
    gu8TxCmdData[1] = u8TxCmdData[1];
    
    writeAdbms6832SpiCmdAndData(u8TxCmdData, u8TotalAfeNum, pu8RxData);
}

static void readAdbms6832RegData(uint16_t u16AfeCmd, 
                       uint8_t u8TotalAfeNum, 
                       uint8_t *pu8RxData 
                      )
{
    uint8_t u8TxCmdData[2];


    u8TxCmdData[0] = (uint8_t)(u16AfeCmd >> 8);
    u8TxCmdData[1] = (uint8_t)(u16AfeCmd);
    gu8TxCmdData[0] = u8TxCmdData[0];
    gu8TxCmdData[1] = u8TxCmdData[1];
    
    writeAdbms6832SpiCmdAndReadData(u8TxCmdData, u8TotalAfeNum, pu8RxData);
}

static void startAdbms6832GpioConversion(uint8_t u8Owaux, uint8_t u8Pup, uint8_t u8Ch)
{
	uint8_t u8TxCmdData[2];
	

	u8TxCmdData[0] = 0x04 | u8Owaux;
	u8TxCmdData[1] = ( ((u8Pup << 7) | (((u8Ch >> 4) & 0x01) << 6)) | (u8Ch & 0x0F) ) | 0x10;
    gu8TxCmdData[0] = u8TxCmdData[0];
    gu8TxCmdData[1] = u8TxCmdData[1];

    writeAdbms6832SpiCmd(u8TxCmdData);
}

static void startAdbms6832CellVConversion(uint8_t u8Rd, uint8_t u8Cont, uint8_t u8Dcp, uint8_t u8Rstf, uint8_t u8Owcs)
{
    uint8_t u8TxCmdData[2];


    u8TxCmdData[0] = 0x02 | u8Rd;
    u8TxCmdData[1] = (u8Cont << 7) | (u8Dcp << 4) | (u8Rstf << 2) | (u8Owcs & 0x03) | 0x60; 
    gu8TxCmdData[0] = u8TxCmdData[0];
    gu8TxCmdData[1] = u8TxCmdData[1];

    writeAdbms6832SpiCmd(u8TxCmdData);
}

static void startAdbms6832Command(uint16_t u16AfeCmd)
{
    uint8_t u8TxCmdData[2];


    u8TxCmdData[0] = (uint8_t)(u16AfeCmd >> 8);
    u8TxCmdData[1] = (uint8_t)(u16AfeCmd); 
    gu8TxCmdData[0] = u8TxCmdData[0];
    gu8TxCmdData[1] = u8TxCmdData[1];

    writeAdbms6832SpiCmd(u8TxCmdData);
} 

//=============================================================================================

void HalAfeAdbms6832ReadAuXi(uint16_t u16AfeCmd)
{
    HalAfeAdbms6832StartWakeupIdle();

    readAdbms6832RegData(u16AfeCmd, gu8AfeOnlineNum, gu8ReadAfeRxDataBuf);
}

void HalAfeAdbms6832ReadCellV(uint16_t u16AfeCmd)
{
    HalAfeAdbms6832StartWakeupIdle();

    readAdbms6832RegData(u16AfeCmd, gu8AfeOnlineNum, gu8ReadAfeRxDataBuf);
}

void HalAfeAdbms6832StartGpioAdc(void)
{
    HalAfeAdbms6832StartWakeupIdle();
    startAdbms6832GpioConversion(ADAX_OW, ADAX_PUP, ADAX_CH);
}

void HalAfeAdbms6832StartCellAdc(void)
{
    HalAfeAdbms6832StartWakeupIdle();
    startAdbms6832CellVConversion(ADCV_RD, ADCV_CONT, ADCV_DCP, ADCV_RSTF, ADCV_OW);
}

void HalAfeAdbms6832StopCellBalance(void)
{
    HalAfeAdbms6832StartWakeupIdle();
    
    startAdbms6832Command(HAL_ADBMS6832_CMD_MUTEBAL);
}

void HalAfeAdbms6832StartCellBalance(void)
{
    HalAfeAdbms6832StartWakeupIdle();
    
    startAdbms6832Command(HAL_ADBMS6832_CMD_UNMUTEBAL);
}

void HalAfeAdbms6832SetCfgaCfgbDccDcto(uint8_t u8AfeIdx, bool* pbDccCell)
{
    uint8_t *pu8CfgaBuf = &gu8AfeCfgaBuf[u8AfeIdx * HAL_ADBMS6832_DATA_BYTE_NUM];
    uint8_t *pu8CfgbBuf = &gu8AfeCfgbBuf[u8AfeIdx * HAL_ADBMS6832_DATA_BYTE_NUM];
    typedef struct 
    {
        bool    bCfgReg; 
        uint8_t u8RegIdx; 
        uint8_t u8BitIdx; 
    }tDccMap;
    const tDccMap mDccBitMap[HAL_ADBMS6832_MAX_AFE_CELL_CHANNEL_NUM] = 
    {
        {false, 4, 0}, {false, 4, 1}, {false, 4, 2}, {false, 4, 3}, {false, 4, 4}, {false ,4, 5}, {false, 4, 6}, {false, 4, 7}, 
        {false, 5, 0}, {false, 5, 1}, {false, 5, 2}, {false, 5, 3}, {false, 5, 4}, {false, 5, 5}, {false, 5, 6}, {false, 5, 7},
        {true, 2, 0}, {true, 2, 1}
    };
    

    for (uint8_t u8Idx = 0; u8Idx < HAL_ADBMS6832_MAX_AFE_CELL_CHANNEL_NUM; u8Idx++)
    {
        if (mDccBitMap[u8Idx].bCfgReg == true)
        {
            if (pbDccCell[u8Idx] == true)
            {
                pu8CfgaBuf[mDccBitMap[u8Idx].u8RegIdx] |= (1 << mDccBitMap[u8Idx].u8BitIdx);
            }
            else
            {
                pu8CfgaBuf[mDccBitMap[u8Idx].u8RegIdx] &= ~(1 << mDccBitMap[u8Idx].u8BitIdx);
            }
        }
        else
        {
            if (pbDccCell[u8Idx] == true)
            {
                pu8CfgbBuf[mDccBitMap[u8Idx].u8RegIdx] |= (1 << mDccBitMap[u8Idx].u8BitIdx);
            }
            else
            {
                pu8CfgbBuf[mDccBitMap[u8Idx].u8RegIdx] &= ~(1 << mDccBitMap[u8Idx].u8BitIdx);
            }
        
        }
    }

    pu8CfgbBuf[3] &= ~0x3F;

    pu8CfgbBuf[3] |= 0x01;
}

static void halAfeAdbms6832ArrangeCfg(uint8_t* pu8WriteAfeOldDataBuf, uint8_t* pu8WriteAfeNewDataBuf)
{
    uint8_t u8AfeOldIdx;
    uint8_t u8AfeNewIdx;
    uint8_t u8AfeOldDataIdx;
    uint8_t u8AfeNewDataIdx;
    uint8_t u8Idx;
    uint16_t u16CalculateDataPec;
    

    u8AfeNewIdx = (gu8AfeOnlineNum - 1);

    for (u8AfeOldIdx = 0; u8AfeOldIdx < gu8AfeOnlineNum; u8AfeOldIdx++, u8AfeNewIdx--)
    {
        u8AfeOldDataIdx = u8AfeOldIdx * HAL_ADBMS6832_DATA_BYTE_NUM;

        u8AfeNewDataIdx = u8AfeNewIdx * HAL_ADBMS6832_DATA_BYTE_NUM;

        for (u8Idx = 0; u8Idx < HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG; u8Idx++) 
        {
            pu8WriteAfeNewDataBuf[u8AfeNewDataIdx + u8Idx] = pu8WriteAfeOldDataBuf[u8AfeOldDataIdx + u8Idx];
        }

        u16CalculateDataPec = pec10_calc(false, HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG, &pu8WriteAfeNewDataBuf[u8AfeNewDataIdx]);

        pu8WriteAfeNewDataBuf[u8AfeNewDataIdx + HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG] = (uint8_t)(u16CalculateDataPec >> 8);
        pu8WriteAfeNewDataBuf[u8AfeNewDataIdx + HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG + 1] = (uint8_t)(u16CalculateDataPec);
    }  
}

void HalAfeAdbms6832WriteCfgaDccDcto(void)
{
    uint8_t u8WriteAfeDataBuf[HAL_ADBMS6832_SPI_RX_BUFFER_SIZE];


    HalAfeAdbms6832StartWakeupIdle();

    halAfeAdbms6832ArrangeCfg(gu8AfeCfgaBuf, u8WriteAfeDataBuf);
    
    writeAdbms6832RegData(HAL_ADBMS6832_CMD_WRCFGA, gu8AfeOnlineNum, u8WriteAfeDataBuf);
}

void HalAfeAdbms6832WriteCfgbDccDcto(void)
{
    uint8_t u8WriteAfeDataBuf[HAL_ADBMS6832_SPI_RX_BUFFER_SIZE];


    HalAfeAdbms6832StartWakeupIdle();

    halAfeAdbms6832ArrangeCfg(gu8AfeCfgbBuf, u8WriteAfeDataBuf);

    writeAdbms6832RegData(HAL_ADBMS6832_CMD_WRCFGB, gu8AfeOnlineNum, u8WriteAfeDataBuf);
}

//=============================================================================================

void HalAfeAdbms6832ReadCfga(eTypeHalAfeAdbms6832State eAfeState)
{
    if (eAfeState == kHAL_AFE_ADBMS6832_INIT_STATE)
    {
        gu8AfeOnlineNum = HAL_ADBMS6832_MAX_AFE_IC_NUM;
    }

    HalAfeAdbms6832StartWakeupIdle();
    
    readAdbms6832RegData(HAL_ADBMS6832_CMD_RDCFGA, gu8AfeOnlineNum, gu8ReadAfeRxDataBuf);
}

void HalAfeAdbms6832ReadCfgb(void)
{
    HalAfeAdbms6832StartWakeupIdle();
    
    readAdbms6832RegData(HAL_ADBMS6832_CMD_RDCFGB, gu8AfeOnlineNum, gu8ReadAfeRxDataBuf);
}

static void halAfeAdbms6832SetCfgaRefonOn(uint8_t* pu8WriteAfeRxDataBuf)
{
    uint8_t u8AfeOldIdx;
    uint8_t u8AfeNewIdx;
    uint8_t u8AfeOldDataIdx;
    uint8_t u8AfeNewDataIdx;
    uint8_t u8Idx;
    uint16_t u16CalculateDataPec;
    

    u8AfeNewIdx = (gu8AfeOnlineNum - 1);

    for (u8AfeOldIdx = 0; u8AfeOldIdx < gu8AfeOnlineNum; u8AfeOldIdx++, u8AfeNewIdx--)
    {
        u8AfeOldDataIdx = u8AfeOldIdx * HAL_ADBMS6832_DATA_BYTE_NUM;

        u8AfeNewDataIdx = u8AfeNewIdx * HAL_ADBMS6832_DATA_BYTE_NUM;

        pu8WriteAfeRxDataBuf[u8AfeNewDataIdx + 0] = (gu8ReadAfeRxDataBuf[u8AfeOldDataIdx + 0] | 0x80);

        for (u8Idx = 1; u8Idx < HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG; u8Idx++) 
        {
            pu8WriteAfeRxDataBuf[u8AfeNewDataIdx + u8Idx] = gu8ReadAfeRxDataBuf[u8AfeOldDataIdx + u8Idx];
        }

        u16CalculateDataPec = pec10_calc(false, HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG, &pu8WriteAfeRxDataBuf[u8AfeNewDataIdx]);

        pu8WriteAfeRxDataBuf[u8AfeNewDataIdx + HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG] = (uint8_t)(u16CalculateDataPec >> 8);
        pu8WriteAfeRxDataBuf[u8AfeNewDataIdx + HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG + 1] = (uint8_t)(u16CalculateDataPec);
    }  
}

void HalAfeAdbms6832WriteCfgaRefon(void)
{
    uint8_t u8WriteAfeDataBuf[HAL_ADBMS6832_SPI_RX_BUFFER_SIZE];


    HalAfeAdbms6832StartWakeupIdle();

    halAfeAdbms6832SetCfgaRefonOn(u8WriteAfeDataBuf);

    writeAdbms6832RegData(HAL_ADBMS6832_CMD_WRCFGA, gu8AfeOnlineNum, u8WriteAfeDataBuf);
}

//=============================================================================================

void HalAfeAdbms6832StartWakeupIdle(void)
{
    uint8_t u8AfeIdx;


    gu8TxCmdData[0] = 0x00;
    gu8TxCmdData[1] = 0x00;
	spiReadByte(0x00);	

    if (geAfeCommDir == kHAL_AFE_ADBMS6832_COMM_DIR_SOUTH)
    {
        for (u8AfeIdx = 0; u8AfeIdx < (HAL_ADBMS6832_MAX_AFE_IC_NUM - 1); u8AfeIdx++)
        {
            spiReadByte(0x00);
        }
    }

    delay_cycles(AFE_WAKEUP_DELAY_400US);
}

//=============================================================================================

void HalAfeAdbms6832SetCommDir(eTypeHalAfeAdbms6832CommDirection eAfeCommDir)
{
    geAfeCommDir = eAfeCommDir;
}

void HalAfeAdbms6832EventRegisterCb(tfpHalAfeAdbms6832CbFun fpCbFun)
{
    gfpHalAfeAdbms6832CbFun = fpCbFun;
}

uint16_t* HalAfeAdbms6832GetDecodeAfeRxDataBuf(void)
{
   return(gu16DecodeAfeRxDataBuf);
}

uint8_t HalAfeAdbms6832GetAfeOnlineNum(void)
{
    return (gu8AfeOnlineNum);
}

//=============================================================================================

static void halAfeAdbms6832DecodeReturnData(uint8_t u8AfeNum)
{
    uint16_t u16ReceivedDataPec;
    uint16_t u16CalculateDataPec;
    uint8_t u8AfeIdx;
    uint8_t u8AfeDataIdx;
    uint8_t u8CellIdx;
    uint16_t u16CellV;
   

    gu8AfeOnlineNum = 0;
    for (u8AfeIdx = 0; u8AfeIdx < u8AfeNum; u8AfeIdx++)
    {
        u8AfeDataIdx = u8AfeIdx * HAL_ADBMS6832_DATA_BYTE_NUM;

        u16CalculateDataPec = pec10_calc(true, HAL_ADBMS6832_BYTE_NUM_IN_AFE_REG, &gu8ReadAfeRxDataBuf[u8AfeDataIdx]);

        for (u8CellIdx = 0; u8CellIdx < HAL_ADBMS6832_CELL_NUM_IN_AFE_REG; u8CellIdx++)
        {
            u16CellV = gu8ReadAfeRxDataBuf[u8AfeDataIdx] + (gu8ReadAfeRxDataBuf[u8AfeDataIdx + 1] << 8);
            
            gu16DecodeAfeRxDataBuf[u8CellIdx + u8AfeIdx * HAL_ADBMS6832_CELL_NUM_IN_AFE_REG] = u16CellV;

            u8AfeDataIdx += 2;
        }
        
        u16ReceivedDataPec = (gu8ReadAfeRxDataBuf[u8AfeDataIdx] << 8) + gu8ReadAfeRxDataBuf[u8AfeDataIdx + 1];
        
        u16ReceivedDataPec &= ~0xFC00;

        if (u16ReceivedDataPec == u16CalculateDataPec)
        {
            gbPecErrorBuf[u8AfeIdx] = false;
            gu8AfeOnlineNum ++;
        }
        else
        {
            gbPecErrorBuf[u8AfeIdx] = true;
        }

        u8AfeDataIdx += 2;
    }  
}

static void bootSwapToBank1(void)
{
    // 切換執行來源到 Bank1
    DL_SYSCTL_executeFromUpperFlashBank();

    delay_cycles(16000); 

    // 發出 INITDONE → MCU 自動 Reset 並執行 Bank1
    DL_SYSCTL_issueINITDONE();

    while (1);  // 永遠不會到這裡
}

static void bootSwapToBank0(void)
{
    // 切換執行來源到 Bank0
    DL_SYSCTL_executeFromLowerFlashBank();

    delay_cycles(16000); 

    DL_SYSCTL_resetDevice(DL_SYSCTL_RESET_BOOT);

    while (1);  // 永遠不會到這裡
}

static void fpSpiAfeAdbms6832SwTimerHandler(__far void *pvdest, uint16_t u16Evt, void *pvDataPtr)
{
    uint8_t u8Idx;
    static uint8_t u8Cnt = 0;


    if (u16Evt & kLIB_SW_TIMER_EVT_10_1_MS)
    {
        if (gbSpiCbEnd == true)
        {
            gbSpiCbEnd = false;

            if (gbSpiRdcfgaCbEnd == true)
            {
                gbSpiRdcfgaCbEnd = false;

                for (u8Idx = 0; u8Idx < (gu8AfeOnlineNum * HAL_ADBMS6832_DATA_BYTE_NUM); u8Idx++)
                {
                    gu8AfeCfgaBuf[u8Idx] = gu8ReadAfeRxDataBuf[u8Idx];
                }
            }

            if (gbSpiRdcfgbCbEnd == true)
            {
                gbSpiRdcfgbCbEnd = false;    

                for (u8Idx = 0; u8Idx < (gu8AfeOnlineNum * HAL_ADBMS6832_DATA_BYTE_NUM); u8Idx++)
                {
                    gu8AfeCfgbBuf[u8Idx] = gu8ReadAfeRxDataBuf[u8Idx];
                }
            }

            halAfeAdbms6832DecodeReturnData(gu8AfeOnlineNum);

            if (gfpHalAfeAdbms6832CbFun != NULL)
            {
                gfpHalAfeAdbms6832CbFun();
            }
  
        }
    }
    if (u16Evt & kLIB_SW_TIMER_EVT_500_MS) 
    {
        u8Cnt ++;
        if (u8Cnt > 20)
        {
            //bootSwapToBank1();
            //bootSwapToBank0();
        }
    }
    if (u16Evt & kLIB_SW_TIMER_EVT_1_S)
    {
        HalGpioToggleExample();
    }
}

static void halAfeAdbms6832SpiEvent(eTypeHalSpiEvent eEvent)
{
    uint8_t u16TxCmdData;

    
    switch (eEvent)
    {
        case kHAL_SPI_EVENT_DONE:
            u16TxCmdData = (uint16_t)(gu8TxCmdData[0] << 8) + (uint16_t)(gu8TxCmdData[1]);

            switch (u16TxCmdData)
            {
                case HAL_ADBMS6832_CMD_WRCFGA:
                case HAL_ADBMS6832_CMD_WRCFGB:
                    if (gfpHalAfeAdbms6832CbFun != NULL)
                    {
                        gfpHalAfeAdbms6832CbFun();
                    }
                    break;    
                case HAL_ADBMS6832_CMD_RDCFGA:
                    gbSpiRdcfgaCbEnd = true;
                    gbSpiCbEnd = true;
                    break;
                case HAL_ADBMS6832_CMD_RDCFGB:
                    gbSpiRdcfgbCbEnd = true;
                    gbSpiCbEnd = true;
                    break;
                case HAL_ADBMS6832_CMD_RDCVA:
                case HAL_ADBMS6832_CMD_RDCVB:
                case HAL_ADBMS6832_CMD_RDCVC:
                case HAL_ADBMS6832_CMD_RDCVD:
                case HAL_ADBMS6832_CMD_RDCVE:
                case HAL_ADBMS6832_CMD_RDCVF:
                case HAL_ADBMS6832_CMD_RDAUXA:
                case HAL_ADBMS6832_CMD_RDAUXB:
                case HAL_ADBMS6832_CMD_RDAUXC:
                case HAL_ADBMS6832_CMD_RDAUXD:
                case HAL_ADBMS6832_CMD_RDAUXE:
                    gbSpiCbEnd = true;
                    break;
            }
            break; 
        case kHAL_SPI_EVENT_BUSY:
            break;
        case kHAL_SPI_EVENT_ERROR:
            break;
    }
}

static void gpioInitTest(void)
{
    tHalGpioConfig mHalGpioConfig;

    
    mHalGpioConfig.eGpioIoType = kHAL_GPIO_OUTPUT;
    mHalGpioConfig.pmPort = GPIOB;
    mHalGpioConfig.u32Pin = DL_GPIO_PIN_29;
    mHalGpioConfig.eInversionType = kHAL_GPIO_INVERSION_DISABLE;
    mHalGpioConfig.eResistorType = kHAL_GPIO_RESISTOR_NONE;
    mHalGpioConfig.eHysteresisType = kHAL_GPIO_HYSTERESIS_DISABLE;
    mHalGpioConfig.eWakeUpType = kHAL_GPIO_WAKEUP_DISABLE;
    mHalGpioConfig.eDriveStrengthType = kHAL_GPIO_DRIVE_STRENGTH_LOW;
    mHalGpioConfig.eHizType = kHAL_GPIO_HIZ_DISABLE;
    HalGpioInit(&mHalGpioConfig);

    mHalGpioConfig.eGpioIoType = kHAL_GPIO_OUTPUT;
    mHalGpioConfig.pmPort = GPIOB;
    mHalGpioConfig.u32Pin = DL_GPIO_PIN_30;
    mHalGpioConfig.eInversionType = kHAL_GPIO_INVERSION_DISABLE;
    mHalGpioConfig.eResistorType = kHAL_GPIO_RESISTOR_NONE;
    mHalGpioConfig.eHysteresisType = kHAL_GPIO_HYSTERESIS_DISABLE;
    mHalGpioConfig.eWakeUpType = kHAL_GPIO_WAKEUP_DISABLE;
    mHalGpioConfig.eDriveStrengthType = kHAL_GPIO_DRIVE_STRENGTH_LOW;
    mHalGpioConfig.eHizType = kHAL_GPIO_HIZ_DISABLE;
    HalGpioInit(&mHalGpioConfig);

    mHalGpioConfig.eGpioIoType = kHAL_GPIO_OUTPUT;
    mHalGpioConfig.pmPort = GPIOC;
    mHalGpioConfig.u32Pin = DL_GPIO_PIN_3;
    mHalGpioConfig.eInversionType = kHAL_GPIO_INVERSION_DISABLE;
    mHalGpioConfig.eResistorType = kHAL_GPIO_RESISTOR_NONE;
    mHalGpioConfig.eHysteresisType = kHAL_GPIO_HYSTERESIS_DISABLE;
    mHalGpioConfig.eWakeUpType = kHAL_GPIO_WAKEUP_DISABLE;
    mHalGpioConfig.eDriveStrengthType = kHAL_GPIO_DRIVE_STRENGTH_LOW;
    mHalGpioConfig.eHizType = kHAL_GPIO_HIZ_DISABLE;
    HalGpioInit(&mHalGpioConfig);

    mHalGpioConfig.eGpioIoType = kHAL_GPIO_OUTPUT;
    mHalGpioConfig.pmPort = GPIOA;
    mHalGpioConfig.u32Pin = DL_GPIO_PIN_25;
    mHalGpioConfig.eInversionType = kHAL_GPIO_INVERSION_DISABLE;
    mHalGpioConfig.eResistorType = kHAL_GPIO_RESISTOR_NONE;
    mHalGpioConfig.eHysteresisType = kHAL_GPIO_HYSTERESIS_DISABLE;
    mHalGpioConfig.eWakeUpType = kHAL_GPIO_WAKEUP_DISABLE;
    mHalGpioConfig.eDriveStrengthType = kHAL_GPIO_DRIVE_STRENGTH_LOW;
    mHalGpioConfig.eHizType = kHAL_GPIO_HIZ_DISABLE;
    HalGpioInit(&mHalGpioConfig);
}    

void HalSpiAfeAdbms6832Init(void)
{
    HalGpioInitExample();

    gpioInitTest();

    gmHalSpi0Config.eChannel = kHAL_SPI_CHANNEL_0;
    gmHalSpi0Config.mConfig.eMode = kHAL_SPI_MODE_MASTER;

    /// [CH] : Mode 0, not used CS pin, use GPIO to be CS pin.
    gmHalSpi0Config.mConfig.eFrameFormat = kHAL_SPI_FRAME_FORMAT_3WIRE_SPO0_SPH0;

    gmHalSpi0Config.mConfig.eParity = kHAL_SPI_PARITY_NONE;
    gmHalSpi0Config.mConfig.eFrameSize = kHAL_SPI_FRAME_SIZE_BITS_8;
    gmHalSpi0Config.mConfig.eBitOrder = kHAL_SPI_BIT_ORDER_MSB_FIRST;
    gmHalSpi0Config.mConfig.eChipSelect = kHAL_SPI_CHIP_SELECT_NONE;

    /// [CH] : Baud rate
    gmHalSpi0Config.u32BitRate = 1600000;
    gmHalSpi0Config.fpSpiEvent = &halAfeAdbms6832SpiEvent;
    HalSpiOpen(&gmHalSpi0Config);

    LibSoftwareTimerHandlerOpen(fpSpiAfeAdbms6832SwTimerHandler, 0);

    gu8AfeOnlineNum = HAL_ADBMS6832_MAX_AFE_IC_NUM;
}