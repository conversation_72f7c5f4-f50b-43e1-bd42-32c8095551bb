/*
******************************************************************************
* @file     HalAfeAdbms6832.h
* <AUTHOR>
* @brief    XXXXXXXXXX

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __HAL_AFE_ADBMS6832_H__
#define	__HAL_AFE_ADBMS6832_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "HalAfe.h"
#include "Define.h" 

/* Global define ------------------------------------------------------------*/
#define HAL_ADBMS6832_MAX_AFE_IC_NUM             (MAX_BMU_NUM) 
#define HAL_ADBMS6832_MAX_AFE_CELL_CHANNEL_NUM   (18)
#define HAL_ADBMS6832_MAX_AFE_GPIO_CHAMMEL_NUM   (12)

/* Global typedef -----------------------------------------------------------*/
typedef struct 
{
    uint8_t u8AfeId;
    uint8_t u8ChId;
}tAfeAdbms6832PhysicalPosi;

typedef struct 
{	 
    uint16_t u16CellLogicIdx[HAL_ADBMS6832_MAX_AFE_IC_NUM][HAL_ADBMS6832_MAX_AFE_CELL_CHANNEL_NUM];
    uint16_t u16NtcLogicIdx[HAL_ADBMS6832_MAX_AFE_IC_NUM][HAL_ADBMS6832_MAX_AFE_GPIO_CHAMMEL_NUM];
    tAfeAdbms6832PhysicalPosi mCellPhysicalPosi[MAX_CELL_NUMBER];
    tAfeAdbms6832PhysicalPosi mNtcPhysicalPosi[MAX_CELL_NUMBER];
}tAfeAdbms6832IdxAndPosiConfig;

typedef enum 
{
    kHAL_AFE_ADBMS6832_CELLV_GROUP_A = 0,
    kHAL_AFE_ADBMS6832_CELLV_GROUP_B,
    kHAL_AFE_ADBMS6832_CELLV_GROUP_C,
    kHAL_AFE_ADBMS6832_CELLV_GROUP_D,
    kHAL_AFE_ADBMS6832_CELLV_GROUP_E,
    kHAL_AFE_ADBMS6832_CELLV_GROUP_F,
    kHAL_AFE_ADBMS6832_CELLV_UNSUPPORT
}eTypeHalAfeAdbms6832CellVGroup;

typedef enum 
{
    kHAL_AFE_ADBMS6832_GPIO_GROUP_A = 0,
    kHAL_AFE_ADBMS6832_GPIO_GROUP_B,
    kHAL_AFE_ADBMS6832_GPIO_GROUP_C,
    kHAL_AFE_ADBMS6832_GPIO_GROUP_D,
    kHAL_AFE_ADBMS6832_GPIO_GROUP_E,
    kHAL_AFE_ADBMS6832_GPIO_UNSUPPORT
}eTypeHalAfeAdbms6832GpioGroup;

typedef enum 
{
    kHAL_AFE_ADBMS6832_NORTH_INIT_START = 0,
    kHAL_AFE_ADBMS6832_NORTH_INIT_END,
    kHAL_AFE_ADBMS6832_SOUTH_INIT_START,
    kHAL_AFE_ADBMS6832_SOUTH_INIT_END,
    kHAL_AFE_ADBMS6832_ALL_INIT_START,
    kHAL_AFE_ADBMS6832_ALL_INIT_END,
    kHAL_AFE_ADBMS6832_ALL_INIT_FAIL
}eTypeHalAfeAdbms6832InitStatus;

typedef enum
{
    kHAL_AFE_ADBMS6832_COMM_DIR_NORTH = 0,
    kHAL_AFE_ADBMS6832_COMM_DIR_SOUTH
}eTypeHalAfeAdbms6832CommDirection;

typedef enum
{
    kHAL_AFE_ADBMS6832_NORMAL_STATE = 0,
    kHAL_AFE_ADBMS6832_INIT_STATE
}eTypeHalAfeAdbms6832State;

typedef enum
{
    kHAL_AFE_ADBMS6832_CELLV_USE = 0,
    kHAL_AFE_ADBMS6832_CELLV_BUSBAR,
    kHAL_AFE_ADBMS6832_CELLV_NONE_USE	
}eTypeHalAfeAdbms6832CellVUse;

typedef enum
{
    kHAL_AFE_ADBMS6832_GPIO_NTC = 0,
	kHAL_AFE_ADBMS6832_GPIO_AMBIENT,
	kHAL_AFE_ADBMS6832_GPIO_BUSBAR,
	kHAL_AFE_ADBMS6832_GPIO_OTHER,
	kHAL_AFE_ADBMS6832_GPIO_NONE_USE	
}eTypeHalAfeAdbms6832GpioUse;

/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void HalAfeAdbms6832Open(tLibFuncPtrRegEvtHandler fpEventHandler);
void HalAfeAdbms6832GetCellPhyPosi(uint16_t u16LogicIdx, uint8_t *pu8AfeId, uint8_t *pu8ChId);
void HalAfeAdbms6832GetNtcPhyPosi(uint16_t u16LogicIdx, uint8_t *pu8AfeId, uint8_t *pu8ChId);
void HalAfeAdbms6832GetDeviceCnt(uint8_t *pu8NorthCnt, uint8_t *pu8SouthCnt, uint8_t *pu8AfeNum);
uint8_t HalAfeAdbms6832GetComDir(void);
void HalAfeAdbms6832SetBroadcasrMsgTimer(uint16_t u16Sec);
uint8_t HalAfeAdbms6832GetState(void);
uint8_t HalAfeAdbms6832IsL1Protect(void);
uint8_t HalAfeAdbms6832IsL2Protect(void);
uint8_t HalAfeAdbms6832GetComDir(void);

#ifdef __cplusplus
}
#endif

#endif