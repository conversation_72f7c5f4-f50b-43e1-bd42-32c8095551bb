/*
******************************************************************************
* @file     AppSerialUartHvBBU.h
* <AUTHOR>
* @brief    XXXXXXXXXX

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

#ifndef _APP_SERIAL_UART_HV_BBU_H_
#define _APP_SERIAL_UART_HV_BBU_H_
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Public define ------------------------------------------------------------*/
void AppSerialUartHvBbuOpen(void);
void AppSerialUartSendMessage(uint8_t *pu8Str);

/* Public macro -------------------------------------------------------------*/
#ifdef __cplusplus
}
#endif


	

#endif /* _APP_SERIAL_UART_HV_BBU_H_ */

/************************ (C) COPYRIGHT *****END OF FILE****/    


