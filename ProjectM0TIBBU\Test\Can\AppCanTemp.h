#ifndef __APP_CAN_DEFINE_H__
#define __APP_CAN_DEFINE_H__
#include "HalCan.h"
#ifdef __cplusplus
extern "C" {
#endif
// Mao without function

#define MAO_DISSABLE 0
#define appBmsSetCellVoltage //
#define appBmsSetPackVoltage //
#define appBmsSetNtcVoltage //
#define appBmsSetScuCurrent //
#define appBmsGetScuVbat //
#define appBmsSetScuVbat //
#define appProjcetSetIrValue //

uint8_t MaoReturnUInt8();
#define appBmsIsScudIdReady MaoReturnUInt8
#define appBmsGetScuId MaoReturnUInt8
#define apiSysParGetSystemActiveFlag MaoReturnUInt8
#define apiFuGetFwChecksum MaoReturnUInt8
#define apiSysParGetChecksum MaoReturnUInt8
#define apiFuGetFwInternalChecksum MaoReturnUInt8
#define appProjectGetTimerCount MaoReturnUInt8
#define apiSystemFlagGetFlag1 MaoReturnUInt8
#define apiSystemFlagGetFlag2 MaoReturnUInt8
#define apiSystemFlagGetFlag3 MaoReturnUInt8
#define apiSystemFlagGetFlag4 MaoReturnUInt8
#define apiSystemFlagGetFlag5 MaoReturnUInt8
#define apiSystemFlagGetFlag6 MaoReturnUInt8
#define apiSysParGetHwVersion MaoReturnUInt8
#define apiFuGetFwVersion MaoReturnUInt8
#define appGaugeGetQStart MaoReturnUInt8
#define appGaugeGetRamSoc MaoReturnUInt8
#define appGaugeGetEndOfSoc MaoReturnUInt8
#define appGaugeGetDisplaySoc MaoReturnUInt8
#define appGaugeGetSoc0 MaoReturnUInt8
#define apiFuGetFwInternalVersion MaoReturnUInt8
#define appGaugeGetQPassCharge MaoReturnUInt8
#define appGaugeGetRPassCharge MaoReturnUInt8
#define apiSysParGetZeroCurrentValue MaoReturnUInt8
#define apiSysParGetMinChargeCurrentValue MaoReturnUInt8
#define appProjectGetScuId MaoReturnUInt8
#define apiSysParGetNtcNumber MaoReturnUInt8
#define apiSysParGetCellNumber MaoReturnUInt8
#define apiSysParGetPackNum MaoReturnUInt8
#define apiSysParGetCellBusbarNumber MaoReturnUInt8
#define apiSysParGetNtcBusbarNumber MaoReturnUInt8
#define apiSysParGetNtcAmbientNumber MaoReturnUInt8
#define apiSysParGetNtcOtherNumber MaoReturnUInt8
#define apiSysParGetPreDischargeTime MaoReturnUInt8
#define apiSysParGetPreDischargeThreshold MaoReturnUInt8
#define apiSysParGetTerminateVoltage MaoReturnUInt8
#define appGaugeGetQmax MaoReturnUInt8
#define appGaugeGetRM MaoReturnUInt8
#define apiSysParGetAfeNumInModule MaoReturnUInt8
#define apiSysParGetTotalAfeNumber MaoReturnUInt8
#define apiSysParGetBMUPassiveBalR MaoReturnUInt8
#define apiSysParGetBmuType MaoReturnUInt8
#define apiEkfParGetEcmParLength MaoReturnUInt8

bool MaoInputUInt16ReturnTrue(uint16_t u16);
#define appBalanceIsBalanceSet MaoInputUInt16ReturnTrue
#define apiSysParSetTerminateVoltage MaoInputUInt16ReturnTrue
#define appGaugeSetQmax MaoInputUInt16ReturnTrue
#define appGaugeSetRM MaoInputUInt16ReturnTrue
#define apiSysParSetQmax MaoInputUInt16ReturnTrue

bool MaoInputUInt16ReturnUInt8(uint16_t u16);
#define apiEkfParGetCellFcc MaoInputUInt16ReturnUInt8
#define apiEkfParGetEcmParPercentage MaoInputUInt16ReturnUInt8
#define apiEkfParGetEcmParVocv MaoInputUInt16ReturnUInt8
#define apiEkfParGetEcmParRp0 MaoInputUInt16ReturnUInt8
#define apiEkfParGetEcmParRp1 MaoInputUInt16ReturnUInt8
#define apiEkfParGetEcmParRp2 MaoInputUInt16ReturnUInt8
#define apiEkfParGetEcmParCp1 MaoInputUInt16ReturnUInt8
#define apiEkfParGetEcmParCp2 MaoInputUInt16ReturnUInt8
#define apiEkfParSetEcmParLength MaoInputUInt16ReturnUInt8

void MaoInputTwoUInt16(uint16_t u161, uint16_t u162);
#define apiEkfParSetCellFcc MaoInputTwoUInt16
#define apiEkfParSetEcmParPercentage MaoInputTwoUInt16
#define apiEkfParSetEcmParVocv MaoInputTwoUInt16
#define apiEkfParSetEcmParRp0 MaoInputTwoUInt16
#define apiEkfParSetEcmParRp1 MaoInputTwoUInt16
#define apiEkfParSetEcmParRp2 MaoInputTwoUInt16
#define apiEkfParSetEcmParCp1 MaoInputTwoUInt16
#define apiEkfParSetEcmParCp2 MaoInputTwoUInt16

int8_t MaoInputHalCanFrameReturnInt8(tHalCanFrame *pCanPkg);
#define ApiEthUdpPutCanPkgToFifo MaoInputHalCanFrameReturnInt8

#define CANBUS_PROTOCOL_VER 0x01
#define MAX_NOTE_MESSAGE_STRING_ITEM    100
// ApiSysPar.h
#define SYS_ACTIVE_FLAG_FULL_CHG_BY_MAX_CELL      0x0001
#define SYS_ACTIVE_FLAG_FULL_DSG_BY_MIN_CELL      0x0002
#define SYS_ACTIVE_FLAG_RELAY_BY_COMMAND        0x0004
#define SYS_ACTIVE_FLAG_RELAY_BY_BMS          0x0008
#define SYS_ACTIVE_FLAG_CELL_BALANCE_BY_MBMS      0x0010
#define SYS_ACTIVE_FLAG_CANCEL_IT_ALGORITHM       0x0020
#define SYS_ACTIVE_FLAG_BALANCE_BY_SOC          0x0040
#define SYS_ACTIVE_FLAG_CURRENT_BY_EMS          0x0080
#define SYS_ACTIVE_FLAG_DISABLE_DI_DETECTION        0x0100
#define SYS_ACTIVE_FLAG_DISABLE_EKF_RUNNING_STATUS  0x0200
// AppGauge.h
enum{
    P_CURRENT = 0,
    N_CURRENT
};

#ifdef __cplusplus
}
#endif
#endif /* _APP_SERIAL_UART_H_ */