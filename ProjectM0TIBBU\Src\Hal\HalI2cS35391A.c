/*
******************************************************************************
* @file     HalI2cS35391A.c
* <AUTHOR>
* @brief    This file is the HAL I2C function of  real-time clock with S35391A Driverlib

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include "HalRtc.h"
#include "HalI2c.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
#define HAL_RTC_CHECK_LOOP_CNT                     (480000) 
#define HAL_RTC_WRITE_TIME_CNT                     (16000000)  
#define HAL_RTC_CHECK_COUNT_THS                    (3)               
#define HAL_RTC_WRITE_RETRY_THS                    (5) 
#define S35391A_I2C_ADDRESS_WRITE                  (0x5A) 
#define S35391A_I2C_ADDRESS_READ                   (0x5B) 
/* Local typedef ------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/*Local variables*/
static tHalRtcCallbackHandler pmRtcPrescaler0Cb = NULL;
static tHalRtcCallbackHandler pmRtcPrescaler1Cb = NULL;
static tHalRtcCallbackHandler pmRtcReadyCb      = NULL;

/* Global variables ---------------------------------------------------------*/
uint32_t gu32RtcTimeCnt = 0;
uint32_t gu32RtcRdyCheckCnt = 0;
uint32_t gu32RtcWriteRetrykCnt = 0;
static tHalI2cMasterConfig gmHalI2c2Config ;
/* Local function prototypes ------------------------------------------------*/
static tFunRetunCode HalRtcCheckRdy(void);

static tFunRetunCode HalRtcCheckRdy(void)
{
    gu32RtcRdyCheckCnt = 0;
    //There is a short delay to test the RTC clock and determine whether it is normal(delay=6ms).
    //--------------------------------------------------------
    delay_cycles(HAL_RTC_CHECK_LOOP_CNT);
    if(gu32RtcRdyCheckCnt >= HAL_RTC_CHECK_COUNT_THS)
    {
        return(RES_SUCCESS);          
    }
    //------------------------------------------------------
    return(RES_ERROR_FAIL);
}

static uint8_t HexToReverseBCD(uint8_t u8Val)
{
    uint8_t u8ReverseBcd;
    u8ReverseBcd = (uint8_t)((u8Val % 10) | ((u8Val / 10) << 4));

    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x55) << 1) | ((u8ReverseBcd & 0xAA) >> 1));
    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x33) << 2) | ((u8ReverseBcd & 0xCC) >> 2));
    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x0F) << 4) | ((u8ReverseBcd & 0xF0) >> 4));
    return u8ReverseBcd;
}

static uint8_t HexToReverseBCD_Hour(uint8_t u8Val)
{
    uint8_t u8ReverseBcd;
    u8ReverseBcd = (uint8_t)((u8Val % 10) | ((u8Val / 10) << 4));
    u8ReverseBcd &= 0x3F;

    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x55) << 1) | ((u8ReverseBcd & 0xAA) >> 1));
    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x33) << 2) | ((u8ReverseBcd & 0xCC) >> 2));
    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x0F) << 4) | ((u8ReverseBcd & 0xF0) >> 4));
    return u8ReverseBcd;
}

static uint8_t CalculateWeekday(uint16_t u16Year, uint8_t u8Month, uint8_t u8Day)
{
    if (u8Month < 3) {
        u8Month += 12;
        u16Year -= 1;
    }
    uint16_t u16K = u16Year % 100;
    uint16_t u16J = u16Year / 100;
    uint8_t u16H = (u8Day + 13*(u8Month + 1)/5 + u16K + u16K/4 + u16J/4 + 5*u16J) % 7;

    // Zeller公式回傳: 0=Saturday, 1=Sunday, ...
    const uint8_t u8DowTable[7] = {7, 1, 2, 3, 4, 5, 6}; // S-35391A: 1=Sunday, ..., 7=Saturday
    return u8DowTable[u16H];
}

static uint8_t ReverseBCDToHex(uint8_t u8Bcd)
{
    uint8_t u8ReverseBcd;
    u8ReverseBcd = u8Bcd;
    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x55) << 1) | ((u8ReverseBcd & 0xAA) >> 1));
    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x33) << 2) | ((u8ReverseBcd & 0xCC) >> 2));
    u8ReverseBcd = (uint8_t)(((u8ReverseBcd & 0x0F) << 4) | ((u8ReverseBcd & 0xF0) >> 4));
    u8ReverseBcd = (uint8_t)((u8ReverseBcd >> 4) * 10 + (u8ReverseBcd & 0x0F));
    return u8ReverseBcd;
}

static uint8_t ReverseBCDToHex_Hour(uint8_t u8Bcd)
{
    u8Bcd = (uint8_t)(((u8Bcd & 0x55) << 1) | ((u8Bcd & 0xAA) >> 1));
    u8Bcd = (uint8_t)(((u8Bcd & 0x33) << 2) | ((u8Bcd & 0xCC) >> 2));
    u8Bcd = (uint8_t)(((u8Bcd & 0x0F) << 4) | ((u8Bcd & 0xF0) >> 4));

    u8Bcd &= 0x3F;

    uint8_t u8ReverseBcd = ((u8Bcd >> 4) * 10) + (u8Bcd & 0x0F);
    return u8ReverseBcd;
}

static tFunRetunCode HalResetRTC()
{
    uint8_t u8ResetCmd[1] = { 0xC0 };
    if (HalI2cMasterSend(&gmHalI2c2Config, 0x58, u8ResetCmd, 1) == RES_SUCCESS)
    {
        delay_cycles(32000 * 10); // 至少等一段時間，依需求可加長
        return (RES_SUCCESS);
    }
    return (RES_ERROR_FAIL);
}

//使用PA0與PA1當作S35391A的INT0與INT1的接口
//-----------------------------------------------------------------------------
void GPIOA_IRQHandler(void)
{
    uint32_t u32Status = DL_GPIO_getEnabledInterruptStatus(GPIOA, DL_GPIO_PIN_0 | DL_GPIO_PIN_1);

    if (u32Status & DL_GPIO_PIN_0) {
        // 處理 INT1 腳位事件
        DL_GPIO_clearInterruptStatus(GPIOA, DL_GPIO_PIN_0);
        if (pmRtcPrescaler0Cb != NULL)
        {
            pmRtcPrescaler0Cb();
        }
        gu32RtcRdyCheckCnt++;
    }

    if (u32Status & DL_GPIO_PIN_1) {
        // 處理 INT2 腳位事件
        DL_GPIO_clearInterruptStatus(GPIOA, DL_GPIO_PIN_1);
        if (pmRtcPrescaler1Cb != NULL)
        {
            pmRtcPrescaler1Cb();
        }
        gu32RtcTimeCnt++;
    }
}
//-----------------------------------------------------------------------------

/* Global function prototypes -----------------------------------------------*/
tFunRetunCode HalRtcOpen(void)
{
    gmHalI2c2Config.eChannel = kHAL_I2C1;
    gmHalI2c2Config.eFreq = kHAL_I2C_FREQUENCY_400k;
    HalI2cMasterInit(&gmHalI2c2Config); 

    if( HalResetRTC() != RES_SUCCESS)
    {
         return(RES_ERROR_INIT); 
    }

    if(HalRtcCheckRdy() == RES_SUCCESS)
    {
        return(RES_SUCCESS);
    }
    
    return(RES_ERROR_INIT);      
}

tFunRetunCode HalRtcClose(void)
{  
    return(RES_SUCCESS);
}

tFunRetunCode HalRtcSetupDate(uint16_t u16Year, uint8_t u8Month, uint8_t u8Day)
{
    uint8_t u8WriteBuf[7];
    gu32RtcWriteRetrykCnt = 0;
    tHalRtcDateTime mDateTime;

    HalRtcGetDateTime(&mDateTime);

    u8WriteBuf[0] = HexToReverseBCD(u16Year % 100);                             // 0x00: 年
    u8WriteBuf[1] = HexToReverseBCD(u8Month);                                   // 0x01: 月
    u8WriteBuf[2] = HexToReverseBCD(u8Day);                                     // 0x02: 日
    u8WriteBuf[3] = HexToReverseBCD(CalculateWeekday(u16Year, u8Month, u8Day)); // 0x03: 星期
    u8WriteBuf[4] = HexToReverseBCD_Hour(mDateTime.u8Hour); // 小時
    u8WriteBuf[5] = HexToReverseBCD(mDateTime.u8Minute); // 分
    u8WriteBuf[6] = HexToReverseBCD(mDateTime.u8Second); // 秒
                          
    while (gu32RtcWriteRetrykCnt < HAL_RTC_WRITE_RETRY_THS)
    {
        delay_cycles(32000);
        if (HalI2cMasterSend(&gmHalI2c2Config, S35391A_I2C_ADDRESS_WRITE, u8WriteBuf, 7) == RES_SUCCESS)
        {
            return (RES_SUCCESS);
        }                   
        gu32RtcWriteRetrykCnt++;
    }
    return (RES_ERROR_FAIL);
}

tFunRetunCode HalRtcSetupTime(uint8_t u8Hour, uint8_t u8Min, uint8_t u8Sec)
{
    gu32RtcWriteRetrykCnt = 0;
    uint8_t u8WriteBuf[7];
    tHalRtcDateTime mDateTime;

    HalRtcGetDateTime(&mDateTime);

    u8WriteBuf[0] = HexToReverseBCD(mDateTime.u16Year % 100);                             // 0x00: 年
    u8WriteBuf[1] = HexToReverseBCD(mDateTime.u8Month);                                   // 0x01: 月
    u8WriteBuf[2] = HexToReverseBCD(mDateTime.u8Day);                                     // 0x02: 日
    u8WriteBuf[3] = HexToReverseBCD(CalculateWeekday(mDateTime.u16Year, mDateTime.u8Month, mDateTime.u8Day)); // 0x03: 星期
    u8WriteBuf[4] = HexToReverseBCD_Hour(u8Hour); // 小時
    u8WriteBuf[5] = HexToReverseBCD(u8Min); // 分
    u8WriteBuf[6] = HexToReverseBCD(u8Sec); // 秒

    while (gu32RtcWriteRetrykCnt < HAL_RTC_WRITE_RETRY_THS)
    {
        delay_cycles(32000);
            if (HalI2cMasterSend(&gmHalI2c2Config, 0x5A, u8WriteBuf, 7) == RES_SUCCESS)
            {               
                return RES_SUCCESS;
            }

        gu32RtcWriteRetrykCnt++;
    }

    return RES_ERROR_FAIL;
}

void HalRtcGetDateTime(tHalRtcDateTime *pmDateTime)
{
    uint8_t u8Buf[7] = {0};

    delay_cycles(32000); 

    HalI2cMasterRead(&gmHalI2c2Config, 0x5A, u8Buf, 7);

    pmDateTime->u16Year  = 2000 + ReverseBCDToHex(u8Buf[0]); // 年(2碼)，加上2000年
    pmDateTime->u8Month  = ReverseBCDToHex(u8Buf[1]); // 月
    pmDateTime->u8Day    = ReverseBCDToHex(u8Buf[2]); // 日期
    pmDateTime->u8Hour   = ReverseBCDToHex_Hour(u8Buf[4]);
    pmDateTime->u8Minute = ReverseBCDToHex(u8Buf[5]);
    pmDateTime->u8Second = ReverseBCDToHex(u8Buf[6]);  
}

/* Unix Time calculated from 2000/1/1 00:00:00 UTC (Self define, since uint32_t format can represent space saving)*/
uint32_t HalRtcGetSelfUnixTime(void)
{
	tHalRtcDateTime mRtcDateTime;
    HalRtcGetDateTime(&mRtcDateTime);  // 用 HalRtcGetDateTime 抓目前 RTC 時間（來自 S-35391A）

    const uint16_t u16DaysInMonth[12] = {
        31,28,31,30,31,30,
        31,31,30,31,30,31
    };

    uint32_t u32Days = 0;

    for (uint16_t u16Y = 2000; u16Y < mRtcDateTime.u16Year; u16Y++) 
    {
        u32Days += ((u16Y % 4 == 0 && u16Y % 100 != 0) || (u16Y % 400 == 0)) ? 366 : 365;
    }

    for (uint8_t u32M = 1; u32M < mRtcDateTime.u8Month; u32M++) 
    {
        u32Days += u16DaysInMonth[u32M - 1];
        if (u32M == 2 && ((mRtcDateTime.u16Year % 4 == 0 && mRtcDateTime.u16Year % 100 != 0) || (mRtcDateTime.u16Year % 400 == 0))) 
        {
            u32Days += 1; // 潤年
        }
    }

    u32Days += mRtcDateTime.u8Day - 1;

    uint32_t u32Seconds = u32Days * 86400UL;
    u32Seconds += mRtcDateTime.u8Hour * 3600UL;
    u32Seconds += mRtcDateTime.u8Minute * 60UL;
    u32Seconds += mRtcDateTime.u8Second;

    return u32Seconds;
}

void HalRtcSelfUnixTimeToDateTime(uint32_t u32Sec, tHalRtcDateTime *pmRtcDateTime)
{
    uint8_t u8Index;
    uint16_t u16Year = 2000;
    uint8_t u8MonthDay[12] = {31,28,31,30,31,30,31,31,30,31,30,31};
    uint32_t u32Days, u32SecondsInDay;

    // 取出日數與當天秒數
    u32Days = u32Sec / 86400UL;
    u32SecondsInDay = u32Sec % 86400UL;

    pmRtcDateTime->u8Hour   = u32SecondsInDay / 3600;
    pmRtcDateTime->u8Minute = (u32SecondsInDay % 3600) / 60;
    pmRtcDateTime->u8Second = u32SecondsInDay % 60;

    // 找年份
    while (1) {
        uint16_t u16DaysInYear = ((u16Year % 4 == 0 && u16Year % 100 != 0) || (u16Year % 400 == 0)) ? 366 : 365;
        if (u32Days < u16DaysInYear)
            break;
        u32Days -= u16DaysInYear;
        u16Year++;
    }

    // 更新 2 月天數（是否為潤年）
    if ((u16Year % 4 == 0 && u16Year % 100 != 0) || (u16Year % 400 == 0)) {
        u8MonthDay[1] = 29;
    }

    // 找月份與日期
    for (u8Index = 0; u8Index < 12; u8Index++) {
        if (u32Days < u8MonthDay[u8Index]) {
            break;
        }
        u32Days -= u8MonthDay[u8Index];
    }

    pmRtcDateTime->u16Year = u16Year;
    pmRtcDateTime->u8Month = u8Index + 1;
    pmRtcDateTime->u8Day   = u32Days + 1;
}

bool HalRtcIsRtcValid(void)
{
    tHalRtcDateTime mTime;

    HalRtcGetDateTime(&mTime);  // 呼叫不回傳狀態的 I2C 函數

    // 根據讀到的值進行合法性判斷
    if ((mTime.u8Second <= 59) &&
        (mTime.u8Minute <= 59) &&
        (mTime.u8Hour   <= 23) &&
        (mTime.u8Month >= 1 && mTime.u8Month <= 12) &&
        (mTime.u8Day   >= 1 && mTime.u8Day <= 31) &&
        (mTime.u16Year >= 2000 && mTime.u16Year <= 2099)) {
        return true;
    }

    return false;
}

void HalRtcRegisterPrescaler0Callback(tHalRtcCallbackHandler mCallBackfun) {
    pmRtcPrescaler0Cb = mCallBackfun;
}

void HalRtcRegisterPrescaler1Callback(tHalRtcCallbackHandler mCallBackfun) {
    pmRtcPrescaler1Cb = mCallBackfun;
}

void HalRtcRegisterReadyCallback(tHalRtcCallbackHandler mCallBackfun) {
    pmRtcReadyCb = mCallBackfun;
}

uint32_t HalRtcGetPrescaler1Cnt(void)
{
    return(gu32RtcTimeCnt);
}

void HalRtcSetPrescaler1Cnt(uint32_t u32SetCnt)
{
    gu32RtcTimeCnt = u32SetCnt;
}
