/**
  ******************************************************************************
  * @file        AppSerialCanDavinciBaseCmd.c
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2021/11/19
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

#include "HalAfe.h"

#include "HalRtc.h"
#include "HalCan.h"

#include "LibSoftwareTimerHandler.h"
#include "AppSerialCanDavinci.h"
#include "AppSerialCanDavinciCmd.h"
#include "AppSerialCanDavinciParameter.h"
#include "AppSerialCanDavinciNotification.h"
#include "AppSerialCanDavinciBaseCmd.h"
//#include "AppBms.h"
//#include "AppScuIdAssign.h"


void appSerialCanDavinciSendTextMessage(char *str);
#define	canCmdDebugMsg(str)	appSerialCanDavinciSendTextMessage(str)

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/

static uint8_t scuDetailMsgsn_num = 0xFF;
static void scuDetailMsgEndFinish(tHalCanFrame *pCanPkg)
{

	uint8_t	scuid;
	uint16_t subindex = 0;
	
	scuid = SMP_CAN_GET_SCU_ID(pCanPkg -> u32Id);
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	if(scuDetailMsgsn_num == subindex){
		return;
	}
	scuDetailMsgsn_num = subindex;
	appSerialCanDavinciNotificationSetLastBroadcastScuId(scuid);
}

//-------------------------------------------------------------------
SMP_CAN_DECODE_CMD_START(mDavinciCanBaseRxTxTab)
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_BASE_TX, 0,
									SMP_BASE_DETAIL_MSG_SEND_END_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								scuDetailMsgEndFinish)

SMP_CAN_DECODE_CMD_END();


/* Public function prototypes -----------------------------------------------*/
void DavinciCanFunBaseRxTx(tHalCanFrame *pCanPkg)
{
	uint8_t cmdIndex;
	
//	char	str[100];
//	char	str1[100];
	
	//appSerialCanDavinciPutPkgToCanFifo(pCanPkg);
	//appSerialCanDavinciSendTextMessage("Decode Rx Cmd");

 	cmdIndex = 0;
	for(cmdIndex = 0; mDavinciCanBaseRxTxTab[cmdIndex].fun != 0; cmdIndex++)
	{
		if((mDavinciCanBaseRxTxTab[cmdIndex].canid & mDavinciCanBaseRxTxTab[cmdIndex].mask) == 
		   (mDavinciCanBaseRxTxTab[cmdIndex].mask & pCanPkg -> u32Id))// &&
		   //appSerialCanDavinciIsCorrectScuId(pCanPkg))
		{
			mDavinciCanBaseRxTxTab[cmdIndex].fun(pCanPkg);
		
			break; 		
 		}
	}
}

void DavinciCanFunBaseSetDetailSnID(uint8_t id){
	scuDetailMsgsn_num = id;
}


/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    
