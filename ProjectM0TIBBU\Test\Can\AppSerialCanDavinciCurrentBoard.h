/**
  ******************************************************************************
  * @file        AppSerialCanDavinciCurrentBoard.h
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2025/3/10
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2025 Chris</center></h2>
  *
  *
  ******************************************************************************
  */

#ifndef _APP_SERIAL_CAN_DAVINCI_CURRENT_BOARD_H_
#define _APP_SERIAL_CAN_DAVINCI_CURRENT_BOARD_H_
/* Includes ------------------------------------------------------------------*/
#include "AppCanTemp.h"

#ifdef __cplusplus
extern "C" {
#endif
/* Public typedef -----------------------------------------------------------*/
/* Public define ------------------------------------------------------------*/
/* Public macro -------------------------------------------------------------*/

/* Public function prototypes -----------------------------------------------*/
void DavinciCanFunCb(tHalCanFrame *pCanPkg);


#ifdef __cplusplus
}
#endif


	

#endif /* _APP_SERIAL_CAN_DAVINCI_CURRENT_BOARD_H_ */

/************************ (C) COPYRIGHT Chris Tsai *****END OF FILE****/    