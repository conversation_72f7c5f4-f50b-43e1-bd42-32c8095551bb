/*
******************************************************************************
* @file     ApiProtectCotp.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include "Define.h"
#include "HalAfe.h"
#include "ApiProtect.h"
#include "LibSysPar.h"

void AppSerialUartSendMessage(char *str);

/* Private define ------------------------------------------------------------*/
#define	API_PROTECT_COTP_DEBUG_MSG(pc8Msg)		AppSerialUartSendMessage(pc8Msg)
#define	API_PROTECT_COTP_CHECK_NUM_PER_TIME	    20
#define	API_PROTECT_COTP_GET_NTC_NUMBER()		LibSysParGetNtcNumber()
#define API_PROTECT_COTP_GET_NTC_VOLTAGE(u16Ntcs)      HalAfeGetNtcVoltage(u16Ntcs);

#define	API_PROTECT_COTP_GET_COTP_PAR(u8ProtectLevel, mProtectPar) 		LibSysParGetCotpProtectPar(u8ProtectLevel, mProtectPar)
#define	API_PROTECT_COTP_GET_LEVEL_MASK(u8ProtectLevel, mProtectFlagValue)	ApiProtectGetLevelMask(u8ProtectLevel, mProtectFlagValue)
#define API_PROTECT_COTP_IS_OT(u16NtcVoltage, u32ParValue)  ApiProtectIsOverTemperter(u16NtcVoltage, u32ParValue)
#define API_PROTECT_COTP_IS_UT(u16NtcVoltage, u32ParValue)  ApiProtectIsUnderTemperter(u16NtcVoltage, u32ParValue)

/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t	u8Flag[MAX_NTC_NUMBER];
	uint8_t	u8SetCount[API_PROTECT_LEVEL][MAX_NTC_NUMBER];
	uint8_t	u8ReleaseCount[API_PROTECT_LEVEL][MAX_NTC_NUMBER];
	tfpApiProtectEvtHandler  fpEvtHandler;
}tCotpProtect;

static tCotpProtect	gmCotpProtect={0};
static uint16_t	gu16CotpNtcIndex = 0;
static bool gbCotpEnable = 0;
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
static void ApiProtectCotpProtectIni(void)
{
	gu16CotpNtcIndex = 0;
}
/* Public function prototypes -----------------------------------------------*/
uint8_t	ApiProtectCotpGetFlag(uint16_t u16NtcIndex)
{
	return gmCotpProtect.u8Flag[u16NtcIndex];
}


uint8_t ApiProtectCotpHandler(uint8_t u8ProtectLevel)
{
	uint8_t		u8Count = 0;
	
	uint16_t    u16NtcVoltage;
	tProtectFlagValue	mProtectFlagValue;
	tScuProtectPar		mProtectPar;

	if (gbCotpEnable == 0)
	{
		return 1;
	}
	
	API_PROTECT_COTP_GET_COTP_PAR(u8ProtectLevel, &mProtectPar);
	API_PROTECT_COTP_GET_LEVEL_MASK(u8ProtectLevel, &mProtectFlagValue);

	while(1)
	{			
		u16NtcVoltage = API_PROTECT_COTP_GET_NTC_VOLTAGE(gu16CotpNtcIndex);
		if(gu16CotpNtcIndex == 0)
		{
		    char    str[100];
		    sprintf(str,"cotp%d %d %d", 
		        u8ProtectLevel,
		        u16NtcVoltage,mProtectPar.mSetValue.u32Value);
		    //API_PROTECT_COTP_DEBUG_MSG(str);
		}

		if( (mProtectPar.mSTime.u32Value != 0) &&
		    (API_PROTECT_COTP_IS_OT(u16NtcVoltage, mProtectPar.mSetValue.u32Value) != 0))
		{
			if((gmCotpProtect.u8Flag[gu16CotpNtcIndex] & mProtectFlagValue.u8Mask) == 0)
			{
				gmCotpProtect.u8Flag[gu16CotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
				gmCotpProtect.u8Flag[gu16CotpNtcIndex] |= mProtectFlagValue.u8Setting;
				gmCotpProtect.u8SetCount[u8ProtectLevel][gu16CotpNtcIndex] = 1;
			}	
			else if((gmCotpProtect.u8Flag[gu16CotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
			{
				gmCotpProtect.u8SetCount[u8ProtectLevel][gu16CotpNtcIndex]++;
				if(gmCotpProtect.u8SetCount[u8ProtectLevel][gu16CotpNtcIndex] >= mProtectPar.mSTime.u32Value)
				{
					gmCotpProtect.u8Flag[gu16CotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
					gmCotpProtect.u8Flag[gu16CotpNtcIndex] |= mProtectFlagValue.u8Setted;
					gmCotpProtect.u8SetCount[u8ProtectLevel][gu16CotpNtcIndex] = 0;

					if(gmCotpProtect.fpEvtHandler != 0)
					{
						gmCotpProtect.fpEvtHandler(0, kAPI_PROTECT_COTP_L1_SET + u8ProtectLevel, &gu16CotpNtcIndex);	
					}
				}
			}
		}
		else if((gmCotpProtect.u8Flag[gu16CotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
		{
			gmCotpProtect.u8Flag[gu16CotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
		}
		//--------------------------------------------------------------------------
		//	Level	Release
		if(API_PROTECT_COTP_IS_UT(u16NtcVoltage, mProtectPar.mRelValue.u32Value) && 
		   mProtectPar.mRTime.u32Value != 0)
		{
			if((gmCotpProtect.u8Flag[gu16CotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setted)
			{
				gmCotpProtect.u8Flag[gu16CotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
				gmCotpProtect.u8Flag[gu16CotpNtcIndex] |= mProtectFlagValue.u8Releasing;
				gmCotpProtect.u8ReleaseCount[u8ProtectLevel][gu16CotpNtcIndex] = 1;
			}	
			else if((gmCotpProtect.u8Flag[gu16CotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
			{
				gmCotpProtect.u8ReleaseCount[u8ProtectLevel][gu16CotpNtcIndex]++;
				if(gmCotpProtect.u8ReleaseCount[u8ProtectLevel][gu16CotpNtcIndex] >= mProtectPar.mRTime.u32Value)
				{
					gmCotpProtect.u8Flag[gu16CotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
					gmCotpProtect.u8ReleaseCount[u8ProtectLevel][gu16CotpNtcIndex] = 0;
					if(gmCotpProtect.fpEvtHandler != 0)
					{
						gmCotpProtect.fpEvtHandler(0, kAPI_PROTECT_COTP_L1_RELEASE + u8ProtectLevel, &gu16CotpNtcIndex);
					}
				}
			}
		}
		else if((gmCotpProtect.u8Flag[gu16CotpNtcIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
		{
			gmCotpProtect.u8Flag[gu16CotpNtcIndex] &= mProtectFlagValue.u8ClearMask;
			gmCotpProtect.u8Flag[gu16CotpNtcIndex] |= mProtectFlagValue.u8Setted;
		}
		gu16CotpNtcIndex++;
		if(gu16CotpNtcIndex >= API_PROTECT_COTP_GET_NTC_NUMBER())
		{
			gu16CotpNtcIndex = 0;
			return 1;
		}
		u8Count++;
		if(u8Count >= API_PROTECT_COTP_CHECK_NUM_PER_TIME)
			break;
	}
	return 0;
}

void ApiProtectCotpOpen(tfpApiProtectEvtHandler fpEvtHandler)
{
	ApiProtectCotpProtectIni();
	
	gbCotpEnable = 1;
	
	gmCotpProtect.fpEvtHandler = fpEvtHandler;
}

/************************ (C) COPYRIGHT *****END OF FILE****/    

