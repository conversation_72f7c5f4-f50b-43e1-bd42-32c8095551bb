/*
******************************************************************************
* @file     TestHalRtc.h
* <AUTHOR>
* @brief    This file is Test HalRtc function

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __TEST_HAL_RTC_H__
#define	__TEST_HAL_RTC_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include <stdint.h>
#include "Bsp.h"
#include "HalGpio.h"
#include "HalRtc.h"

/* Global define ------------------------------------------------------------*/
#define TEST_HAL_RTC_HALRTCCLOSE_FUN
#define TEST_HAL_RTC_ON_AFTER_OFF_TIME_CNT          (120)

#define TEST_HAL_RTC_SETUP_YEAR                     (2025)
#define TEST_HAL_RTC_SETUP_MONTH                    (5)
#define TEST_HAL_RTC_SETUP_DAY                      (16)
#define TEST_HAL_RTC_SETUP_HOUR                     (18)
#define TEST_HAL_RTC_SETUP_MINUTE                   (19)
#define TEST_HAL_RTC_SETUP_SECOND                   (48)
/* Global typedef -----------------------------------------------------------*/
/* Global macro -------------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
void TestHalRtcApp(void);

#ifdef __cplusplus
}
#endif

#endif
