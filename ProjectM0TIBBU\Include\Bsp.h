/*
******************************************************************************
* @file     Bsp.h
* <AUTHOR>
* @brief    This file include MSPM0G3519 Board Support Package Define.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
#ifndef __BSP_H__
#define __BSP_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes -----------------------------------------------------------------*/
#include "Main.h"

/* Global typedef -----------------------------------------------------------*/
typedef enum
{
    kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_00 = 0,
    kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_01,
    kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_02,
    kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_03,
    kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_04,
    kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_05,
    kBSP_DMA_CHANNEL_SINGLE_TRANSFER_06,
    kBSP_DMA_CHANNEL_SINGLE_TRANSFER_07,
    kBSP_DMA_CHANNEL_SINGLE_TRANSFER_08,
    kBSP_DMA_CHANNEL_SINGLE_TRANSFER_09,
    kBSP_DMA_CHANNEL_SINGLE_TRANSFER_10,
    kBSP_DMA_CHANNEL_SINGLE_TRANSFER_11,
    kBSP_DMA_CHANNEL_NONE,
} eTypeBspDmaChannel;

/* Global define ------------------------------------------------------------*/

#define BSP_DELAY_CYCLES(num)                       (delay_cycles(num))

/* Define to HalGpio.h typedef enum start */
#define BSP_LED_PORT                                (GPIOB)
#define BSP_LED1_PIN                                (DL_GPIO_PIN_22)
#define BSP_LED1_IOMUX                              (IOMUX_PINCM50)
#define BSP_LED2_PIN                                (DL_GPIO_PIN_26)
#define BSP_LED2_IOMUX                              (IOMUX_PINCM57)
#define BSP_LED3_PIN                                (DL_GPIO_PIN_27)
#define BSP_LED3_IOMUX                              (IOMUX_PINCM58)

#define BSP_UART_0                                  (UART0)
#define BSP_UART_0_TX_PORT                          (GPIOA)
#define BSP_UART_0_RX_PORT                          (GPIOA)
#define BSP_UART_0_TX_PIN                           (DL_GPIO_PIN_10)
#define BSP_UART_0_RX_PIN                           (DL_GPIO_PIN_11)
#define BSP_UART_0_TX_IOMUX                         (IOMUX_PINCM21)
#define BSP_UART_0_TX_IOMUX_PF                      (IOMUX_PINCM21_PF_UART0_TX)
#define BSP_UART_0_RX_IOMUX                         (IOMUX_PINCM22)
#define BSP_UART_0_RX_IOMUX_PF                      (IOMUX_PINCM22_PF_UART0_RX)

#define BSP_UART_1                                  (UART1)
#define BSP_UART_1_TX_PORT                          (GPIOA)
#define BSP_UART_1_RX_PORT                          (GPIOA)
#define BSP_UART_1_TX_PIN                           (DL_GPIO_PIN_8)
#define BSP_UART_1_RX_PIN                           (DL_GPIO_PIN_9)
#define BSP_UART_1_TX_IOMUX                         (IOMUX_PINCM19)
#define BSP_UART_1_TX_IOMUX_PF                      (IOMUX_PINCM19_PF_UART1_TX)
#define BSP_UART_1_RX_IOMUX                         (IOMUX_PINCM20)
#define BSP_UART_1_RX_IOMUX_PF                      (IOMUX_PINCM20_PF_UART1_RX)
/* Define to HalGpio.h typedef enum end */

/* Define to HalGpio.k #define start */
// HalMcuPeropheralConfig
//-------------------------------------------------------------------
#define BSP_LFXT_OUT_PORT                           (GPIOA)
#define BSP_LFXT_OUT_PIN                            (DL_GPIO_PIN_4)
#define BSP_LFXT_OUT_IOMUX                          (IOMUX_PINCM9)

#define BSP_LFXT_IN_PORT                            (GPIOA)
#define BSP_LFXT_IN_PIN                             (DL_GPIO_PIN_3)
#define BSP_LFXT_IN_IOMUX                           (IOMUX_PINCM8)

#define BSP_HFXT_OUT_PORT                           (GPIOA)
#define BSP_HFXT_OUT_PIN                            (DL_GPIO_PIN_6)
#define BSP_HFXT_OUT_IOMUX                          (IOMUX_PINCM11)            

#define BSP_HFXT_IN_PORT                            (GPIOA)
#define BSP_HFXT_IN_PIN                             (DL_GPIO_PIN_5)
#define BSP_HFXT_IN_IOMUX                           (IOMUX_PINCM10) 

#define BSP_DEBUG0_PORT                             (GPIOA)
#define BSP_DEBUG0_PIN                              (DL_GPIO_PIN_14)
#define BSP_DEBUG0_IOMUX                            (IOMUX_PINCM36) 

#define BSP_DEBUG1_PORT                             (GPIOA)
#define BSP_DEBUG1_PIN                              (DL_GPIO_PIN_8)
#define BSP_DEBUG1_IOMUX                            (IOMUX_PINCM19) 

#define BSP_DEBUG2_PORT                             (GPIOA)
#define BSP_DEBUG2_PIN                              (DL_GPIO_PIN_9)
#define BSP_DEBUG2_IOMUX                            (IOMUX_PINCM20) 

#if 1
#define BSP_MCU_EVB3519_LED_FUNCTION
#endif

#ifdef BSP_MCU_EVB3519_LED_FUNCTION
#define BSP_EVB_RED_LED_PORT                        (GPIOB)
#define BSP_EVB_RED_LED_PIN                         (DL_GPIO_PIN_26)
#define BSP_EVB_RED_LED_IOMUX                       (IOMUX_PINCM57)

#define BSP_EVB_GREEN_LED_PORT                      (GPIOB)
#define BSP_EVB_GREEN_LED_PIN                       (DL_GPIO_PIN_27)
#define BSP_EVB_GREEN_LED_IOMUX                     (IOMUX_PINCM58)

#define BSP_EVB_BLUE_LED_PORT                       (GPIOB)
#define BSP_EVB_BLUE_LED_PIN                        (DL_GPIO_PIN_22)
#define BSP_EVB_BLUE_LED_IOMUX                      (IOMUX_PINCM50)
#endif
//-------------------------------------------------------------------
//ADC
//-------------------------------------------------------------------
#define BSP_ADC_0_PORT                              (GPIOA)
#define BSP_ADC_0_PIN                               (DL_GPIO_PIN_25)
#define BSP_ADC_1_PORT                              (GPIOA)
#define BSP_ADC_1_PIN                               (DL_GPIO_PIN_24)
#define BSP_ADC_2_PORT                              (GPIOA)
#define BSP_ADC_2_PIN                               (DL_GPIO_PIN_15)
#define BSP_ADC_3_PORT                              (GPIOA)
#define BSP_ADC_3_PIN                               (DL_GPIO_PIN_16)

//-------------------------------------------------------------------
#define BSP_CAN_0_TX_PORT                           (GPIOA)
#define BSP_CAN_0_TX_PIN                            (DL_GPIO_PIN_26)
#define BSP_CAN_0_TX_IOMUX                          (IOMUX_PINCM59)
#define BSP_CAN_0_TX_IOMUX_PF                       (IOMUX_PINCM59_PF_CANFD0_CANTX)

#define BSP_CAN_0_RX_PORT                           (GPIOA)
#define BSP_CAN_0_RX_PIN                            (DL_GPIO_PIN_27)
#define BSP_CAN_0_RX_IOMUX                          (IOMUX_PINCM60)
#define BSP_CAN_0_RX_IOMUX_PF                       (IOMUX_PINCM60_PF_CANFD0_CANRX)

#define BSP_CAN_1_TX_PORT                           (GPIOC)
#define BSP_CAN_1_TX_PIN                            (DL_GPIO_PIN_26)
#define BSP_CAN_1_TX_IOMUX                          (IOMUX_PINCM91)
#define BSP_CAN_1_TX_IOMUX_PF                       (IOMUX_PINCM91_PF_CANFD1_CANTX)

#define BSP_CAN_1_RX_PORT                           (GPIOC)
#define BSP_CAN_1_RX_PIN                            (DL_GPIO_PIN_27)
#define BSP_CAN_1_RX_IOMUX                          (IOMUX_PINCM92)
#define BSP_CAN_1_RX_IOMUX_PF                       (IOMUX_PINCM92_PF_CANFD1_CANRX)
//-------------------------------------------------------------------

#define BSP_I2C_DMA_TX_CHANNEL                     (kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_02)
#define BSP_I2C_DMA_TX_INTERRUPT_CHANNEL           (DL_DMA_INTERRUPT_CHANNEL3)
#define BSP_I2C_DMA_RX_CHANNEL                     (kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_03)
#define BSP_I2C_DMA_RX_INTERRUPT_CHANNEL           (DL_DMA_INTERRUPT_CHANNEL4)
#define BSP_I2C_0                                  (I2C0)
#define BSP_I2C_0_PORT                             (GPIOA)
#define BSP_I2C_0_SDA_PIN                          (DL_GPIO_PIN_8)
#define BSP_I2C_0_SDA_IOMUX_PF                     (IOMUX_PINCM19_PF_I2C0_SDA)
#define BSP_I2C_0_SDA_IOMUX                        (IOMUX_PINCM19)
#define BSP_I2C_0_SCL_PIN                          (DL_GPIO_PIN_9)
#define BSP_I2C_0_SCL_IOMUX_PF                     (IOMUX_PINCM20_PF_I2C0_SCL)
#define BSP_I2C_0_SCL_IOMUX                        (IOMUX_PINCM20)

#define BSP_I2C_1                                  (I2C1)
#define BSP_I2C_1_PORT                             (GPIOA)
#define BSP_I2C_1_SDA_PIN                          (DL_GPIO_PIN_16)
#define BSP_I2C_1_SDA_IOMUX_PF                     (IOMUX_PINCM38_PF_I2C1_SDA)
#define BSP_I2C_1_SDA_IOMUX                        (IOMUX_PINCM38)
#define BSP_I2C_1_SCL_PIN                          (DL_GPIO_PIN_15)
#define BSP_I2C_1_SCL_IOMUX_PF                     (IOMUX_PINCM37_PF_I2C1_SCL)
#define BSP_I2C_1_SCL_IOMUX                        (IOMUX_PINCM37)

#define BSP_I2C_2                                  (I2C2)
#define BSP_I2C_2_PORT                             (GPIOC)
#define BSP_I2C_2_SDA_PIN                          (DL_GPIO_PIN_3)
#define BSP_I2C_2_SDA_IOMUX_PF                     (IOMUX_PINCM77_PF_I2C2_SDA)
#define BSP_I2C_2_SDA_IOMUX                        (IOMUX_PINCM77)
#define BSP_I2C_2_SCL_PIN                          (DL_GPIO_PIN_2)
#define BSP_I2C_2_SCL_IOMUX_PF                     (IOMUX_PINCM76_PF_I2C2_SCL)
#define BSP_I2C_2_SCL_IOMUX                        (IOMUX_PINCM76)
// SPI ------------------------------------------------------------------------------
#define BSP_SPI_0_SCLK_PORT                         (GPIOB)
#define BSP_SPI_0_SCLK_PIN                          (DL_GPIO_PIN_18)
#define BSP_SPI_0_SCLK_IOMUX                        (IOMUX_PINCM44)
#define BSP_SPI_0_SCLK_IOMUX_PF                     (IOMUX_PINCM44_PF_SPI0_SCLK)

#define BSP_SPI_0_MOSI_PORT                         (GPIOB)
#define BSP_SPI_0_MOSI_PIN                          (DL_GPIO_PIN_17)
#define BSP_SPI_0_MOSI_IOMUX                        (IOMUX_PINCM43)
#define BSP_SPI_0_MOSI_IOMUX_PF                     (IOMUX_PINCM43_PF_SPI0_PICO)

#define BSP_SPI_0_MISO_PORT                         (GPIOB)
#define BSP_SPI_0_MISO_PIN                          (DL_GPIO_PIN_19)
#define BSP_SPI_0_MISO_IOMUX                        (IOMUX_PINCM45)
#define BSP_SPI_0_MISO_IOMUX_PF                     (IOMUX_PINCM45_PF_SPI0_POCI)

#define BSP_SPI_0_CS0_PORT                          (GPIOC)
#define BSP_SPI_0_CS0_PIN                           (DL_GPIO_PIN_7)
#define BSP_SPI_0_CS0_IOMUX                         (IOMUX_PINCM85)

#define BSP_SPI_0_CS1_PORT                          (GPIOC)
#define BSP_SPI_0_CS1_PIN                           (DL_GPIO_PIN_6)
#define BSP_SPI_0_CS1_IOMUX                         (IOMUX_PINCM84)

#define BSP_SPI_0_TX_DMA_CHANNEL                    (kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_00)
#define BSP_SPI_0_RX_DMA_CHANNEL                    (kBSP_DMA_CHANNEL_REPEATED_AND_SINGLE_TRANSFER_01)
// ------------------------------------------------------------------------------
#ifdef __cplusplus
}
#endif

#endif