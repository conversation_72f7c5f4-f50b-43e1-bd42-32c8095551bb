/*
******************************************************************************
* @file     ApiProtectOvp.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "Define.h"
#include "HalAfe.h"
#include "ApiProtect.h"
#include "ApiProtectOvp.h"
#include "LibSysPar.h"

void AppSerialUartSendMessage(char *str);

/* Private define ------------------------------------------------------------*/
#define	API_PROTECT_OVP_DEBUG_MSG(pc8Msg)		AppSerialUartSendMessage(pc8Msg)
#define	API_PROTECT_OVP_CHECK_NUM_PER_TIME	    (20)
#define	API_PROTECT_OVP_GET_CELL_NUMBER()	    LibSysParGetCellNumber()
#define	API_PROTECT_OVP_GET_MAX_CELL_VOLTAGE(u8Bmu, u8Posi, u16Cells)	HalAfeGetMaxCellVoltage(u8Bmu, u8Posi, u16Cells)
#define	API_PROTECT_OVP_GET_OVP_PAR(u8ProtectLevel, mProtectPar) 		LibSysParGetOvpPar(u8ProtectLevel, mProtectPar)
#define	API_PROTECT_OVP_GET_OVP_PF_PAR(mProtectPar) 		LibSysParGetOvpPfPar(mProtectPar)
#define	API_PROTECT_OVP_GET_LEVEL_MASK(u8ProtectLevel, mProtectFlagValue)	ApiProtectGetLevelMask(u8ProtectLevel, mProtectFlagValue)
#define API_PROTECT_OVP_IS_OVP_PF_SET()		    LibSysParIsOvpPfSet()

/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef struct
{
    uint8_t u8Flag[MAX_CELL_NUMBER];
    uint8_t u8SetCount[API_PROTECT_LEVEL][MAX_CELL_NUMBER];
    uint8_t u8ReleaseCount[API_PROTECT_LEVEL][MAX_CELL_NUMBER];
    uint8_t u8PfSetCount;
    uint8_t u8PfFlag;
    tfpApiProtectEvtHandler fpEvtHandler;
} tOvpProtect;

static tOvpProtect gmOvpProtect = {0};
static uint16_t gu16OvpCellIndex;
static bool gbOvpEnable = 0;
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
static void ApiProtectOvpIni(void)
{
    gu16OvpCellIndex = 0;
    if (API_PROTECT_OVP_IS_OVP_PF_SET())
    {
        gmOvpProtect.u8PfFlag = 1;
  	}
}

/* Public function prototypes -----------------------------------------------*/
void ApiProtectOvpPfClean(void)
{
    gmOvpProtect.u8PfFlag = 0;
}

uint8_t ApiProtectOvpPfGetFlag(void)
{
    return gmOvpProtect.u8PfFlag;
}

uint8_t ApiProtectOvpGetFlag(uint16_t u16CellIndex)
{
	if(u16CellIndex >= MAX_CELL_NUMBER)
	{
		return 0;
	}
    return gmOvpProtect.u8Flag[u16CellIndex];
}

uint8_t ApiProtectOvpHandler(uint8_t u8ProtectLevel)
{
    uint8_t u8Count = 0;
    uint16_t u16CellVoltage;
    tProtectFlagValue mProtectFlagValue;
    tScuProtectPar mProtectPar;
 
	if (gbOvpEnable == 0)
	{
		return 1;
	}
	
    API_PROTECT_OVP_GET_OVP_PAR(u8ProtectLevel, &mProtectPar);
    API_PROTECT_OVP_GET_LEVEL_MASK(u8ProtectLevel, &mProtectFlagValue);

    while (1)
    {
        u16CellVoltage = HalAfeGetCellVoltage(gu16OvpCellIndex);

        if (u16CellVoltage > mProtectPar.mSetValue.u32Value && 
            mProtectPar.mSTime.u32Value != 0)
        {
			#if 0 
			if(apiSystemFlagGetFlag1() & SYSTEM_FLAG1_SYSTEM_READY){
				 halAfeStartOpenWireTest();
			}
			
           
            if (!halAfeIsAdcValid(gu16OvpCellIndex))
            {
				gmOvpProtect.u8Flag[gu16OvpCellIndex] &= mProtectFlagValue.u8ClearMask;
				gmOvpProtect.u8ReleaseCount[u8ProtectLevel][gu16OvpCellIndex] = 0;
				gmOvpProtect.u8SetCount[u8ProtectLevel][gu16OvpCellIndex] = 0;
                goto cpmpareNext;
            }
            #endif
            if ((gmOvpProtect.u8Flag[gu16OvpCellIndex] & mProtectFlagValue.u8Mask) == 0)
            {
                gmOvpProtect.u8Flag[gu16OvpCellIndex] &= mProtectFlagValue.u8ClearMask;
                gmOvpProtect.u8Flag[gu16OvpCellIndex] |= mProtectFlagValue.u8Setting;
                gmOvpProtect.u8SetCount[u8ProtectLevel][gu16OvpCellIndex] = 1;
            }
            else if ((gmOvpProtect.u8Flag[gu16OvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
            {
                gmOvpProtect.u8SetCount[u8ProtectLevel][gu16OvpCellIndex]++;
                if (gmOvpProtect.u8SetCount[u8ProtectLevel][gu16OvpCellIndex] >= mProtectPar.mSTime.u32Value)
                {
                    gmOvpProtect.u8Flag[gu16OvpCellIndex] &= mProtectFlagValue.u8ClearMask;
                    gmOvpProtect.u8Flag[gu16OvpCellIndex] |= mProtectFlagValue.u8Setted;
                    gmOvpProtect.u8SetCount[u8ProtectLevel][gu16OvpCellIndex] = 0;

                    if (gmOvpProtect.fpEvtHandler)
                    {
                        gmOvpProtect.fpEvtHandler(0, kAPI_PROTECT_OVP_L1_SET + u8ProtectLevel, &gu16OvpCellIndex);
                    }
                }
            }
        }
        else if ((gmOvpProtect.u8Flag[gu16OvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
        {
            gmOvpProtect.u8Flag[gu16OvpCellIndex] &= mProtectFlagValue.u8ClearMask;
        }
        //-------------------------------------------
        // release
        if (u16CellVoltage < mProtectPar.mRelValue.u32Value && mProtectPar.mRTime.u32Value)
        {
            if ((gmOvpProtect.u8Flag[gu16OvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setted)
            {
                gmOvpProtect.u8Flag[gu16OvpCellIndex] &= mProtectFlagValue.u8ClearMask;
                gmOvpProtect.u8Flag[gu16OvpCellIndex] |= mProtectFlagValue.u8Releasing;
                gmOvpProtect.u8ReleaseCount[u8ProtectLevel][gu16OvpCellIndex] = 1;
            }
            else if ((gmOvpProtect.u8Flag[gu16OvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
            {
                gmOvpProtect.u8ReleaseCount[u8ProtectLevel][gu16OvpCellIndex]++;
                if (gmOvpProtect.u8ReleaseCount[u8ProtectLevel][gu16OvpCellIndex] >= mProtectPar.mRTime.u32Value)
                {
                    gmOvpProtect.u8Flag[gu16OvpCellIndex] &= mProtectFlagValue.u8ClearMask;
                    gmOvpProtect.u8ReleaseCount[u8ProtectLevel][gu16OvpCellIndex] = 0;
                    if (gmOvpProtect.fpEvtHandler)
                    {
                        gmOvpProtect.fpEvtHandler(0, kAPI_PROTECT_OVP_L1_RELEASE + u8ProtectLevel, &gu16OvpCellIndex);
                    }
                }
            }
        }
        else if ((gmOvpProtect.u8Flag[gu16OvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
        {
            gmOvpProtect.u8Flag[gu16OvpCellIndex] &= mProtectFlagValue.u8ClearMask;
            gmOvpProtect.u8Flag[gu16OvpCellIndex] |= mProtectFlagValue.u8Setted;
        }
cpmpareNext:
        gu16OvpCellIndex++;
        if (gu16OvpCellIndex >= API_PROTECT_OVP_GET_CELL_NUMBER())
        {
            gu16OvpCellIndex = 0;
            return 1;
        }
        u8Count++;
        if (u8Count >= API_PROTECT_OVP_CHECK_NUM_PER_TIME)
        {
            break;
      	}
    }
    return 0;
}

void ApiProtectOvpPfHandler(void)
{
    uint8_t u8Bmu, u8Posi;
    uint16_t u16Cells;
    tScuProtectPar mProtectPar;
    uint16_t u16Voltage;

	if (gbOvpEnable == 0)
	{
		return;
	}
       	
    if (gmOvpProtect.u8PfFlag != 0)
    {
        return;       
   	}
    API_PROTECT_OVP_GET_OVP_PF_PAR(&mProtectPar);
    u16Voltage = API_PROTECT_OVP_GET_MAX_CELL_VOLTAGE(&u8Bmu, &u8Posi, &u16Cells);
    if (u16Voltage > mProtectPar.mSetValue.u32Value && mProtectPar.mSTime.u32Value != 0)
    {
        gmOvpProtect.u8PfSetCount++;
        if (gmOvpProtect.u8PfSetCount >= mProtectPar.mSTime.u32Value)
        {
            gmOvpProtect.u8PfFlag = 1;
            if (gmOvpProtect.fpEvtHandler)
            {
                gmOvpProtect.fpEvtHandler(0, kAPI_PROTECT_OVP_PF, &u16Cells);
            }
        }
    }
    else
        gmOvpProtect.u8PfSetCount = 0;
}

void ApiProtectOvpOpen(tfpApiProtectEvtHandler fpEvtHandler)
{
    ApiProtectOvpIni();
	
	gbOvpEnable = 1;

    gmOvpProtect.fpEvtHandler = fpEvtHandler;
}

/************************ (C) COPYRIGHT *****END OF FILE****/
