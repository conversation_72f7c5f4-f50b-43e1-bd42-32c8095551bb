/*
******************************************************************************
* @file     ApiSpiFlash.c
* <AUTHOR> @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "ApiSpiFlash.h"
/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
/* Local variables ----------------------------------------------------------*/
static tApiSpiFlash *pmApiSpiFlashContext[API_SPI_FLASH_DEVICE_QTY];
static bool bApiSpiFlashInitLocalVariables = false;
static uint16_t u16ApiSpiFlashContextIndex;
static const uint8_t u8ReadStatusCmd[] =
{
    kAPI_SPI_FLASH_CMD_READ_STATUS_REGISTER_1,
    kAPI_SPI_FLASH_CMD_READ_STATUS_REGISTER_2,
    kAPI_SPI_FLASH_CMD_READ_STATUS_REGISTER_3,
};
static const uint8_t u8WriteStatusCmd[] =
{
    kAPI_SPI_FLASH_CMD_WRITE_STATUS_REGISTER_1,
    kAPI_SPI_FLASH_CMD_WRITE_STATUS_REGISTER_2,
    kAPI_SPI_FLASH_CMD_WRITE_STATUS_REGISTER_3,
};
static const uint8_t u8WriteEnableCmd[] =
{
    kAPI_SPI_FLASH_CMD_PAGE_PROGRAM,
};
/* Local function declare ---------------------------------------------------*/
static inline void ApiSpiFlashFillAddress24(uint8_t *pu8Dst, uint32_t u32Address);
static int8_t ApiSpiFlashPushFifo(tApiSpiFlash *pmApiSpiFlash, tUnionApiSpiFlashPacket *pmSpiFlashPacket, tLibSpiPacket *pmLibSpiPacket);
static inline bool ApiSpiFlashIsWriteEnableCommand(uint8_t u8Cmd);
static void fpApiSpiFlashSwTimerHandler(__far void *dest, uint16_t evt, __far void *data);
static void fpApiSpiFlashCallback(void *pContext, uint8_t u8CsIndex, eTypeHalSpiEvent eEvent);
/* Global variables ---------------------------------------------------------*/
/* Global function prototypes -----------------------------------------------*/
int8_t ApiSpiFlashInit(tApiSpiFlash *pmApiSpiFlash)
{
    if (bApiSpiFlashInitLocalVariables == false)
    {
        for (uint16_t u16Index = 0; u16Index < API_SPI_FLASH_DEVICE_QTY; u16Index++)
        {
            pmApiSpiFlashContext[u16Index] = NULL;
        }
        u16ApiSpiFlashContextIndex = 0;
        bApiSpiFlashInitLocalVariables = true;
    }
    if (pmApiSpiFlash -> pmHalSpi == NULL)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    HalSpiRegisterEventHandler(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash, pmApiSpiFlash -> u8CsIndex, fpApiSpiFlashCallback);
    pmApiSpiFlash -> mSpiFifo.pmFifoStartAddr = pmApiSpiFlash -> mSpiPackets;
    pmApiSpiFlash -> mSpiFifo.u16FifoSize = API_SPI_FLASH_FIFO_SIZE;
    pmApiSpiFlash -> mSpiFifo.u16FifoPushInPosi =
    pmApiSpiFlash -> mSpiFifo.u16FifoPopOutPosi = 0;
#if API_SPI_FLASH_FIFO_TARGET == API_SPI_FLASH_FIFO_STATIC_POINTER
    for (uint16_t u16Index = 0; u16Index < API_SPI_FLASH_FIFO_SIZE; u16Index++)
    {
        pmApiSpiFlash -> mSpiPackets[u16Index].pu8TxData = pmApiSpiFlash -> mSpiFlashTxPackets[u16Index].u8TxData;
    }
#elif API_SPI_FLASH_FIFO_TARGET == API_SPI_FLASH_FIFO_DYNAMIC_FIFO_BASE
    pmApiSpiFlash -> mSpiFlashTxFifo.pmFifoStartAddr = pmApiSpiFlash -> mSpiFlashTxPackets;
    pmApiSpiFlash -> mSpiFlashTxFifo.u16ElementSize = sizeof(pmApiSpiFlash -> mSpiFlashTxPackets[0]);
    pmApiSpiFlash -> mSpiFlashTxFifo.u16FifoSize = sizeof(pmApiSpiFlash -> mSpiFlashTxPackets) / sizeof(pmApiSpiFlash -> mSpiFlashTxPackets[0]);
    pmApiSpiFlash -> mSpiFlashTxFifo.u16FifoPushInPosi = pmApiSpiFlash -> mSpiFlashTxFifo.u16FifoPopOutPosi = 0;
#endif
    LibSoftwareTimerHandlerOpen(fpApiSpiFlashSwTimerHandler, pmApiSpiFlash);
    return RES_SUCCESS;
}

uint16_t ApiSpiFlashFifoCommandSize(tApiSpiFlash *pmApiSpiFlash)
{
    return LibSpiFifoGetCount(&(pmApiSpiFlash -> mSpiFifo));
}

bool ApiSpiFlashIsBusy(tApiSpiFlash *pmApiSpiFlash)
{
    if ((pmApiSpiFlash -> uStatusRegister.u32WinbondSeries & kAPI_SPI_STATUS_FLAG_BUSY) == kAPI_SPI_STATUS_FLAG_BUSY)
    {
        return true;
    }
    return false;
}

bool ApiSpiFlashIsWriteEnable(tApiSpiFlash *pmApiSpiFlash)
{
    if ((pmApiSpiFlash -> uStatusRegister.u32WinbondSeries & kAPI_SPI_STATUS_FLAG_WEL) == kAPI_SPI_STATUS_FLAG_WEL)
    {
        return true;
    }
    return false;
}

int8_t ApiSpiFlashReadManufacturerDeviceId(tApiSpiFlash *pmApiSpiFlash)
{
    int8_t i8Result;
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_MANUFACTURER_DEVICE_ID;
    pmApiSpiFlash -> u8TxData[1] =
    pmApiSpiFlash -> u8TxData[2] = kAPI_SPI_FLASH_CMD_DUMMY;
    pmApiSpiFlash -> u8TxData[3] = kAPI_SPI_FLASH_CMD_NOP;
    i8Result = HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 4, pmApiSpiFlash -> u8RxData, 2, pmApiSpiFlash -> u8CsIndex);
    if (i8Result != RES_SUCCESS)
    {
        return i8Result;
    }
    pmApiSpiFlash -> u8ManufacturerId = pmApiSpiFlash -> u8RxData[0];
    pmApiSpiFlash -> u8DeviceId = pmApiSpiFlash -> u8RxData[1];
    return i8Result;
}

int8_t ApiSpiFlashReadJedecId(tApiSpiFlash *pmApiSpiFlash)
{
    int8_t i8Result;
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_JEDEC_ID;
    i8Result = HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 1, pmApiSpiFlash -> u8RxData, 3, pmApiSpiFlash -> u8CsIndex);
    if (i8Result != RES_SUCCESS)
    {
        return i8Result;
    }
    pmApiSpiFlash -> u8ManufacturerId = pmApiSpiFlash -> u8RxData[0];
    pmApiSpiFlash -> uJedecId.MemoryTypeId = pmApiSpiFlash -> u8RxData[1];
    pmApiSpiFlash -> uJedecId.CapacityId = pmApiSpiFlash -> u8RxData[2];
    return i8Result;
}

int8_t ApiSpiFlashReadUniqueId(tApiSpiFlash *pmApiSpiFlash)
{
    int8_t i8Result;
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_READ_UNIQUE_ID;
    pmApiSpiFlash -> u8TxData[1] =
    pmApiSpiFlash -> u8TxData[2] = 
    pmApiSpiFlash -> u8TxData[3] = 
    pmApiSpiFlash -> u8TxData[4] = kAPI_SPI_FLASH_CMD_DUMMY;
    i8Result = HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 5, pmApiSpiFlash -> u8RxData, 8, pmApiSpiFlash -> u8CsIndex);
    if (i8Result != RES_SUCCESS)
    {
        return i8Result;
    }
    for (uint8_t u8Index = 0; u8Index < 8; u8Index++)
    {
        pmApiSpiFlash -> uUniqueId.u8UniqueId[u8Index] = pmApiSpiFlash -> u8RxData[(7 - u8Index)];
    }
    return i8Result;
}

int8_t ApiSpiFlashReadStatusRegister(tApiSpiFlash *pmApiSpiFlash, eTypeApiSpiFlashStatusRegister eApiSpiFlashStatusRegister)
{
    if (eApiSpiFlashStatusRegister == kAPI_SPI_FLASH_STATUS_REGISTER_NONE)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    int8_t i8Result = RES_SUCCESS;
    for (uint8_t u8BitMask, u8Index = 0; u8Index < API_SPI_FLASH_WINBOND_STATUS_REGISTER_SIZE; u8Index++)
    {
        u8BitMask = (1 << u8Index);
        if ((eApiSpiFlashStatusRegister & u8BitMask) == u8BitMask)
        {
            pmApiSpiFlash -> u8TxData[0] = u8ReadStatusCmd[u8Index];
            i8Result = HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 1, pmApiSpiFlash -> u8RxData, 1, pmApiSpiFlash -> u8CsIndex);
            if (i8Result != RES_SUCCESS)
            {
                return i8Result;
            }
            pmApiSpiFlash -> uStatusRegister.u8WinbondSeries[u8Index] = pmApiSpiFlash -> u8RxData[0];
        }
    }
    return i8Result;
}

int8_t ApiSpiFlashWriteVolatileStatusRegisterEnable(tApiSpiFlash *pmApiSpiFlash)
{
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_VOLATILE_SR_WRITE_ENABLE;
    return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 1, NULL, 0, pmApiSpiFlash -> u8CsIndex);
}

int8_t ApiSpiFlashWriteStatusRegister(tApiSpiFlash *pmApiSpiFlash, eTypeApiSpiFlashStatusRegister eApiSpiFlashStatusRegister, uint8_t u8Data)
{
    if (eApiSpiFlashStatusRegister == kAPI_SPI_FLASH_STATUS_REGISTER_NONE ||
        eApiSpiFlashStatusRegister == kAPI_SPI_FLASH_STATUS_REGISTER_ALL)
    {
        return RES_ERROR_INVALID_PARAM;
    }
    for (uint8_t u8BitMask, u8Index = 0; u8Index < API_SPI_FLASH_WINBOND_STATUS_REGISTER_SIZE; u8Index++)
    {
        u8BitMask = (1 << u8Index);
        if ((eApiSpiFlashStatusRegister & u8BitMask) == u8BitMask)
        {
            pmApiSpiFlash -> u8TxData[0] = u8WriteStatusCmd[u8Index];
            pmApiSpiFlash -> u8TxData[1] = u8Data;
            return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 2, NULL, 0, pmApiSpiFlash -> u8CsIndex);
        }
    }
    return RES_SUCCESS;
}

int8_t ApiSpiFlashWriteEnable(tApiSpiFlash *pmApiSpiFlash)
{
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_WRITE_ENABLE;
    return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 1, NULL, 0, pmApiSpiFlash -> u8CsIndex);
}

int8_t ApiSpiFlashWriteDisable(tApiSpiFlash *pmApiSpiFlash)
{
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_WRITE_DISABLE;
    return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 1, NULL, 0, pmApiSpiFlash -> u8CsIndex);
}

int8_t ApiSpiFlashPowerDown(tApiSpiFlash *pmApiSpiFlash)
{
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_POWER_DOWN;
    return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 1, NULL, 0, pmApiSpiFlash -> u8CsIndex);
}

int8_t ApiSpiFlashReleasePowerDown(tApiSpiFlash *pmApiSpiFlash)
{
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_RELEASE_POWER_DOWN_ID;
    return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 1, NULL, 0, pmApiSpiFlash -> u8CsIndex);
#if 0   // Read DeviceId
    int8_t i8Result;
    pmApiSpiFlash -> u8TxData[1] =
    pmApiSpiFlash -> u8TxData[2] =
    pmApiSpiFlash -> u8TxData[3] = kAPI_SPI_FLASH_CMD_DUMMY;
    i8Result = HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 4, pmApiSpiFlash -> u8RxData, 1, pmApiSpiFlash -> u8CsIndex);
    if (i8Result != RES_SUCCESS)
    {
        return i8Result;
    }
    pmApiSpiFlash -> u8DeviceId = pmApiSpiFlash -> u8RxData[0];
    return i8Result;
#endif
}

int8_t ApiSpiFlashReset(tApiSpiFlash *pmApiSpiFlash)
{
    //ApiSpiFlashReadStatusRegister(pmApiSpiFlash, kAPI_SPI_FLASH_STATUS_REGISTER_1);
    if (ApiSpiFlashIsBusy(pmApiSpiFlash) == true)
    {
        return RES_ERROR_BUSY;
    }
    pmApiSpiFlash -> u8TxData[0] = kAPI_SPI_FLASH_CMD_ENABLE_RESET;
    pmApiSpiFlash -> u8TxData[1] = kAPI_SPI_FLASH_CMD_RESET_DEVICE;
    return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, pmApiSpiFlash -> u8TxData, 2, NULL, 0, pmApiSpiFlash -> u8CsIndex);
}

int8_t ApiSpiFlashBlockingReadDataByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, uint8_t *pu8Data, uint16_t u16Size)
{
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_READ_DATA;
    ApiSpiFlashFillAddress24(mSpiFlashPacket.u8Address, u32Address);
    return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, mSpiFlashPacket.u8TxData, 4, pu8Data, u16Size, pmApiSpiFlash -> u8CsIndex);
}

int8_t ApiSpiFlashBlockingReadDataByPage(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size)
{
    uint32_t u32Address = (((uint32_t) u16Page) << API_SPI_FLASH_PAGE_SHIFT);
    return ApiSpiFlashBlockingReadDataByAddress(pmApiSpiFlash, u32Address, pu8Data, u16Size);
}

int8_t ApiSpiFlashBlockingFastReadDataByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, uint8_t *pu8Data, uint16_t u16Size)
{
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_READ_DATA;
    ApiSpiFlashFillAddress24(mSpiFlashPacket.u8Address, u32Address);
    mSpiFlashPacket.u8Data[0] = kAPI_SPI_FLASH_CMD_DUMMY;
    return HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, mSpiFlashPacket.u8TxData, 5, pu8Data, u16Size, pmApiSpiFlash -> u8CsIndex);
}

int8_t ApiSpiFlashBlockingFastReadDataByPage(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size)
{
    uint32_t u32Address = (((uint32_t) u16Page) << API_SPI_FLASH_PAGE_SHIFT);
    return ApiSpiFlashBlockingFastReadDataByAddress(pmApiSpiFlash, u32Address, pu8Data, u16Size);
}

int8_t ApiSpiFlashDmaReadDataByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback)
{
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    tLibSpiPacket mLibSpiPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_READ_DATA;
    ApiSpiFlashFillAddress24(mSpiFlashPacket.u8Address, u32Address);
    //StaticPointer
    // mLibSpiPacket.pu8TxData = mSpiFlashPacket.u8TxData;  
    /*  
    // DynamicFifoBase
    i8Result = LibFifoPush(&(pmApiSpiFlash -> mSpiFlashTxFifo), &mSpiFlashPacket);
    if (i8Result != RES_SUCCESS)
    {
        return i8Result;
    }
    mLibSpiPacket.pu8TxData = LibFifoGetBackPointer(&(pmApiSpiFlash -> mSpiFlashTxFifo));
    */
    mLibSpiPacket.u16TxSize = 4;
    mLibSpiPacket.pu8RxData = pu8Data;
    mLibSpiPacket.u16RxSize = u16Size;
    mLibSpiPacket.u8CsIndex = pmApiSpiFlash -> u8CsIndex;
    mLibSpiPacket.eTransferMode = kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV;
    mLibSpiPacket.fpCallback = fpCallback;
    //StaticPointer
    //i8Result = LibSpiFifoPushAndCloneTx(&(pmApiSpiFlash -> mSpiFifo), &mLibSpiPacket);
    //DynamicFifoBase
    //i8Result = LibSpiFifoPush(&(pmApiSpiFlash -> mSpiFifo), &mLibSpiPacket);
    return ApiSpiFlashPushFifo(pmApiSpiFlash, &mSpiFlashPacket, &mLibSpiPacket);
}

int8_t ApiSpiFlashDmaReadDataByPage(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback)
{
    uint32_t u32Address = (((uint32_t) u16Page) << API_SPI_FLASH_PAGE_SHIFT);
    return ApiSpiFlashDmaReadDataByAddress(pmApiSpiFlash, u32Address, pu8Data, u16Size, fpCallback);
}

int8_t ApiSpiFlashDmaFastReadDataByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback)
{
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    tLibSpiPacket mLibSpiPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_FAST_READ;
    ApiSpiFlashFillAddress24(mSpiFlashPacket.u8Address, u32Address);
    mSpiFlashPacket.u8Data[0] = kAPI_SPI_FLASH_CMD_DUMMY;
    mLibSpiPacket.u16TxSize = 5;
    mLibSpiPacket.pu8RxData = pu8Data;
    mLibSpiPacket.u16RxSize = u16Size;
    mLibSpiPacket.u8CsIndex = pmApiSpiFlash -> u8CsIndex;
    mLibSpiPacket.eTransferMode = kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV;
    mLibSpiPacket.fpCallback = fpCallback;
    return ApiSpiFlashPushFifo(pmApiSpiFlash, &mSpiFlashPacket, &mLibSpiPacket);
}

int8_t ApiSpiFlashDmaFastReadDataByPage(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback)
{
    uint32_t u32Address = (u16Page << API_SPI_FLASH_PAGE_SHIFT);
    return ApiSpiFlashDmaFastReadDataByAddress(pmApiSpiFlash, u32Address, pu8Data, u16Size, fpCallback);
}

int8_t ApiSpiFlashPageProgram(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Page, uint8_t *pu8Data, uint16_t u16Size, tfpHalSpiEvent fpCallback)
{
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    tLibSpiPacket mLibSpiPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_PAGE_PROGRAM;
    ApiSpiFlashFillAddress24(mSpiFlashPacket.u8Address, (u16Page << API_SPI_FLASH_PAGE_SHIFT));
    for (uint8_t u8Offset = 0; u8Offset < u16Size; u8Offset++)
    {
        mSpiFlashPacket.u8Data[u8Offset] = pu8Data[u8Offset];
    }
    mLibSpiPacket.u16TxSize = 4 + u16Size;
    mLibSpiPacket.pu8RxData = NULL;
    mLibSpiPacket.u16RxSize = 0;
    mLibSpiPacket.u8CsIndex = pmApiSpiFlash -> u8CsIndex;
    mLibSpiPacket.eTransferMode = kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV;
    mLibSpiPacket.fpCallback = fpCallback;
    return ApiSpiFlashPushFifo(pmApiSpiFlash, &mSpiFlashPacket, &mLibSpiPacket);
}

int8_t ApiSpiFlashSectorEraseByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, tfpHalSpiEvent fpCallback)
{
    if (u32Address != API_SPI_FLASH_ALIGN_TO_SECTOR(u32Address))
    {
        return RES_ERROR_INVALID_PARAM;
    }
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    tLibSpiPacket mLibSpiPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_ERASE_SECTOR;
    ApiSpiFlashFillAddress24(mSpiFlashPacket.u8Address, u32Address);
    mLibSpiPacket.u16TxSize = 4;
    mLibSpiPacket.pu8RxData = NULL;
    mLibSpiPacket.u16RxSize = 0;
    mLibSpiPacket.u8CsIndex = pmApiSpiFlash -> u8CsIndex;
    mLibSpiPacket.eTransferMode = kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV;
    mLibSpiPacket.fpCallback = fpCallback;
    return ApiSpiFlashPushFifo(pmApiSpiFlash, &mSpiFlashPacket, &mLibSpiPacket);
}

int8_t ApiSpiFlashSectorEraseBySector(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Sector, tfpHalSpiEvent fpCallback)
{
    uint32_t u32Address = (((uint32_t) u16Sector) << API_SPI_FLASH_SECTOR_SHIFT);
    return ApiSpiFlashSectorEraseByAddress(pmApiSpiFlash, u32Address, fpCallback);
}

int8_t ApiSpiFlashBlockErase32kByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, tfpHalSpiEvent fpCallback)
{
    if (u32Address != API_SPI_FLASH_ALIGN_TO_BLOCK32K(u32Address))
    {
        return RES_ERROR_INVALID_PARAM;
    }
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    tLibSpiPacket mLibSpiPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_ERASE_BLOCK_32K;
    ApiSpiFlashFillAddress24(mSpiFlashPacket.u8Address, u32Address);
    mLibSpiPacket.u16TxSize = 4;
    mLibSpiPacket.pu8RxData = NULL;
    mLibSpiPacket.u16RxSize = 0;
    mLibSpiPacket.u8CsIndex = pmApiSpiFlash -> u8CsIndex;
    mLibSpiPacket.eTransferMode = kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV;
    mLibSpiPacket.fpCallback = fpCallback;
    return ApiSpiFlashPushFifo(pmApiSpiFlash, &mSpiFlashPacket, &mLibSpiPacket);
}

int8_t ApiSpiFlashBlockErase32kByBlock(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Block32k, tfpHalSpiEvent fpCallback)
{
    uint32_t u32Address = (((uint32_t) u16Block32k) << API_SPI_FLASH_BLOCK32K_SHIFT);
    return ApiSpiFlashBlockErase32kByAddress(pmApiSpiFlash, u32Address, fpCallback);
}

int8_t ApiSpiFlashBlockErase64kByAddress(tApiSpiFlash *pmApiSpiFlash, uint32_t u32Address, tfpHalSpiEvent fpCallback)
{
    if (u32Address != API_SPI_FLASH_ALIGN_TO_BLOCK64K(u32Address))
    {
        return RES_ERROR_INVALID_PARAM;
    }
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    tLibSpiPacket mLibSpiPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_ERASE_BLOCK_64K;
    ApiSpiFlashFillAddress24(mSpiFlashPacket.u8Address, u32Address);
    mLibSpiPacket.u16TxSize = 4;
    mLibSpiPacket.pu8RxData = NULL;
    mLibSpiPacket.u16RxSize = 0;
    mLibSpiPacket.u8CsIndex = pmApiSpiFlash -> u8CsIndex;
    mLibSpiPacket.eTransferMode = kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV;
    mLibSpiPacket.fpCallback = fpCallback;
    return ApiSpiFlashPushFifo(pmApiSpiFlash, &mSpiFlashPacket, &mLibSpiPacket);
}

int8_t ApiSpiFlashBlockErase64kByBlock(tApiSpiFlash *pmApiSpiFlash, uint16_t u16Block64k, tfpHalSpiEvent fpCallback)
{
    uint32_t u32Address = (((uint32_t) u16Block64k) << API_SPI_FLASH_BLOCK64K_SHIFT);
    return ApiSpiFlashBlockErase64kByAddress(pmApiSpiFlash, u32Address, fpCallback);
}

int8_t ApiSpiFlashChipErase(tApiSpiFlash *pmApiSpiFlash, tfpHalSpiEvent fpCallback)
{
    tUnionApiSpiFlashPacket mSpiFlashPacket;
    tLibSpiPacket mLibSpiPacket;
    mSpiFlashPacket.u8Command = kAPI_SPI_FLASH_CMD_ERASE_CHIP;
    mLibSpiPacket.u16TxSize = 1;
    mLibSpiPacket.pu8RxData = NULL;
    mLibSpiPacket.u16RxSize = 0;
    mLibSpiPacket.u8CsIndex = pmApiSpiFlash -> u8CsIndex;
    mLibSpiPacket.eTransferMode = kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV;
    mLibSpiPacket.fpCallback = fpCallback;
    return ApiSpiFlashPushFifo(pmApiSpiFlash, &mSpiFlashPacket, &mLibSpiPacket);
}

static inline void ApiSpiFlashFillAddress24(uint8_t *pu8Dst, uint32_t u32Address)
{
    pu8Dst[0] = (u32Address >> 16) & 0xFF;
    pu8Dst[1] = (u32Address >> 8) & 0xFF;
    pu8Dst[2] = u32Address & 0xFF;
    return;
}

static int8_t ApiSpiFlashPushFifo(tApiSpiFlash *pmApiSpiFlash, tUnionApiSpiFlashPacket *pmSpiFlashPacket, tLibSpiPacket *pmLibSpiPacket)
{
    int8_t i8Result;
#if API_SPI_FLASH_FIFO_TARGET == API_SPI_FLASH_FIFO_STATIC_POINTER
    pmLibSpiPacket -> pu8TxData = pmSpiFlashPacket -> u8TxData;
    i8Result = LibSpiFifoPushAndCloneTx(&(pmApiSpiFlash -> mSpiFifo), pmLibSpiPacket);
#elif API_SPI_FLASH_FIFO_TARGET == API_SPI_FLASH_FIFO_DYNAMIC_FIFO_BASE
    i8Result = LibFifoPush(&(pmApiSpiFlash -> mSpiFlashTxFifo), pmSpiFlashPacket);
    if (i8Result != RES_SUCCESS)
    {
        return i8Result;
    }
    pmLibSpiPacket -> pu8TxData = LibFifoGetBackPointer(&(pmApiSpiFlash -> mSpiFlashTxFifo));
    i8Result = LibSpiFifoPush(&(pmApiSpiFlash -> mSpiFifo), pmLibSpiPacket);
#endif
    return i8Result;
}

static inline bool ApiSpiFlashIsWriteEnableCommand(uint8_t u8Cmd)
{
    for (uint8_t u8Index = 0; u8Index < LIB_FIFO_CALC_ARRAY_SIZE(u8WriteEnableCmd); u8Index++)
    {
        if (u8Cmd == u8WriteEnableCmd[u8Index])
        {
            return true;
        }
    }
    return false;
}

static void fpApiSpiFlashSwTimerHandler(__far void *dest, uint16_t evt, __far void *data)
{
    if (dest == NULL)
    {
        return;
    }
    if (evt & kLIB_SW_TIMER_EVT_1_MS)
    {
        tApiSpiFlash *pmApiSpiFlash = (tApiSpiFlash *) dest;
        if (HalSpiIsReady(pmApiSpiFlash -> pmHalSpi) == false)
        {
            return;
        }
        if (ApiSpiFlashIsBusy(pmApiSpiFlash) == false &&
            LibSpiFifoGetCount(&(pmApiSpiFlash -> mSpiFifo)) > 0)
        {
            #if API_SPI_FLASH_TEST_DISABLE_READ_REGISTER == (0)
            ApiSpiFlashReadStatusRegister(pmApiSpiFlash, kAPI_SPI_FLASH_STATUS_REGISTER_1);
            #endif
            tLibSpiPacket mLibSpiPacket;
            LibSpiFifoPeekFront(&(pmApiSpiFlash -> mSpiFifo), &mLibSpiPacket);
            #if API_SPI_FLASH_TEST_DISABLE_READ_REGISTER == (0)
            while (ApiSpiFlashIsWriteEnableCommand(mLibSpiPacket.pu8TxData[0]) == true &&
                ApiSpiFlashIsWriteEnable(pmApiSpiFlash) == false)
            {
                ApiSpiFlashWriteEnable(pmApiSpiFlash);
                ApiSpiFlashReadStatusRegister(pmApiSpiFlash, kAPI_SPI_FLASH_STATUS_REGISTER_1);
            }
            #endif
            switch (mLibSpiPacket.eTransferMode)
            {
            case kLIB_SPI_FIFO_TRANSFER_MODE_BLOCKING_FULL_DUPLEX:
                HalSpiSendRecvBlockingFullDuplex(pmApiSpiFlash -> pmHalSpi, mLibSpiPacket.pu8TxData, mLibSpiPacket.pu8RxData, mLibSpiPacket.u16TxSize, mLibSpiPacket.u8CsIndex);
                break;
            case kLIB_SPI_FIFO_TRANSFER_MODE_BLOCKING_HALF_DUPLEX:
                HalSpiSendRecvBlockingHalfDuplex(pmApiSpiFlash -> pmHalSpi, mLibSpiPacket.pu8TxData, mLibSpiPacket.u16TxSize, mLibSpiPacket.pu8RxData, mLibSpiPacket.u16RxSize, mLibSpiPacket.u8CsIndex);
                break;
            case kLIB_SPI_FIFO_TRANSFER_MODE_DMA_FULL_DUPLEX:
                HalSpiSendRecvDmaFullDuplex(pmApiSpiFlash -> pmHalSpi, mLibSpiPacket.pu8TxData, mLibSpiPacket.pu8RxData, mLibSpiPacket.u16TxSize, mLibSpiPacket.u8CsIndex);
                break;
            /*
            case kLIB_SPI_FIFO_TRANSFER_MODE_DMA_HALF_DUPLEX:
                HalSpiSendRecvDmaHalfDuplex(pmApiSpiFlash -> pmHalSpi, mLibSpiPacket.pu8TxData, mLibSpiPacket.u16TxSize, mLibSpiPacket.pu8RxData, mLibSpiPacket.u16RxSize, mLibSpiPacket.u8CsIndex);
                break;
            */
            case kLIB_SPI_FIFO_TRANSFER_MODE_SEND_BLOCKING_DMA_RECV:
                HalSpiSendBlockingRecvDma(pmApiSpiFlash -> pmHalSpi, mLibSpiPacket.pu8TxData, mLibSpiPacket.u16TxSize, mLibSpiPacket.pu8RxData, mLibSpiPacket.u16RxSize, mLibSpiPacket.u8CsIndex);            
                break;
            default:
                #if API_SPI_FLASH_FIFO_TARGET == API_SPI_FLASH_FIFO_DYNAMIC_FIFO_BASE
                LibFifoPop(&(pmApiSpiFlash -> mSpiFlashTxFifo), NULL);
                #endif
                LibSpiFifoPop(&(pmApiSpiFlash -> mSpiFifo), &mLibSpiPacket);
                break;
            }
        }
    }
    return;
}

static void fpApiSpiFlashCallback(void *pContext, uint8_t u8CsIndex, eTypeHalSpiEvent eEvent)
{
    if (pContext == NULL ||
        ((tApiSpiFlash *) pContext) -> u8CsIndex != u8CsIndex ||
        eEvent != kHAL_SPI_EVENT_DONE)
    {
        return;
    }
    tApiSpiFlash *pmApiSpiFlash = (tApiSpiFlash *) pContext;
    tLibSpiPacket mLibSpiPacket;
#if API_SPI_FLASH_TEST_DISABLE_READ_REGISTER == (0)
    while (ApiSpiFlashIsWriteEnable(pmApiSpiFlash) == true)
    {
        ApiSpiFlashWriteDisable(pmApiSpiFlash);
        ApiSpiFlashReadStatusRegister(pmApiSpiFlash, kAPI_SPI_FLASH_STATUS_REGISTER_1);
    }
#endif
#if API_SPI_FLASH_FIFO_TARGET == API_SPI_FLASH_FIFO_DYNAMIC_FIFO_BASE
    LibFifoPop(&(pmApiSpiFlash -> mSpiFlashTxFifo), NULL);
#endif
    int8_t i8Result = LibSpiFifoPop(&(pmApiSpiFlash -> mSpiFifo), &mLibSpiPacket);
    if (i8Result != RES_SUCCESS)
    {
        return;
    }
    if (mLibSpiPacket.fpCallback != NULL)
    {
        mLibSpiPacket.fpCallback(eEvent);
    }
    return;
}
/* Local function prototypes ------------------------------------------------*/
/* Global test function prototypes ------------------------------------------*/
#ifdef API_SPI_FLASH_ENABLE_EXAMPLE
static tHalSpi gmHalSpi =
{
    .eChannel = kHAL_SPI_CHANNEL_0,
    .mConfig.eMode = kHAL_SPI_MODE_MASTER,
    .mConfig.eFrameFormat = kHAL_SPI_FRAME_FORMAT_3WIRE_SPO0_SPH0,
    .mConfig.eParity = kHAL_SPI_PARITY_NONE,
    .mConfig.eFrameSize = kHAL_SPI_FRAME_SIZE_BITS_8,
    .mConfig.eBitOrder = kHAL_SPI_BIT_ORDER_MSB_FIRST,
    .mConfig.eChipSelect = kHAL_SPI_CHIP_SELECT_NONE,
    .u32BitRate = 1000000,
};
static tApiSpiFlash gmApiSpiFlash, gmApiSpiFlash2;
static uint8_t gu8RxData[256];
void ApiSpiFlashExampleInit(void)
{
    HalSpiOpen(&gmHalSpi);
    gmApiSpiFlash.pmHalSpi = &gmHalSpi;
    gmApiSpiFlash.u8CsIndex = 0;
    ApiSpiFlashInit(&gmApiSpiFlash);
    gmApiSpiFlash2.pmHalSpi = &gmHalSpi;
    gmApiSpiFlash2.u8CsIndex = 1;
    ApiSpiFlashInit(&gmApiSpiFlash2);
    return;
}

void ApiSpiFlashExampleTest(void)
{
    tApiSpiFlash *pmApiSpiFlash = &gmApiSpiFlash;
    tApiSpiFlash *pmApiSpiFlash2 = &gmApiSpiFlash2;
    uint8_t u8TxData[4] = {0x1, 0x2, 0x3, 0x4};
    uint8_t u8TxData2[4] = {0x5, 0x6, 0x7, 0x8};
    ApiSpiFlashWriteEnable(pmApiSpiFlash);
    ApiSpiFlashWriteEnable(pmApiSpiFlash2);
    ApiSpiFlashSectorEraseByAddress(pmApiSpiFlash, 0, NULL);
    while (ApiSpiFlashFifoCommandSize(pmApiSpiFlash) != 0)
    {
        LibSWTimerHandler();
    }
    ApiSpiFlashSectorEraseByAddress(pmApiSpiFlash2, 0, NULL);
    while (ApiSpiFlashFifoCommandSize(pmApiSpiFlash2) != 0)
    {
        LibSWTimerHandler();
    }
    delay_cycles(32000000);  // Wait 400ms
    ApiSpiFlashBlockingReadDataByAddress(pmApiSpiFlash, 0x0, gu8RxData, 4);
    ApiSpiFlashBlockingReadDataByAddress(pmApiSpiFlash2, 0x0, gu8RxData, 4);
    ApiSpiFlashWriteEnable(pmApiSpiFlash);
    ApiSpiFlashWriteEnable(pmApiSpiFlash2);
    ApiSpiFlashPageProgram(pmApiSpiFlash, 0x0, u8TxData, 4, NULL);
    while (ApiSpiFlashFifoCommandSize(pmApiSpiFlash) != 0)
    {
        LibSWTimerHandler();
    }
    ApiSpiFlashPageProgram(pmApiSpiFlash2, 0x0, u8TxData2, 4, NULL);
    while (ApiSpiFlashFifoCommandSize(pmApiSpiFlash2) != 0)
    {
        LibSWTimerHandler();
    }
    delay_cycles(240000);  // Wait 3ms
    ApiSpiFlashBlockingReadDataByAddress(pmApiSpiFlash, 0x0, gu8RxData, 4);
    ApiSpiFlashBlockingReadDataByAddress(pmApiSpiFlash2, 0x0, gu8RxData, 4);
    ApiSpiFlashWriteDisable(pmApiSpiFlash);
    ApiSpiFlashWriteDisable(pmApiSpiFlash2);
    return;
}
#endif
