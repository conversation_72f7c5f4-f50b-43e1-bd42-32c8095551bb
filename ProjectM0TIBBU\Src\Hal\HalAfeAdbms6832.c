/*
******************************************************************************
* @file     HalAfeAdbms6832.c
* <AUTHOR>
* @brief    XXXXXXXXXX

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "Main.h"
#include <stdio.h>
#include "HalAfeAdbms6832.h"
#include "HalSpiAfeAdbms6832.h"
#include "LibSoftwareTimerHandler.h"
#include "LibSysPar.h"
#include "HalGpio.h"
#include "Bsp.h"
#include "HalUart.h" 
#include "AppSerialUartHvBBU.h"
#include "HalAfeInternal.h"

/* Local typedef ------------------------------------------------------------*/
typedef void (*tfpAfeFunctionTable)(void);
typedef void (*tfpAfeSWTimerHandler)(uint16_t u16Evt);

/* Local define -------------------------------------------------------------*/
#define AppProjectIsInSimuMode()      (false)
#define AppProjectIsInEngMode()       (false)
#define ApiSysParGetCellNumInPack()   (52)
#define TOTAL_AFE_NUMBER              (HAL_AFE_GET_AFE_NUM())
#define AFE_COMM_L1_TIME              ((uint16_t)LibSysParGetAfeCommL1Time() * 100)
#define AFE_COMM_L2_TIME              ((uint16_t)LibSysParGetAfeCommL2Time() * 100)

#define AFE_COMM_FLAG_L1              (0x01)
#define AFE_COMM_FLAG_L2              (0x02)
#define AFE_RETRY_CNT_MAX             (4)
#define SINGLE_AFE_COM_TIMEOUT        (10) // unit : ms

#define INIT_RETRY_DELAY              (500) // unit ms

#define SINGLE_PARSER_REG_CNT         (3)

#define ROUTINE_INTERVAL              (50) // unit 10 ms

#define CELL_V_ADC_DELAY              (7)				 // ms
#define GPIO_ADC_DELAY                (6)				 // ms
#define ADC_SYNC_TIME                 (3)				 // ms

#define AUX_REG_A_GPIO_OFFSET         (0)
#define AUX_REG_B1_GPIO_OFFSET        (10)
#define AUX_REG_B2_GPIO_OFFSET        (5)
#define AUX_REG_C_GPIO_OFFSET         (6)
#define AUX_REG_D_GPIO_OFFSET         (9)
#define AUX_REG_E_GPIO_OFFSET         (3)

#define	CV_BUSBAR_BASE_INDEX		  (0x1000)
#define	NTC_BUSBAR_BASE_INDEX		  (0x1000)
#define	NTC_AMBIENT_BASE_INDEX	      (0x2000)
#define	NTC_OTHER_BASE_INDEX		  (0x4000)

#define GENERAL_ADC_RESOLUTION 0.1f
#define PACKV_ADC_RESOLUTION 0.03f
#define NTC_ADC_RESOLUTION 1.667f

/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
static void halAfeAdbms6832InitTask(uint16_t u16Evt);
static void halAfeAdbms6832RingTask(uint16_t u16Evt);
static void halAfeAdbms6832RoutineTask(uint16_t u16Evt);

/* Global variables ---------------------------------------------------------*/
static tLibFuncPtrRegEvtHandler gfpAfeEventHandler = NULL;
static tAfeAdbms6832IdxAndPosiConfig gmAfeIdxAndPosiConfig = {0};

static uint8_t gu8AfeAmount[2] = {0};
static uint8_t gu8AfeAmountValid[2] = {0};
static eTypeHalAfeAdbms6832InitStatus geAfeInitStatus = kHAL_AFE_ADBMS6832_NORTH_INIT_START;
static eTypeHalAfeAdbms6832CommDirection geAfeCommDir = kHAL_AFE_ADBMS6832_COMM_DIR_NORTH;
static uint16_t gu16AfeCommTimeoutCnt = 0;
static eTypeHalAfeAdbms6832State geAfeState = kHAL_AFE_ADBMS6832_INIT_STATE;
static uint8_t gu8AfeCommProtectFlag = 0;
static uint32_t gu32AfeCommRequestCnt = 0;
static uint32_t gu32AfeCommFailCnt = 0;
static uint16_t gu16AfeDelayCnt = 0;
static uint16_t gu16AfeInitRetryDelay = 0;
static uint8_t gu8AfeReinitCnt = 0;
static uint16_t gu16AfeCmdTimeoutCnt = 0;
static uint8_t gu8MainFunctionTableIdx = 0;
static bool gbAfeCbEnd = false;

static tfpAfeFunctionTable gfpAfeSubFunctionTable = NULL;
static tfpAfeFunctionTable gfpAfeMainFunctionTable = NULL;
static tfpAfeFunctionTable gfpAfeFunctionProcessor = NULL;
static tfpAfeSWTimerHandler gfpAfeSWTimerHandlerProcessor = NULL;

/* Local function prototypes ------------------------------------------------*/
static void afeInitReset(void)
{
    gfpAfeSWTimerHandlerProcessor = halAfeAdbms6832InitTask;
    	
    geAfeCommDir = kHAL_AFE_ADBMS6832_COMM_DIR_NORTH;
    geAfeInitStatus = kHAL_AFE_ADBMS6832_NORTH_INIT_START;
    
    gu16AfeInitRetryDelay = 0;
	
    geAfeState = kHAL_AFE_ADBMS6832_INIT_STATE;
    
    gu8AfeReinitCnt = 0;
}

/// [CH] : 將AD值換成電壓mV
static uint16_t convertNtcAdcToMv(uint16_t adc)
{
    #if 0			//0.65 us
    float d;
    // char str[100];
    //	sprintf(str, "ntcv=%d", adc);
    // appSerialCanDavinciSendTextMessage(str);
    d = adc;
    d *= (GENERAL_ADC_RESOLUTION * NTC_ADC_RESOLUTION);
    return (uint16_t)d;
    #endif
    #if 0			//0.3125 us
    adc /= 6;
    return adc;
    #endif
    #if 1			//1.225 us
    #define	_52S_PACK		52
    #define	_CAL_GAIN		100
    #define	_52S_PULL_UP_R	49900
    #define	_18S_PULL_UP_R	10000
    #define	DST_VREF		5000
    #define	SRC_VREF		30000

    uint32_t d1, d2;

    /// [CH] : 如果是52串
    if (ApiSysParGetCellNumInPack() == _52S_PACK) 
    {
        d1 = (DST_VREF / _CAL_GAIN * _52S_PULL_UP_R / _CAL_GAIN) * adc;
        d2 = (_18S_PULL_UP_R / _CAL_GAIN * SRC_VREF / _CAL_GAIN) +
            (((_52S_PULL_UP_R - _18S_PULL_UP_R) / _CAL_GAIN ) * adc) / _CAL_GAIN ;
        d1 /= d2;

        return (uint16_t)d1;
    } 
    /// [CH] : 如果不是
    else 
    {
        adc /= 6;
        return adc;
    }

    #endif
}

static void convertuint32BalanceEnToBoolFlag(bool *pbDccCell, uint8_t u8AfeIdx) 
{
    uint8_t u8ChIdx = 0;
    uint32_t u32BalanceEnCh = 0;
    uint32_t u32ChMsk = 0;
    
    
    /// [CH] : 回傳此AFE(不管哪個方向, 都是由0~7)的哪個Channel需要平衡	
    /// [CH] : 0x0001 代表 CH1需平衡
    /// [CH] : 0x0003 代表 CH1跟CH2需平衡 
    /*
    u32BalanceEnCh = HalAfeGetPhysicalBalancePosition(u8AfeIdx);
    
    /// [CH] : 如果沒有Channel需要平衡 或 平衡總開關不允許平衡 
    if ((u32BalanceEnCh == 0) || (HalAfeGetBalanceOnFlag() == 0)) 
    {
        return;
    }
    */

    /// [CH] : 這邊記得要改回來
    if (u8AfeIdx == 0x00)
    {
        u32BalanceEnCh = 0x0009;
    }
    else if (u8AfeIdx == 0x01)
    {
        u32BalanceEnCh = 0x0000;
    }

    for (u8ChIdx = 0; u8ChIdx < HAL_ADBMS6832_MAX_AFE_CELL_CHANNEL_NUM; u8ChIdx++)
    {
        u32ChMsk = 1 << u8ChIdx;

        if (u32BalanceEnCh & u32ChMsk) 
        {
            *(pbDccCell + u8ChIdx) = 1; 
        } 
        else 
        {
            *(pbDccCell + u8ChIdx) = 0; 
        }
    }
}

static void afeCommProtect() 
{
    if (AppProjectIsInSimuMode() || AppProjectIsInEngMode()) 
    {
        gu16AfeCommTimeoutCnt = 0;
        return;
    }

    if (gu16AfeCommTimeoutCnt < UINT16_MAX) 
    {
        gu16AfeCommTimeoutCnt ++;
    }
    
    if (gu16AfeCommTimeoutCnt >= AFE_COMM_L1_TIME) 
    {
        if ((gu8AfeCommProtectFlag & AFE_COMM_FLAG_L1) == 0)	
        {
            gu8AfeCommProtectFlag |= AFE_COMM_FLAG_L1;

            if (gfpAfeEventHandler != NULL) 
            {
                gfpAfeEventHandler(0, kAFE_EVT_COMM_L1_SET, 0);
            }
        }
    } 
    else 
    {
        if ((gu8AfeCommProtectFlag & AFE_COMM_FLAG_L1) != 0) 
        {
            gu8AfeCommProtectFlag &= ~AFE_COMM_FLAG_L1;

            if (gfpAfeEventHandler != NULL) 
            {
                gfpAfeEventHandler(0, kAFE_EVT_COMM_L1_RELEASE, 0);
            }
        }
    }

    if (gu16AfeCommTimeoutCnt >= AFE_COMM_L2_TIME) 
    {
        if ((gu8AfeCommProtectFlag & AFE_COMM_FLAG_L2) == 0) 
        {
            gu8AfeCommProtectFlag |= AFE_COMM_FLAG_L2;

            if (gfpAfeEventHandler != NULL) 
            {
                gfpAfeEventHandler(0, kAFE_EVT_COMM_L2_SET, 0);
            }
        }
    } 
    else 
    {
        if ((gu8AfeCommProtectFlag & AFE_COMM_FLAG_L2) != 0) 
        {
            gu8AfeCommProtectFlag &= ~AFE_COMM_FLAG_L2;

            if (gfpAfeEventHandler != NULL) 
            {
                gfpAfeEventHandler(0, kAFE_EVT_COMM_L2_RELEASE, 0);
            }
        }
    }
}

static uint8_t whichCellChFun(uint8_t u8AfeIdx, uint8_t u8ChPosi) 
{
    uint32_t u32Mask;
    uint32_t u32CellFlag;
    uint32_t u32CellBusBarFlag;


    u32CellFlag = HAL_AFE_GET_CELL_FLAG(u8AfeIdx);
    	
    u32CellBusBarFlag = LibSysParGetCellBusBarFlag(u8AfeIdx);
    
    u32Mask = 1 << u8ChPosi;

    if (u32CellFlag & u32Mask)
    {
        return kHAL_AFE_ADBMS6832_CELLV_USE;
    }
    else if (u32CellBusBarFlag & u32Mask)
    {
        return kHAL_AFE_ADBMS6832_CELLV_BUSBAR;
    }
    else
    {
        return kHAL_AFE_ADBMS6832_CELLV_NONE_USE;
    }
}

static uint8_t whichGpioFun(uint8_t u8AfeIdx, uint8_t u8ChPosi) 
{
    uint32_t u32Mask;
    uint32_t u32NtcFlag;
    uint32_t u32NtcBusBarFlag;
    uint32_t u32NtcAmbientFlag;
    uint32_t u32NtcOtherFlag;
    
    
    u32NtcFlag = HAL_AFE_GET_NTC_FLAG(u8AfeIdx);
    u32NtcAmbientFlag = LibSysParGetNtcAmbientFlag(u8AfeIdx);
    u32NtcBusBarFlag = LibSysParGetNtcBusBarFlag(u8AfeIdx);
    u32NtcOtherFlag = LibSysParGetNtcOtherFlag(u8AfeIdx);
  
    u32Mask = 1 << u8ChPosi;

    if (u32NtcFlag & u32Mask)
    {
        return kHAL_AFE_ADBMS6832_GPIO_NTC;
    }
    else if (u32NtcAmbientFlag & u32Mask)
    {
        return kHAL_AFE_ADBMS6832_GPIO_AMBIENT;
    }
    else if (u32NtcBusBarFlag & u32Mask)
    {
        return kHAL_AFE_ADBMS6832_GPIO_BUSBAR;
    }
    else if (u32NtcOtherFlag & u32Mask)
    {
        return kHAL_AFE_ADBMS6832_GPIO_OTHER;
    }
    else
    {
        return kHAL_AFE_ADBMS6832_GPIO_NONE_USE;
    }
}

static void getAfeNtcCnt(void) 
{
    uint8_t 	u8AfeIdx;
    uint8_t     u8ChnlIdx;
    uint8_t		u8NtcFun;
    uint16_t	u16NtcLogicIdx = 0;
    uint16_t	u16NtcBusBarLogicIdx = 0;
    uint16_t	u16NtcAmbientLogicIdx = 0;
    uint16_t	u16NtcOtherLogicIdx = 0;

		
    for (u8AfeIdx = 0; u8AfeIdx < TOTAL_AFE_NUMBER; u8AfeIdx++) 
    {
        for (u8ChnlIdx = 0; u8ChnlIdx < HAL_ADBMS6832_MAX_AFE_GPIO_CHAMMEL_NUM; u8ChnlIdx++) 
        {	
            u8NtcFun = whichGpioFun(u8AfeIdx, u8ChnlIdx);
        	
            if (u8NtcFun == kHAL_AFE_ADBMS6832_GPIO_NTC) 
            {
                gmAfeIdxAndPosiConfig.mNtcPhysicalPosi[u16NtcLogicIdx].u8AfeId = u8AfeIdx;
                gmAfeIdxAndPosiConfig.mNtcPhysicalPosi[u16NtcLogicIdx].u8ChId = u8ChnlIdx;
                gmAfeIdxAndPosiConfig.u16NtcLogicIdx[u8AfeIdx][u8ChnlIdx] = u16NtcLogicIdx++;
            }
            else if (u8NtcFun == kHAL_AFE_ADBMS6832_GPIO_AMBIENT) 
            {
                gmAfeIdxAndPosiConfig.u16NtcLogicIdx[u8AfeIdx][u8ChnlIdx] = NTC_AMBIENT_BASE_INDEX + u16NtcAmbientLogicIdx;
                u16NtcAmbientLogicIdx++;
            } 
            else if (u8NtcFun == kHAL_AFE_ADBMS6832_GPIO_BUSBAR) 
            {
                gmAfeIdxAndPosiConfig.u16NtcLogicIdx[u8AfeIdx][u8ChnlIdx] = NTC_BUSBAR_BASE_INDEX + u16NtcBusBarLogicIdx;
                u16NtcBusBarLogicIdx++;
            } 
            else if (u8NtcFun == kHAL_AFE_ADBMS6832_GPIO_OTHER)
            {
                gmAfeIdxAndPosiConfig.u16NtcLogicIdx[u8AfeIdx][u8ChnlIdx] = NTC_OTHER_BASE_INDEX + u16NtcOtherLogicIdx;
                u16NtcOtherLogicIdx++;
            }
        }
    }
}

static void getAfeCellCnt(void) 
{
    uint8_t u8AfeIdx;
    uint8_t u8ChnlIdx;
    uint8_t u8CellFun;
    uint16_t u16CvLogicIdx = 0;
    uint16_t u16BbLogicIdx = 0;

    	
    for (u8AfeIdx = 0; u8AfeIdx < TOTAL_AFE_NUMBER; u8AfeIdx++)
    {
        for (u8ChnlIdx = 0; u8ChnlIdx < HAL_ADBMS6832_MAX_AFE_CELL_CHANNEL_NUM; u8ChnlIdx++) 
        {
            u8CellFun = whichCellChFun(u8AfeIdx, u8ChnlIdx);
        
            if (u8CellFun == kHAL_AFE_ADBMS6832_CELLV_USE) 
            {
                gmAfeIdxAndPosiConfig.mCellPhysicalPosi[u16CvLogicIdx].u8AfeId = u8AfeIdx;
                gmAfeIdxAndPosiConfig.mCellPhysicalPosi[u16CvLogicIdx].u8ChId = u8ChnlIdx;
                gmAfeIdxAndPosiConfig.u16CellLogicIdx[u8AfeIdx][u8ChnlIdx] = u16CvLogicIdx++;
            }
            else if (u8CellFun == kHAL_AFE_ADBMS6832_CELLV_BUSBAR) 
            {
                gmAfeIdxAndPosiConfig.u16CellLogicIdx[u8AfeIdx][u8ChnlIdx] = CV_BUSBAR_BASE_INDEX + u16BbLogicIdx;
                u16BbLogicIdx++;
            }
        }
    }
}

static uint8_t getGpioFunction(uint8_t u8AfeIndex, eTypeHalAfeAdbms6832GpioGroup eAfeGpioGroup, uint8_t u8RegIndex) 
{
    switch (eAfeGpioGroup) 
    {
        case kHAL_AFE_ADBMS6832_GPIO_GROUP_A:
            return whichGpioFun(u8AfeIndex, u8RegIndex + AUX_REG_A_GPIO_OFFSET);
            break;
        case kHAL_AFE_ADBMS6832_GPIO_GROUP_B:
            if (u8RegIndex < 2)
            {
                return whichGpioFun(u8AfeIndex, u8RegIndex + AUX_REG_B1_GPIO_OFFSET);
            }
            else
            {
                return whichGpioFun(u8AfeIndex, u8RegIndex + AUX_REG_B2_GPIO_OFFSET);
            }
            break;
        case kHAL_AFE_ADBMS6832_GPIO_GROUP_C:
            return whichGpioFun(u8AfeIndex, u8RegIndex + AUX_REG_C_GPIO_OFFSET);
            break;
        case kHAL_AFE_ADBMS6832_GPIO_GROUP_D:
            if (u8RegIndex == 0)
            {
                return whichGpioFun(u8AfeIndex, u8RegIndex + AUX_REG_D_GPIO_OFFSET);
            }
            break;
        case kHAL_AFE_ADBMS6832_GPIO_GROUP_E: 
            if (u8RegIndex < 2)
            {
                return whichGpioFun(u8AfeIndex, u8RegIndex + AUX_REG_E_GPIO_OFFSET);
            } 
            break;
        case kHAL_AFE_ADBMS6832_GPIO_UNSUPPORT:
            return kHAL_AFE_ADBMS6832_GPIO_UNSUPPORT;
            break;
    }
}

static uint16_t getNtcLogicIdx(uint8_t u8AfeIdx, eTypeHalAfeAdbms6832GpioGroup eGpioGroup, uint8_t u8RegIndex) 
{
    uint8_t		u8ChLogicIdx;
    const uint8_t u8ChIdxTable[5][3] = 
    {
      {0, 1, 2},
      {10, 11, 5},
      {6, 7, 8},
      {9, 0, 0},
      {3, 4, 0}
    };
 
    u8ChLogicIdx = u8ChIdxTable[eGpioGroup][u8RegIndex];
    
    return gmAfeIdxAndPosiConfig.u16NtcLogicIdx[u8AfeIdx][u8ChLogicIdx];
}

static void halDecodeCellV(eTypeHalAfeAdbms6832CellVGroup eCellgroup) 
{
    uint8_t u8Idx;
    uint8_t u8AfeIdx;
    uint8_t u8RegIdx;
    uint16_t* pu16Adc;
    uint16_t u16Voltage = 0;
    uint16_t u16LogicIdx = 0;
    uint8_t u8ChIndex;
    uint8_t u8CellFun;
    uint32_t u32Voltage;


    gu8AfeAmountValid[geAfeCommDir] = HalAfeAdbms6832GetAfeOnlineNum();

    if (gu8AfeAmountValid[geAfeCommDir] != TOTAL_AFE_NUMBER)
    {
        gu32AfeCommFailCnt++;
        return;
    }

    for (u8Idx = 0; u8Idx < gu8AfeAmount[geAfeCommDir]; u8Idx++) 
    {
        if (geAfeCommDir == kHAL_AFE_ADBMS6832_COMM_DIR_NORTH)		
        {
            u8AfeIdx = u8Idx;
        } 
        else 
        {
            u8AfeIdx = TOTAL_AFE_NUMBER - u8Idx - 1;
        }

        u8ChIndex = eCellgroup * SINGLE_PARSER_REG_CNT;


        for (u8RegIdx = 0; u8RegIdx < SINGLE_PARSER_REG_CNT; u8RegIdx++, u8ChIndex++)
        {
            pu16Adc = HalAfeAdbms6832GetDecodeAfeRxDataBuf();

            u16Voltage = pu16Adc[u8AfeIdx * SINGLE_PARSER_REG_CNT + u8RegIdx];
            u32Voltage = (uint32_t)(u16Voltage * 150 + 1500000);
            u16Voltage = (uint16_t)(u32Voltage / 100);

            u8CellFun = whichCellChFun(u8Idx, u8ChIndex);

            if (u8CellFun == kHAL_AFE_ADBMS6832_CELLV_USE) 
            {
                if (AppProjectIsInSimuMode() == false)		
                {
                    u16LogicIdx = gmAfeIdxAndPosiConfig.u16CellLogicIdx[u8Idx][u8ChIndex];
                    HalAfeSetCellVoltage(u16LogicIdx, u16Voltage);
                }
            } 
            else if (u8CellFun == kHAL_AFE_ADBMS6832_CELLV_BUSBAR) 
            {
                u16LogicIdx = gmAfeIdxAndPosiConfig.u16CellLogicIdx[u8Idx][u8ChIndex] & 0x3ff;
                HalAfeSetCellBusbarVoltage(u16LogicIdx, u16Voltage);
            }
        }
    }
}

static void halDecodeNtc(eTypeHalAfeAdbms6832GpioGroup eGpioGroup) 
{
    uint8_t   u8Idx;
    uint8_t   u8AfeIdx;
    uint8_t   u8RegIdx;
    uint16_t* pu16Adc;
    uint16_t  u16LogicIdx = 0;
    uint16_t  u16Voltage = 0;
    uint8_t   u8GpioFun;
        
      
    if (AppProjectIsInSimuMode() == true) 
    {
        return;
    }
      
    gu8AfeAmountValid[geAfeCommDir] = HalAfeAdbms6832GetAfeOnlineNum();

    if (gu8AfeAmountValid[geAfeCommDir] != TOTAL_AFE_NUMBER)
    {
        gu32AfeCommFailCnt++;
        return;
    }

    for (u8Idx = 0; u8Idx < gu8AfeAmount[geAfeCommDir]; u8Idx++) 
    {
        if (geAfeCommDir == kHAL_AFE_ADBMS6832_COMM_DIR_NORTH) 
        {
            u8AfeIdx = u8Idx;
        } 
        else 
        {	
            u8AfeIdx = TOTAL_AFE_NUMBER - u8Idx - 1;
        }
      
        for (u8RegIdx = 0; u8RegIdx < SINGLE_PARSER_REG_CNT; u8RegIdx++) 
        {
            u8GpioFun = getGpioFunction(u8AfeIdx, eGpioGroup, u8RegIdx);
	
            if (u8GpioFun == kHAL_AFE_ADBMS6832_GPIO_NONE_USE)
            {
                continue;
            }

            pu16Adc = HalAfeAdbms6832GetDecodeAfeRxDataBuf();
          
            u16Voltage = convertNtcAdcToMv(pu16Adc[u8AfeIdx * SINGLE_PARSER_REG_CNT + u8RegIdx]);
          
            u16LogicIdx = getNtcLogicIdx(u8AfeIdx, eGpioGroup, u8RegIdx);

            if (u8GpioFun == kHAL_AFE_ADBMS6832_GPIO_NTC) 
            {
                HalAfeSetNtcVoltage(u16LogicIdx, u16Voltage);
            } 
            else if (u8GpioFun == kHAL_AFE_ADBMS6832_GPIO_BUSBAR) 
            {
                u16LogicIdx &= 0x3ff;
                HalAfeSetNtcBusbarVoltage(u16LogicIdx, u16Voltage);
            }   
            else if (u8GpioFun == kHAL_AFE_ADBMS6832_GPIO_AMBIENT) 
            {  
                u16LogicIdx &= 0x3ff;
                HalAfeSetNtcAmbientVoltage(u16LogicIdx, u16Voltage);
            } 
            else if (u8GpioFun == kHAL_AFE_ADBMS6832_GPIO_OTHER) 
            {  
                u16LogicIdx &= 0x3ff;
                HalAfeSetNtcOtherVoltage(u16LogicIdx, u16Voltage);
            }
        }
    }
}

static void getNextSubFunctionPointer(void) 
{	
    gfpAfeSubFunctionTable = (tfpAfeFunctionTable)((uint32_t)gfpAfeSubFunctionTable + 4);
    gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)gfpAfeSubFunctionTable));
    gu16AfeCmdTimeoutCnt = 0;
}

static void getNextMainFunctionPointer(void) 
{
    tfpAfeFunctionTable fpAfeFunctionTable;


    gu8MainFunctionTableIdx++;

    fpAfeFunctionTable = gfpAfeMainFunctionTable;
    fpAfeFunctionTable = (tfpAfeFunctionTable)((uint32_t)fpAfeFunctionTable + (gu8MainFunctionTableIdx * 4));
    gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)fpAfeFunctionTable));

    if (gfpAfeFunctionProcessor != NULL) 
    {
        gfpAfeFunctionProcessor();
    }
}

static void delayFunction(void)	
{
    if (gu16AfeDelayCnt > 0) 
    {
        gu16AfeDelayCnt--;
    }
    
    if (gu16AfeDelayCnt == 0) 
    {
        getNextSubFunctionPointer();
    }
}

static void readDataEndOrTimeoutHandle(void)
{
    if (gbAfeCbEnd == true) 
    {
        gbAfeCbEnd = false;
        gu16AfeCmdTimeoutCnt = 0;
        
        getNextSubFunctionPointer();
      	  
        if (gfpAfeFunctionProcessor != NULL) 
        {
            gfpAfeFunctionProcessor();
        }
        return;
    }

    gu16AfeCmdTimeoutCnt++;
    
    if (gu16AfeCmdTimeoutCnt > SINGLE_AFE_COM_TIMEOUT) 
    {
        gu16AfeCmdTimeoutCnt = 0;
        getNextSubFunctionPointer();
    }
}

/*========== Communication End Routine subFunction ==========*/
static void checkCommResult(void) 
{
    if (gu8AfeAmount[geAfeCommDir] == gu8AfeAmountValid[geAfeCommDir]) 
    {
        gu16AfeCommTimeoutCnt = 0;
    } 
    else 
    {
        gu8AfeReinitCnt++;
    }
    
    getNextMainFunctionPointer();

    if (gu8AfeReinitCnt > AFE_RETRY_CNT_MAX) 
    {
        afeInitReset();
    }
}

const tfpAfeFunctionTable lastprocessFunTable[] = 
{
    checkCommResult,
    getNextMainFunctionPointer
};

static void mainFunLastProcess(void) 
{
    gfpAfeSubFunctionTable = (tfpAfeFunctionTable)lastprocessFunTable;
    gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)gfpAfeSubFunctionTable));
}
/*========== Communication End Routine subFunction  End==========*/

/*========== Start Balancing Routine subFunction ==========*/
static void readBalanceDataA(void)
{
    HalAfeAdbms6832ReadCfga(geAfeState);
    getNextSubFunctionPointer();
}

static void readBalanceDataB(void)
{
    HalAfeAdbms6832ReadCfgb();
    getNextSubFunctionPointer();
}

static void sendBalanceDataA(void) 
{
    bool bBalEnFlag[HAL_ADBMS6832_MAX_AFE_CELL_CHANNEL_NUM] = {0};
    uint8_t u8Idx;
    uint8_t u8AfeIdx = 0;

    
    for (u8Idx = 0; u8Idx < gu8AfeAmount[geAfeCommDir]; u8Idx++) 
    {
        if (geAfeCommDir == kHAL_AFE_ADBMS6832_COMM_DIR_NORTH) 
        {
            u8AfeIdx = u8Idx;
        } 
        else		
        {
            u8AfeIdx = TOTAL_AFE_NUMBER - u8Idx - 1;
        }
    
        convertuint32BalanceEnToBoolFlag(&bBalEnFlag[0], u8Idx);
        HalAfeAdbms6832SetCfgaCfgbDccDcto(u8AfeIdx, &bBalEnFlag[0]);
    }

    HalAfeAdbms6832WriteCfgaDccDcto();
    getNextSubFunctionPointer();
}

static void sendBalanceDataB(void) 
{
    HalAfeAdbms6832WriteCfgbDccDcto();
    getNextSubFunctionPointer();
}

static void sendStartBalanceCmd(void) 
{
	//if (HalAfeGetBalanceOnFlag() == true)
	{
		HalAfeAdbms6832StartCellBalance();
	}
	getNextSubFunctionPointer();
}

const tfpAfeFunctionTable startBalanceFunTable[] = 
{
    readBalanceDataA,
    readDataEndOrTimeoutHandle,
    readBalanceDataB,
    readDataEndOrTimeoutHandle,
    sendBalanceDataA,
    readDataEndOrTimeoutHandle,
    sendBalanceDataB,		
    readDataEndOrTimeoutHandle,
    sendStartBalanceCmd,	
    readDataEndOrTimeoutHandle,
    getNextMainFunctionPointer
};

static void mainFunStartBalance(void) 
{
    gfpAfeSubFunctionTable = (tfpAfeFunctionTable)startBalanceFunTable;
    gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)gfpAfeSubFunctionTable));
}
/*========== Start Balancing Routine subFunction End ==========*/

/*========== Stop Balancing Routine subFunction ==========*/
static void stopAllCellBalance(void) 
{
    HalAfeAdbms6832StopCellBalance();
    getNextSubFunctionPointer();
}

const tfpAfeFunctionTable stopBalanceFunTable[] = 
{
    stopAllCellBalance,
    getNextMainFunctionPointer
};

static void mainFunStopBalance(void) 
{
    gfpAfeSubFunctionTable = (tfpAfeFunctionTable)stopBalanceFunTable;
    gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)gfpAfeSubFunctionTable));
}
/*========== Stop Balancing Routine subFunction End ==========*/

static void startWakeupAfe(void) 
{
    HalAfeAdbms6832StartWakeupIdle();
    getNextSubFunctionPointer();
}

const tfpAfeFunctionTable startWakeupFunTable[] = 
{
    startWakeupAfe,
    delayFunction,
    getNextMainFunctionPointer
};

static void mainFunStartWakeup(void) 
{
    gfpAfeSubFunctionTable = (tfpAfeFunctionTable)startWakeupFunTable;
    gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)gfpAfeSubFunctionTable));
}

/*========== Get Cell Temperature Routine subFunction  ==========*/
static void startCvtGpioAdc(void) 
{
    HalAfeAdbms6832StartGpioAdc();
    getNextSubFunctionPointer();		
    gu16AfeDelayCnt = GPIO_ADC_DELAY + ADC_SYNC_TIME;
}

static void readGpioAdcA(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadAuXi(HAL_ADBMS6832_CMD_RDAUXA);
    getNextSubFunctionPointer();
}

static void updateGpioAdcGroupA(void) 
{
    halDecodeNtc(kHAL_AFE_ADBMS6832_GPIO_GROUP_A);
    getNextSubFunctionPointer();
}

static void readGpioAdcB(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadAuXi(HAL_ADBMS6832_CMD_RDAUXB);
    getNextSubFunctionPointer();
}

static void updateGpioAdcGroupB(void) 
{
    halDecodeNtc(kHAL_AFE_ADBMS6832_GPIO_GROUP_B);
    getNextSubFunctionPointer();
}

static void readGpioAdcC(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadAuXi(HAL_ADBMS6832_CMD_RDAUXC);
    getNextSubFunctionPointer();
}

static void updateGpioAdcGroupC(void)
{
    halDecodeNtc(kHAL_AFE_ADBMS6832_GPIO_GROUP_C);
    getNextSubFunctionPointer();
}

static void readGpioAdcD(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadAuXi(HAL_ADBMS6832_CMD_RDAUXD);
    getNextSubFunctionPointer();
}

static void updateGpioAdcGroupD(void) 
{
    halDecodeNtc(kHAL_AFE_ADBMS6832_GPIO_GROUP_D);
    getNextSubFunctionPointer();
}

const tfpAfeFunctionTable getNtcNoMultiFunTable[] = 
{
    startCvtGpioAdc,
    delayFunction,
    readGpioAdcA,
    readDataEndOrTimeoutHandle,
    updateGpioAdcGroupA,
    readGpioAdcB,
    readDataEndOrTimeoutHandle,
    updateGpioAdcGroupB,
    readGpioAdcC,
    readDataEndOrTimeoutHandle,
    updateGpioAdcGroupC,
    readGpioAdcD,
    readDataEndOrTimeoutHandle,
    updateGpioAdcGroupD,
    getNextMainFunctionPointer
};

static void mainFunGetNtcVoltage(void) 
{
    gfpAfeSubFunctionTable = (tfpAfeFunctionTable)getNtcNoMultiFunTable;
    gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)gfpAfeSubFunctionTable));
}
/*========== Cell Temperature Routine subFunction End ==========*/

/*========== Get Cell V Routine subFunction ==========*/
static void startCellVadc(void) 
{
    HalAfeAdbms6832StartCellAdc();
    gu16AfeDelayCnt = CELL_V_ADC_DELAY + ADC_SYNC_TIME;
    getNextSubFunctionPointer();
}

static void readCellVGroupA(void)
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadCellV(HAL_ADBMS6832_CMD_RDCVA);
    getNextSubFunctionPointer();
}

static void updateCellvGroupA(void) 
{
    halDecodeCellV(kHAL_AFE_ADBMS6832_CELLV_GROUP_A);
    getNextSubFunctionPointer();
}

static void readCellVGroupB(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadCellV(HAL_ADBMS6832_CMD_RDCVB);
    getNextSubFunctionPointer();
}

static void updateCellvGroupB(void) 
{
    halDecodeCellV(kHAL_AFE_ADBMS6832_CELLV_GROUP_B);
    getNextSubFunctionPointer();
}

static void readCellVGroupC(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadCellV(HAL_ADBMS6832_CMD_RDCVC);
    getNextSubFunctionPointer();
}

static void updateCellvGroupC(void) 
{
    halDecodeCellV(kHAL_AFE_ADBMS6832_CELLV_GROUP_C);
    getNextSubFunctionPointer();
}

static void readCellVGroupD(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadCellV(HAL_ADBMS6832_CMD_RDCVD);
    getNextSubFunctionPointer();
}

static void updateCellvGroupD(void) 
{
    halDecodeCellV(kHAL_AFE_ADBMS6832_CELLV_GROUP_D);
    getNextSubFunctionPointer();
}

static void readCellVGroupE(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadCellV(HAL_ADBMS6832_CMD_RDCVE);
    getNextSubFunctionPointer();
}

static void updateCellvGroupE(void) 
{
    halDecodeCellV(kHAL_AFE_ADBMS6832_CELLV_GROUP_E);
    getNextSubFunctionPointer();
}

static void readCellVGroupF(void) 
{
    gu32AfeCommRequestCnt++;
    HalAfeAdbms6832ReadCellV(HAL_ADBMS6832_CMD_RDCVF);
    getNextSubFunctionPointer();
}

static void updateCellvGroupF(void) 
{
    halDecodeCellV(kHAL_AFE_ADBMS6832_CELLV_GROUP_F);
    HalAfeCalVbatFromCellVoltage();
    getNextSubFunctionPointer();
}

const tfpAfeFunctionTable fpGetCellVoltageFunTable[] = 
{
    startCellVadc,
    readDataEndOrTimeoutHandle,
    delayFunction,
    readCellVGroupA,	
    readDataEndOrTimeoutHandle,	
    updateCellvGroupA,
    readCellVGroupB,
    readDataEndOrTimeoutHandle,
    updateCellvGroupB,
    readCellVGroupC,
    readDataEndOrTimeoutHandle,
    updateCellvGroupC,
    readCellVGroupD,
    readDataEndOrTimeoutHandle,
    updateCellvGroupD,
    readCellVGroupE,
    readDataEndOrTimeoutHandle,
    updateCellvGroupE,
    readCellVGroupF,
    readDataEndOrTimeoutHandle,
    updateCellvGroupF,
    getNextMainFunctionPointer
};

static void mainFunGetCellVoltage(void)	
{
    gfpAfeSubFunctionTable = (tfpAfeFunctionTable)fpGetCellVoltageFunTable;
    gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)gfpAfeSubFunctionTable));
}

static void mainFunEnd(void) 
{
    gfpAfeFunctionProcessor = NULL;
}

const tfpAfeFunctionTable fpAfeMainFunTable[] = 
{
    mainFunStartWakeup,
    mainFunStopBalance,	
    mainFunGetNtcVoltage,	
    mainFunGetCellVoltage,			
    mainFunStartBalance,	
    mainFunLastProcess,		
    mainFunEnd
};

static void halAfeAdbms6832RoutineTask(uint16_t u16Evt) 
{
    static uint8_t gu810msCnt;
    uint8_t u8Str[100];
    
    
    if (u16Evt & kLIB_SW_TIMER_EVT_1_MS) 
    {
        if (gfpAfeFunctionProcessor != NULL)
        {
            gfpAfeFunctionProcessor();
        }
    } 
    if (u16Evt & kLIB_SW_TIMER_EVT_10_9_MS) 
    {
        gu810msCnt++;
      
        if (gu810msCnt > ROUTINE_INTERVAL) 
        {
            gu810msCnt = 0;
            
            if (gfpAfeFunctionProcessor == NULL) 
            {
                gfpAfeMainFunctionTable = (tfpAfeFunctionTable)&fpAfeMainFunTable;
                gu8MainFunctionTableIdx = 0;
                gfpAfeFunctionProcessor = (tfpAfeFunctionTable)(*((uint32_t *)gfpAfeMainFunctionTable));
            }
        }
    }
    if (u16Evt & kLIB_SW_TIMER_EVT_1_S)
    {
        sprintf(u8Str, "AFE REQ_CNT[%d] FAIL_CNT[%d]", gu32AfeCommRequestCnt, gu32AfeCommFailCnt);
        AppSerialUartSendMessage(u8Str);
    }
}

static void halAfeAdbms6832InitTask(uint16_t u16Evt) 
{
    if (u16Evt & kLIB_SW_TIMER_EVT_1_MS)
    {     
        switch (geAfeInitStatus)
		{
            case kHAL_AFE_ADBMS6832_NORTH_INIT_START:
                HalAfeAdbms6832SetCommDir(geAfeCommDir);
                HalAfeAdbms6832ReadCfga(geAfeState);
                geAfeInitStatus = kHAL_AFE_ADBMS6832_NORTH_INIT_END;

                break;
            case kHAL_AFE_ADBMS6832_NORTH_INIT_END:
                if (gbAfeCbEnd == false)
                {
                    return;
                }

                gbAfeCbEnd = false;
                gu8AfeAmount[geAfeCommDir] = HalAfeAdbms6832GetAfeOnlineNum();

                if (gu8AfeAmount[geAfeCommDir] == TOTAL_AFE_NUMBER) 
                {
                    geAfeCommDir = kHAL_AFE_ADBMS6832_COMM_DIR_NORTH;
                    HalAfeAdbms6832SetCommDir(geAfeCommDir);
                    geAfeInitStatus = kHAL_AFE_ADBMS6832_ALL_INIT_START;
                } 
                else 
                {
                    geAfeCommDir = kHAL_AFE_ADBMS6832_COMM_DIR_SOUTH;
                    geAfeInitStatus = kHAL_AFE_ADBMS6832_SOUTH_INIT_START;
                }

                break;
            case kHAL_AFE_ADBMS6832_SOUTH_INIT_START:
                HalAfeAdbms6832SetCommDir(geAfeCommDir);
                HalAfeAdbms6832ReadCfga(geAfeState);
                geAfeInitStatus = kHAL_AFE_ADBMS6832_SOUTH_INIT_END;

                break;
            case kHAL_AFE_ADBMS6832_SOUTH_INIT_END:
                if (gbAfeCbEnd == false)
                {
                    return;
                }

                gbAfeCbEnd = false;
                gu8AfeAmount[geAfeCommDir] = HalAfeAdbms6832GetAfeOnlineNum();

                if (gu8AfeAmount[geAfeCommDir] == TOTAL_AFE_NUMBER) 
                {
                    geAfeCommDir = kHAL_AFE_ADBMS6832_COMM_DIR_SOUTH;
                    HalAfeAdbms6832SetCommDir(geAfeCommDir);
                    geAfeInitStatus = kHAL_AFE_ADBMS6832_ALL_INIT_START;
                }
                else
                {
                    geAfeInitStatus = kHAL_AFE_ADBMS6832_ALL_INIT_FAIL;
                    gu16AfeInitRetryDelay = INIT_RETRY_DELAY;
                }

                break;
            case kHAL_AFE_ADBMS6832_ALL_INIT_START:
                HalAfeAdbms6832WriteCfgaRefon();
                geAfeInitStatus = kHAL_AFE_ADBMS6832_ALL_INIT_END;

                break;
            case kHAL_AFE_ADBMS6832_ALL_INIT_END:  
                if (gbAfeCbEnd == false)
                {
                    return;
                }

                gbAfeCbEnd = false;
                geAfeInitStatus = kHAL_AFE_ADBMS6832_NORTH_INIT_START;
                gfpAfeSWTimerHandlerProcessor = halAfeAdbms6832RoutineTask;	
                geAfeState = kHAL_AFE_ADBMS6832_NORMAL_STATE;
                
                break;
            case kHAL_AFE_ADBMS6832_ALL_INIT_FAIL:
                if (gu16AfeInitRetryDelay > 0)		  
                {
                    gu16AfeInitRetryDelay--;
            
                    if (gu16AfeInitRetryDelay == 0) 
                    {
                        if (AppProjectIsInSimuMode() == false) 
                        {
                            afeInitReset();
                        }
                    }
                }
                break;
        }
   }
}

static void fpAfeSwTimerHandler(__far void *pvdest, uint16_t u16Evt, void *pvDataPtr)
{
    if (u16Evt & kLIB_SW_TIMER_EVT_10_3_MS) 
    {
        afeCommProtect();
    } 

    gfpAfeSWTimerHandlerProcessor(u16Evt);
}

static void fpAfeCbFun(void)
{
    gbAfeCbEnd = true;
}

/* Global function prototypes -----------------------------------------------*/
void HalAfeAdbms6832GetDeviceCnt(uint8_t *pu8NorthCnt, uint8_t *pu8SouthCnt, uint8_t *pu8AfeNum)
{
    *pu8NorthCnt = gu8AfeAmount[kHAL_AFE_ADBMS6832_COMM_DIR_NORTH];
    *pu8SouthCnt = gu8AfeAmount[kHAL_AFE_ADBMS6832_COMM_DIR_SOUTH];
    *pu8AfeNum = TOTAL_AFE_NUMBER;
}

void HalAfeAdbms6832GetCellPhyPosi(uint16_t u16LogicIdx, uint8_t *pu8AfeId, uint8_t *pu8ChId)
{
    *pu8AfeId = gmAfeIdxAndPosiConfig.mCellPhysicalPosi[u16LogicIdx].u8AfeId + 1;
    *pu8ChId = gmAfeIdxAndPosiConfig.mCellPhysicalPosi[u16LogicIdx].u8ChId + 1;
}

void HalAfeAdbms6832GetNtcPhyPosi(uint16_t u16LogicIdx, uint8_t *pu8AfeId, uint8_t *pu8ChId)
{
    *pu8AfeId = gmAfeIdxAndPosiConfig.mNtcPhysicalPosi[u16LogicIdx].u8AfeId + 1;
    *pu8ChId = gmAfeIdxAndPosiConfig.mNtcPhysicalPosi[u16LogicIdx].u8ChId + 1;
}

uint8_t HalAfeAdbms6832GetState(void) 
{
    if (AppProjectIsInSimuMode() == true) 
    {
        return kHAL_AFE_ADBMS6832_NORMAL_STATE;
    }

    return geAfeState;
}

uint8_t HalAfeAdbms6832IsL1Protect(void)
{
    if (gu8AfeCommProtectFlag & AFE_COMM_FLAG_L1) 
	{
		return true;
	}
	
    return false;
}

uint8_t HalAfeAdbms6832IsL2Protect(void) 
{
	if (gu8AfeCommProtectFlag & AFE_COMM_FLAG_L2) 
	{
	    return true;
	}

    return false;
}

uint8_t HalAfeAdbms6832GetComDir(void) 
{
    return geAfeCommDir;
}

void HalAfeAdbms6832Open(tLibFuncPtrRegEvtHandler fpEventHandler) 
{
    gfpAfeEventHandler = fpEventHandler;
    geAfeCommDir = kHAL_AFE_ADBMS6832_COMM_DIR_NORTH;
    geAfeInitStatus = kHAL_AFE_ADBMS6832_NORTH_INIT_START;

    LibSoftwareTimerHandlerOpen(fpAfeSwTimerHandler, 0);
  
    gu16AfeInitRetryDelay = 0;
    gfpAfeSWTimerHandlerProcessor = halAfeAdbms6832InitTask;
    geAfeState = kHAL_AFE_ADBMS6832_INIT_STATE;

    getAfeCellCnt();
    getAfeNtcCnt();
    HalAfeAdbms6832EventRegisterCb(fpAfeCbFun);

    gfpAfeMainFunctionTable = (tfpAfeFunctionTable)&fpAfeMainFunTable;
    
    HalSpiAfeAdbms6832Init();
}
