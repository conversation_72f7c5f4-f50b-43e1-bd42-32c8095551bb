/**
  ******************************************************************************
  * @file        AppSerialCanDavinciCmd.c
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2021/11/19
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

#include "HalAfe.h"

#include "HalRtc.h"
//#include "HalBsp.h"
#include "HalCan.h"

#include "LibSoftwareTimerHandler.h"
#include "AppSerialCanDavinci.h"
#include "AppSerialCanDavinciCmd.h"
#include "AppSerialCanDavinciParameter.h"
//#include "AppBms.h"
//#include "AppBalance.h"
//#include "AppScuIdAssign.h"
//#include "ApiSysPar.h"
//#include "ApiEventLog.h"
//#include "AppGauge.h"
#include "ApiProtectUvp.h"
#include "ApiProtectOvp.h"
//#include "ApiProtectDvp.h"
//#include "ApiProtectMDvp.h"
//#include "ApiFu.h"
//#include "ApiRelayControl.h"
//#include "halSpiRom.h"
//#include "ProjectVersion.h"
void appSerialCanDavinciSendTextMessage(char *str);
#define	canCmdDebugMsg(str)	appSerialCanDavinciSendTextMessage(str)

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define		canCmdScuId()		appProjectGetScuId()
/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/


static void davinciCanAdc(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	AdcValue;

	if(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id) == 0x00)
	{
		//canCmdDebugMsg("Read Current Adc1");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_ADC_OBJ_INDEX,
									0x00);
		AdcValue.i32 = halAfeGetCurrentAdcValue(0);
	}
	else if(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id) == 0x01)
	{
		//canCmdDebugMsg("Read Current Adc2");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_ADC_OBJ_INDEX,
									0x01);
		AdcValue.i32 = halAfeGetCurrentAdcValue(1);
	}
	else if(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id) == 0x10)
	{
		//canCmdDebugMsg("Read Vbat Adc1");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_ADC_OBJ_INDEX,
									0x10);
		AdcValue.i32 = halAfeGetVBatAdcValue(0);
	}
	else if(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id) == 0x11)
	{
		//canCmdDebugMsg("Read Vbat Adc2");
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_ADC_OBJ_INDEX,
									0x11);
		AdcValue.i32 = halAfeGetVBatAdcValue(1);
	}
	else
		return;
	CanPkg.u8Dlc = 4;
	CanPkg.tUnionData.u8Data[0] = AdcValue.u8[0];
	CanPkg.tUnionData.u8Data[1] = AdcValue.u8[1];
	CanPkg.tUnionData.u8Data[2] = AdcValue.u8[2];
	CanPkg.tUnionData.u8Data[3] = AdcValue.u8[3];
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}


static void davinciCanRtc(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	tUnion32Bits	Lbyte;
	tHalRtcDateTime	mRtcDateTime;

	if(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id) == SMP_RD_RTC_SUB_INDEX)
	{
		
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_RTC_OBJ_INDEX,
									SMP_RD_RTC_SUB_INDEX);
		Lbyte.u32 = HalRtcGetSelfUnixTime();


		CanPkg.u8Dlc = 4;
		CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
		CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
		CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
		CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];

		//canCmdDebugMsg("Read RTC");
	}
	else if(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id) == SMP_WR_RTC_SUB_INDEX)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_RTC_OBJ_INDEX,
									SMP_WR_RTC_SUB_INDEX);
		Lbyte.u32 = HalRtcGetSelfUnixTime();


		CanPkg.u8Dlc = 0;

		Lbyte.u32 = GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
		HalRtcSelfUnixTimeToDateTime(Lbyte.u32, &mRtcDateTime);
		HalRtcSetupTime(mRtcDateTime.u8Hour, mRtcDateTime.u8Minute, mRtcDateTime.u8Second);
		HalRtcSetupDate(mRtcDateTime.u16Year, mRtcDateTime.u8Month, mRtcDateTime.u8Day);

		//canCmdDebugMsg("Write RTC");
	}
	else
		return;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}


static void davinciCanRelayOn(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint8_t	scuid;

	scuid = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	if(memcmp(&pCanPkg->tUnionData.u8Data[0], "RelayOn", 7) == 0) 
	{

		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, scuid,
									SMP_CMD_RELAY_ON_OBJ_INDEX,
									canCmdScuId());
		CanPkg.u8Dlc = 0;									
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}
}

static void davinciCanRelayOff(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint8_t	scuid;

	scuid = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	if(memcmp(&pCanPkg->tUnionData.u8Data[0], "RelayOff", 8) == 0) 
	{
		#if 1
		char str[100];
		sprintf(str,"Recv Rly Off Command");
		canCmdDebugMsg(str);
		#endif

		#if MAO_DISSABLE
		appProjectScuRelayOff();
		#endif
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, scuid,
									SMP_CMD_RELAY_OFF_OBJ_INDEX,
									canCmdScuId());
		CanPkg.u8Dlc = 0;									
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}

}


static void davinciCanChecksum(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint8_t	subindex;
	tUnion32Bits	checksum;

	CanPkg.u8Dlc = 4;
								
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_CHECKSUM_OBJ_INDEX,
									subindex);
									

	if(subindex == SMP_FW_CHECKSUM_SUB_INDEX)
	{
		checksum.u32 = apiFuGetFwChecksum();
	}
	else if(subindex == SMP_PAR_CHECKSUM_SUB_INDEX)
	{
		checksum.u32 = apiSysParGetChecksum();
	}
	else if(subindex == SMP_CAL_PAR_CHECKSUM_SUB_INDEX)
	{
		checksum.u32 = apiCaliParGetChecksum();
	}
	else if(subindex == SMP_FW_INTERNAL_CHECKSUM_SUB_INDEX)
		checksum.u32 = apiFuGetFwInternalChecksum();	
	else
		CanPkg.u8Dlc = 0;

	CanPkg.tUnionData.u8Data[0] = checksum.u8[0];
	CanPkg.tUnionData.u8Data[1] = checksum.u8[1];
	CanPkg.tUnionData.u8Data[2] = checksum.u8[2];
	CanPkg.tUnionData.u8Data[3] = checksum.u8[3];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void davinciCanPfFlag(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint8_t	subindex;
//	tUnion32Bits	checksum;

	CanPkg.u8Dlc = 4;
								
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_PF_FLAG_OBJ_INDEX,
									subindex);
									
	if(subindex == SMP_PF_FLAG_CLEAN_ALL_SUB_INDEX)
	{
		#if MAO_DISSABLE
		apiSysParOvpPfClean();
		apiSysParUvpPfClean();
		apiSysParCDvpPfClean();
		apiSysParMDvpPfClean();

		apiProtectOvpPfClean();
		apiProtectUvpPfClean();
		apiProtectMDvpPfClean();
		apiProtectCDvpPfClean();
		#endif
		//canCmdDebugMsg("Clear All PF Flag");
	}
	else if(subindex == SMP_PF_FLAG_CLEAN_OVP_SUB_INDEX)
	{
		#if MAO_DISSABLE
		apiSysParOvpPfClean();
		apiProtectOvpPfClean();
		#endif
		//canCmdDebugMsg("Clear OVP PF Flag");	
	}
	else if(subindex == SMP_PF_FLAG_CLEAN_UVP_SUB_INDEX)
	{
		#if MAO_DISSABLE
		apiSysParUvpPfClean();
		apiProtectUvpPfClean();
		#endif
		//canCmdDebugMsg("Clear UVP PF Flag");
	}
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void davinciCanCleanData(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint8_t	subindex;

	CanPkg.u8Dlc = 0;
								
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_CLEAN_DATA_OBJ_INDEX,
									subindex);
		
	if(subindex == SMP_CLEAN_ALL_DATA_SUB_INDEX)
	{
		//canCmdDebugMsg("Clear All data");	
		#if MAO_DISSABLE
		appGaugeCleanCycleCount();
		apiEventLogClearLogData();
		appGaugeCleanAccPower();
		#endif
	}	
	else if(subindex == SMP_CLEAN_CYCLE_COUNT_SUB_INDEX)
	{
		//canCmdDebugMsg("Clear cycle count");
		#if MAO_DISSABLE
		appGaugeCleanCycleCount();
		#endif
	}
	else if(subindex == SMP_CLEAN_FAULT_LOG1_SUB_INDEX)
	{
		#if MAO_DISSABLE
		apiEventLogClearLogData();
		#endif
		//canCmdDebugMsg("Clear fault log1");
	}
	else if(subindex == SMP_CLEAN_FAULT_LOG2_SUB_INDEX)
	{
		;//canCmdDebugMsg("Clear fault log2");
	}
	else if(subindex == SMP_CLEAN_ACC_POWER_SUB_INDEX)
	{
		#if MAO_DISSABLE
		appGaugeCleanAccPower();
		#endif
	}
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void faultLog1DataCallBack(uint8_t num, uint8_t *pBuf)
{
//	char		str[100];
	uint8_t		n;
	uint8_t		index;
	tHalCanFrame	CanPkg;
	
#if 0
	sprintf(str,"Read %d fault data ....", num);
	canCmdDebugMsg(str);
#endif
	
	index = 0;
	for(n=0; n<num; n++)
	{
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_FAULT_LOG_OBJ_INDEX,
									SMP_RET_FAULT_LOG1_DATA_SUB_INDEX+n);
		CanPkg.u8Dlc = 8;
		LibFifoMemcpy(CanPkg.tUnionData.u8Data, &pBuf[index], 8);
		index += 8;
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}
}

static void davinciCanFaultLog(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint8_t	subindex;
	tUnion32Bits	Lbyte;
	tUnion32Bits	ReadNum;
	CanPkg.u8Dlc = 0;
								
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_FAULT_LOG_OBJ_INDEX,
									subindex);
									
	if(subindex == SMP_READ_FAULT_LOG1_NUM_SUB_INDEX)
	{
		CanPkg.u8Dlc = 4;
		#if MAO_DISSABLE
		Lbyte.u32 = apiEventLogGetLogNumber();
		#endif
		CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
		CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
		CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
		CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
		
//		canCmdDebugMsg("Read Fault Log1 number");
		//appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}	
	else if(subindex == SMP_READ_FAULT_LOG2_NUM_SUB_INDEX)
	{
//		canCmdDebugMsg("Read Fault Log2 number");
	}
	else if(subindex == SMP_READ_FAULT_LOG1_DATA_SUB_INDEX)
	{
		Lbyte.u8[0] = pCanPkg->tUnionData.u8Data[0];
		Lbyte.u8[1] = pCanPkg->tUnionData.u8Data[1];
		Lbyte.u8[2] = pCanPkg->tUnionData.u8Data[2];
		Lbyte.u8[3] = pCanPkg->tUnionData.u8Data[3];

		ReadNum.u8[0] = pCanPkg->tUnionData.u8Data[4];
		ReadNum.u8[1] = pCanPkg->tUnionData.u8Data[5];
		ReadNum.u8[2] = pCanPkg->tUnionData.u8Data[6];
		ReadNum.u8[3] = pCanPkg->tUnionData.u8Data[7];
		
//		canCmdDebugMsg("Read Fault Log1 data");

		#if MAO_DISSABLE
		apiEventLogReadLogData(Lbyte.u32, ReadNum.l, faultLog1DataCallBack);
		#endif

	}
	else if(subindex == SMP_READ_FAULT_LOG2_DATA_SUB_INDEX)
	{
//		canCmdDebugMsg("Read Fault Log2 data");
	}
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}
static void davinciCanSoc(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint8_t	subindex;
	tUnion16Bits	Soc0;
//	tUnion32Bits	ReadNum;
	CanPkg.u8Dlc = 0;
								
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_SOC_OBJ_INDEX,
									subindex);			
	CanPkg.u8Dlc = 0;						
	
	if(subindex == SMP_UPDATE_SOC_SUB_INDEX && 
	   memcmp(pCanPkg->tUnionData.u8Data,"UpdatSoc", 8)==0)
	{
		CanPkg.u8Dlc = 0;
		//canCmdDebugMsg("Update Soc");
		#if MAO_DISSABLE
		appGaugeUpdateSoc0();
		#endif
	}	
	else if(subindex == SMP_SET_SOC_VALUE_SUB_INDEX)
	{
		//canCmdDebugMsg("Set Soc");
		Soc0.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
		#if MAO_DISSABLE
		appGaugeSetSoc0(Soc0.u16);
		#endif
	}
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void davinciCanSpirom(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint8_t		Manufacture_ID;
	tUnion16Bits		Device_ID;
	uint16_t	subindex;
	
					
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_SPIROM_OBJ_INDEX,
									subindex);			
	if(subindex == SMP_SPIROM_READ_ID_SUB_INDEX)
	{
		#if MAO_DISSABLE
		halSpiromGetID(&Manufacture_ID, &Device_ID.u16);
		#endif
		CanPkg.u8Dlc = 3;
		CanPkg.tUnionData.u8Data[0] = Device_ID.u8[0];
		CanPkg.tUnionData.u8Data[1] = Device_ID.u8[1];
		CanPkg.tUnionData.u8Data[2] = Manufacture_ID;
	}
	else	
		CanPkg.u8Dlc = 0;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void davinciCanMBMSBalanceCtrl(tHalCanFrame *pCanPkg){
	tHalCanFrame	CanPkg;
	uint8_t balafe = 0, balch = 0;
	uint16_t	subindex;
	uint32_t mask = 0;
	uint32_t balctrl = 0;
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	
	LibFifoMemcpy(&mask, &pCanPkg->tUnionData.u8Data[0], 4); 
	LibFifoMemcpy(&balctrl, &pCanPkg->tUnionData.u8Data[4], 4); 
	
	while(mask){
		if(mask & 1){
			if(balctrl & 1){
				#if MAO_DISSABLE
				appBalanceLogicCellIdxCtrl(subindex, 1);
				#endif
			}else{
				#if MAO_DISSABLE
				appBalanceLogicCellIdxCtrl(subindex, 0);
				#endif
			}
		}
		mask >>= 1;
		balctrl >>= 1;
		subindex++;
	}
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_MBMSBAL_CTRL_OBJ_INDEX,
									0);			
	CanPkg.u8Dlc = 0;	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);

}

static void davinciCanSaveEkfStatus(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;		
	
	
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
								SMP_CMD_SAVE_EKF_STATUS_OBJ_INDEX,
								0);	
	#if MAO_DISSABLE
	SetSaveEkfStatus();
	#endif
	
	CanPkg.u8Dlc = 0;
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void davinciCanbusVersion(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;		
	
	//canCmdDebugMsg("Read Protocol Version");

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_CANBUS_PROTOCOL_VER_OBJ_INDEX,
									0);			
	CanPkg.u8Dlc = 2;
	CanPkg.tUnionData.u8Data[0] = CANBUS_PROTOCOL_VER % 0x100;
	CanPkg.tUnionData.u8Data[1] = CANBUS_PROTOCOL_VER / 0x100;
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void davinciCanbusOpenWireFlag(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;	
	uint16_t	subindex;
	tUnion32Bits	Lbyte;
	tUnion16Bits	Ibyte;

	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	//canCmdDebugMsg("Read Open Wire Flag");

	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_TX, canCmdScuId(),
									SMP_CMD_OPEN_WIRE_FLAG_OBJ_INDEX,
									subindex);
	#if MAO_DISSABLE	
	Lbyte.u32 = halLtcAfeGetCellOpenWirePosi(subindex);
	Ibyte.u16 = halLtcAfeGetGpioOpenWirePosi(subindex);
	#endif
	CanPkg.u8Dlc = 6;
	CanPkg.tUnionData.u8Data[0] = Lbyte.u8[0];
	CanPkg.tUnionData.u8Data[1] = Lbyte.u8[1];
	CanPkg.tUnionData.u8Data[2] = Lbyte.u8[2];
	CanPkg.tUnionData.u8Data[3] = Lbyte.u8[3];
	
	CanPkg.tUnionData.u8Data[4] = Ibyte.u8[0];
	CanPkg.tUnionData.u8Data[5] = Ibyte.u8[1];
	
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

//=========================================================================

SMP_CAN_DECODE_CMD_START(mDavinciCanCmdRxTab)
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_PAR_WR_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanParameterRdWrite)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_PAR_RD_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanParameterRdWrite)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_RTC_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanRtc)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_ADC_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanAdc)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_RELAY_ON_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanRelayOn)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_RELAY_OFF_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanRelayOff)


	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_CHECKSUM_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanChecksum)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_PF_FLAG_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanPfFlag)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_CLEAN_DATA_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanCleanData)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_FAULT_LOG_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanFaultLog)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_SOC_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanSoc)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_SPIROM_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanSpirom)
					
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
								SMP_CMD_MBMSBAL_CTRL_OBJ_INDEX,
								0),
								CHECK_SMP_CAN_OBJ,
								davinciCanMBMSBalanceCtrl)
							
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_SAVE_EKF_STATUS_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanSaveEkfStatus)						

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_CANBUS_PROTOCOL_VER_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanbusVersion)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_CMD_RX, 0,
									SMP_CMD_OPEN_WIRE_FLAG_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								davinciCanbusOpenWireFlag)


									
SMP_CAN_DECODE_CMD_END();


/* Public function prototypes -----------------------------------------------*/
void DavinciCanFunCmdRx(tHalCanFrame *pCanPkg)
{
//	uint8_t	i,n;
	uint8_t cmdIndex;
	
//	char	str[100];
//	char	str1[100];
	
	//appSerialCanDavinciPutPkgToCanFifo(pCanPkg);
	//canCmdDebugMsg("Decode Rx Cmd");

 	cmdIndex = 0;
	for(cmdIndex = 0; mDavinciCanCmdRxTab[cmdIndex].fun != 0; cmdIndex++)
	{
		if((mDavinciCanCmdRxTab[cmdIndex].canid & mDavinciCanCmdRxTab[cmdIndex].mask) == 
		   (mDavinciCanCmdRxTab[cmdIndex].mask & pCanPkg -> u32Id) &&
		    appSerialCanDavinciIsCorrectScuId(pCanPkg))
		{
			mDavinciCanCmdRxTab[cmdIndex].fun(pCanPkg);
		
			break; 		
 		}
	}
}


/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/
