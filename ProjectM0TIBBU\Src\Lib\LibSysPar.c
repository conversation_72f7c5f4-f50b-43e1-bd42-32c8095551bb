/*
******************************************************************************
* @file     LibSysPar.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include "LibSysPar.h"
#include "HalAfe.h"
#include "HalEeProm.h"
#include "LibSoftwareTimerHandler.h"
#include "LibNtc.h"

void AppSerialUartSendMessage(char *str);
#define	LibSysParDebugMsg(str)	//AppSerialUartSendMessage((char *)str)

void DumpBuffer(uint8_t *pu8Buf,uint16_t u16Len);

/* Private define ------------------------------------------------------------*/
#define	LIB_SYS_PAR_EEPROM_ERASE	HalEePromErase
#define LIB_SYS_PAR_EEPROM_WRITE	HalEePromWrite
#define	LIB_SYS_PAR_EEPROM_READ		HalEePromRead
#define LIB_DATA_FLASH_ECC_START_ADDR   (0x41D00000U)
#define LIB_SYS_TEMP_TO_VOL(i16Temp)	LibTemperatureToVoltage(i16Temp)
#define LIB_SYS_TEMP_TO_ADC(i16Temp)	LibSetRealTemperatureToInternalValue(i16Temp)


#define	SYSPAR_HEAD_INFO			"SysPar02"
#define	SYSPAR_DATE_CODE			0x0706
#define	PAR_ADDR					(LIB_DATA_FLASH_ECC_START_ADDR + 4L * 1024L)

#define	BAT_INFO_PAR_HEAD_INFO		"BatPar01"
#define	BAT_INFO_PAR_DATE_CODE		0x1108
#define	BAT_INFO_PAR_ADDR			(LIB_DATA_FLASH_ECC_START_ADDR)

/* Private typedef -----------------------------------------------------------*/
#define	BATINFO_OVP_PF_FLAG		0x0001
#define	BATINFO_UVP_PF_FLAG		0x0002
#define	BATINFO_CDVP_PF_FLAG	0x0004
#define	BATINFO_MDVP_PF_FLAG	0x0008

#define	MAX_LINE_LOSE_NUM		4


typedef struct{
	uint8_t			u8HeadInfo[8];
	uint16_t		u16ParLeng;
	uint16_t		u16DateCode;
	uint32_t		u32Checksum;
	uint32_t		u32Qmax;
	uint16_t		u16QmaxUpdateTimes;
	uint16_t		u16CycleCount;
	uint16_t		u16PfFlag;
	uint32_t		u32Reserved;
}tBatteryInfo;


typedef struct{
	uint8_t			u8HeadInfo[8];
	uint16_t		u16ParLeng;
	uint16_t		u16DateCode;
	uint32_t		u32Checksum;
	uint32_t		u32HwVersion;
	uint32_t		u32DesignedCapacity;
	uint16_t		u16ZeroCurrent;
	uint16_t		u16MinChargeDischargeCurrent;
	uint16_t		u16MinFlatVoltage;
	uint16_t		u16MaxFlatVoltage;
	uint32_t		u32CellFlag[MAX_BMU_NUM];
	uint32_t		u32NtcFlag[MAX_BMU_NUM];
	
	uint32_t		u32CellBusBarFlag[MAX_BMU_NUM];
	uint32_t		u32NtcAmbientFlag[MAX_BMU_NUM];
	uint32_t		u32NtcBusBarFlag[MAX_BMU_NUM];
	uint32_t		u32NtcOtherFlag[MAX_BMU_NUM];	
	
	uint8_t			u8TotalAfeNumber;
	uint16_t		u16TerminateVoltage;
	struct{
		uint16_t		u16SetValue;
		uint8_t			u8SetTime;
	}mOtHwSetValue;
	struct{
		uint16_t		u16SetValue;
		uint8_t			u8SetTime;
	}mUtHwSetValue;
	struct{
		uint16_t		u16SetValue;
		uint8_t			u8SetTime;
	}mOvpHwSetValue;
	struct{
		uint16_t		u16SetValue;
		uint8_t			u8SetTime;
	}mUvpHwSetValue;
	
	uint16_t		u16PreDischargeTime;
	uint16_t		u16PreDischargeThreshold;
	uint16_t		u16RelayOnThreshold;
	
	struct{
		uint8_t		u8L1Time;
		uint8_t		u8L2Time;
	}mAfeComm;
	
	struct{
		uint16_t	u16Current;
		uint16_t	u16Voltage;
		uint8_t		u8Time;
	}mFullCharge;
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mOvp[3];
		
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mUvp[3];
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mDvp[3];
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mCotp[3];
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mCutp[3];
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mDotp[3];

	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mDutp[3];
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mDtp[3];

	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mCocp[3];
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mDocp[3];
	
	tOcvRaTable	mOcvTable[API_SYS_PAR_MAX_OCV_TABLE_NUM];
	
	tOcvRaTable	mRaTable[API_SYS_PAR_MAX_RA_TABLE_NUM];
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
	}mOvpPf;
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
	}mUvpPf;

//------------------------------------
	struct{
		uint8_t			u8DutySet;
		uint8_t			u8DutyRest;
		uint8_t			u8TempSet;
		uint8_t			u8TempRelease;
		//------------------------------------
		uint16_t		u16ChgSet;
		uint16_t		u16ChgRelease;
		uint16_t		u16ChgDeltaSet;
		uint16_t		u16ChgDeltaRelease;
		//------------------------------------
		uint16_t		u16DhgSet;
		uint16_t		u16DhgRelease;
		uint16_t		u16DhgDeltaSet;
		uint16_t		u16DhgDeltaRelease;
		//------------------------------------
		uint16_t		u16RlxSet;
		uint16_t		u16RlxRelease;
		uint16_t		u16RlxDeltaSet;
		uint16_t		u16RlxDeltaRelease;
	}mBalance;
	
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;
	}mScuOt[5][3];
	
	struct{
		uint16_t	u16ChgCurrent;
		uint16_t	u16DsgCurrent;
		uint16_t	u16ChgPeakCurrent;
		uint16_t	u16DsgPeakCurrent;
		uint8_t		u8ChgPeakSecond;
		uint8_t		u8DsgPeakSecond;
	}mMaxCurrent;
	
	struct{
		uint16_t	u16Voltage;
		uint16_t	u16MaxVoltage;
		uint16_t	u16MinVoltage;
	}mRate;	
		
	struct{
		uint16_t	u16Channel;
		uint32_t	u32RValue;
	}mLineLoss[4];
		
	struct{
		uint16_t	u16SetValue;
		uint8_t		u8SetTime;
		uint16_t	u16ReleaseValue;
		uint8_t		u8ReleaseTime;	
	}mDip[3];	
	
	uint32_t	u32SystemActiveFlag;
	uint8_t		u8NoteMessage[MAX_NOTE_MESSAGE_STRING_ITEM + 2];
	
	uint16_t	u16BmuPassiveBalRVal; 
	uint32_t	u32Reserved;
}tSysRomPar;
typedef struct{
	uint16_t		u16CellNumber;
	uint16_t		u16NtcNumber;
	uint16_t		u16CellLogicIndexOffset[MAX_BMU_NUM];
	uint8_t			u8CellNumberOfAfe[MAX_BMU_NUM];
	uint8_t			u8CellBusbarNumber;
	uint8_t			u8NtcBusbarNumber;
	uint8_t			u8NtcAmbientNumber;
	uint8_t			u8NtcOtherNumber;
	
	struct{
		uint16_t	u16SetAdcValue;
		uint16_t	u16ReleaseAdcValue;
	}mCotp[3];
	struct{
		uint16_t	u16SetAdcValue;
		uint16_t	u16ReleaseAdcValue;
	}mCutp[3];
	struct{
		uint16_t	u16SetAdcValue;
		uint16_t	u16ReleaseAdcValue;
	}mDotp[3];

	struct{
		uint16_t	u16SetAdcValue;
		uint16_t	u16ReleaseAdcValue;
	}mDutp[3];
	struct{
		uint16_t	u16TempSetAdcValue;
		uint16_t	u16TempReleaseAdcValue;	
	}mBalance;
}tSysRamPar;

typedef struct{
	tSysRomPar	mRomPar;
	tSysRamPar	mRamPar;
}tSysPar;



/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static tBatteryInfo	gmBatteryInfo;
static tSysPar	gmSystemParemater;

static	uint8_t gu8BatInfoIdleCount = 0;
static	uint8_t gu8SystemParIdleCount = 0;

/* Public variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
static void LibSysParSetRamValue(void);


static void LibSysParSetBatteryInfoDefaultValue(void)
{
	gmBatteryInfo.u32Qmax = 280000;
	gmBatteryInfo.u16QmaxUpdateTimes = 0;
	gmBatteryInfo.u16CycleCount = 0;
	gmBatteryInfo.u16PfFlag =0;
}

static void saveBatteryInfoPar(void)
{
	uint32_t		*pu32Par;
	uint32_t		u32Checksum = 0;
	tHalEeProm		mHalEeProm;
	
	memcpy(&gmBatteryInfo.u8HeadInfo, BAT_INFO_PAR_HEAD_INFO, 8);
	gmBatteryInfo.u16ParLeng = sizeof(tBatteryInfo);
	gmBatteryInfo.u16DateCode = BAT_INFO_PAR_DATE_CODE;
	gmBatteryInfo.u32Checksum = 0;
	gmBatteryInfo.u32Reserved = 0;
	pu32Par =(uint32_t *)&gmBatteryInfo;
	while(1)
	{
		u32Checksum ^= (uint32_t)*pu32Par;
		pu32Par++;
		if(pu32Par >= &gmBatteryInfo.u32Reserved)
			break;
	}
	gmBatteryInfo.u32Checksum = u32Checksum;
	
	mHalEeProm.u32StartAddress = BAT_INFO_PAR_ADDR;
	mHalEeProm.u16Length = gmBatteryInfo.u16ParLeng;
	mHalEeProm.pu8DataBuffer = (uint8_t *)&gmBatteryInfo;
	
	if(LIB_SYS_PAR_EEPROM_ERASE(&mHalEeProm) != 0)
		LibSysParDebugMsg("Save batinfo Par ..erase error");
	
	LIB_SYS_PAR_EEPROM_WRITE(&mHalEeProm);
	
	LibSysParDebugMsg("Save batinfo Par");
}


static void LibSysParLoadBatteryInfoPar(void)
{
	uint8_t			u8Buffer[2100];
	tHalEeProm		mHalEeProm;
	tBatteryInfo	*pmBatteryInfo;
	uint8_t			u8Flag = 0;
	uint32_t		*pu32Par;
	uint32_t		u32Checksum = 0;
	char			c8Str[100];
	
	mHalEeProm.u32StartAddress = BAT_INFO_PAR_ADDR;
	mHalEeProm.u16Length = sizeof(tBatteryInfo);
	mHalEeProm.pu8DataBuffer = u8Buffer;
	
	LibSysParDebugMsg("Load bat Par");
	LIB_SYS_PAR_EEPROM_READ(&mHalEeProm);
	
	//DumpBuffer(buffer,64);
	
	pmBatteryInfo = (tBatteryInfo *)u8Buffer;
	if(memcmp(pmBatteryInfo->u8HeadInfo, BAT_INFO_PAR_HEAD_INFO, 8) != 0)
	{
		LibSysParDebugMsg("bat info Head Info Error");	
		u8Flag = 1;
	}
	else if(pmBatteryInfo->u16ParLeng != sizeof(tBatteryInfo))
	{
		LibSysParDebugMsg("batinfo len Error");	
		u8Flag = 1;
	}
	else if(pmBatteryInfo->u16DateCode != BAT_INFO_PAR_DATE_CODE)
	{
		LibSysParDebugMsg("bat info DateCode Error");	
		u8Flag = 1;
	}
	if(u8Flag == 0)
	{
		pu32Par =(uint32_t *)pmBatteryInfo;
		while(1)
		{
			u32Checksum ^= (uint32_t)*pu32Par;
			pu32Par++;
			if(pu32Par >= &pmBatteryInfo->u32Reserved)
				break;
		}
		if(u32Checksum != 0)
		{	
			u8Flag = 1;
			sprintf(c8Str,"BatInfo Checksum Error =%.8X %.8X", u32Checksum,
					pmBatteryInfo->u32Checksum	
						);
			LibSysParDebugMsg(c8Str);
		}
	}
	if(u8Flag != 0)
	{
	}
	else
	{
		LibSysParDebugMsg("BatInfo Load Par ok");		
		memcpy(&gmBatteryInfo ,pmBatteryInfo, sizeof(tBatteryInfo));
		
		sprintf(c8Str,"Qmax=%d" ,gmBatteryInfo.u32Qmax);
		LibSysParDebugMsg(_c8Str);
	}	
}
static void LibSysParBatteryInfoSwTimerHandler(__far void *pvDest, uint16_t u16Evt, void *pvDataPtr)
{
	if(u16Evt & kLIB_SW_TIMER_EVT_10_2_MS)		      
	{
		if(gu8BatInfoIdleCount)
		{
			gu8BatInfoIdleCount--;
			if(gu8BatInfoIdleCount ==  0)
			{
				saveBatteryInfoPar();
				LibSoftwareTimerHandlerClose(LibSysParBatteryInfoSwTimerHandler, 0);
			}
		}
	}
}

static void resetBatteryInfoIdleCount(void)
{
	if(gu8BatInfoIdleCount == 0)
	{
	  	LibSoftwareTimerHandlerOpen(LibSysParBatteryInfoSwTimerHandler, 0);
	}
	gu8BatInfoIdleCount  = 50;
}


static uint16_t LibSysParLoadSysPar(void)
{
	uint8_t		u8Buffer[2100];
	tHalEeProm	mHalEeProm;
	tSysPar		*pmSysPar;
	uint8_t		u8Flag = 0;
	uint32_t	*pu32Par;
	uint32_t	u32Checksum = 0;
	char	c8Str[100];
	
	mHalEeProm.u32StartAddress = PAR_ADDR;
	mHalEeProm.u16Length = sizeof(tSysRomPar);
	mHalEeProm.pu8DataBuffer = u8Buffer;
	
	LibSysParDebugMsg("Load Par");
	//LIB_SYS_PAR_EEPROM_READ(&_mHalEeProm);
	
	//DumpBuffer(buffer,64);
	pmSysPar = (tSysPar *)u8Buffer;
	
	sprintf(c8Str, "par len = %d %d",
		pmSysPar->mRomPar.u16ParLeng, sizeof(tSysRomPar));
	LibSysParDebugMsg(c8Str);
	
	
	if(memcmp(pmSysPar->mRomPar.u8HeadInfo, SYSPAR_HEAD_INFO, 8) != 0)
	{
		LibSysParDebugMsg("Head Info Error");	
		u8Flag = 1;
	}
	else if(pmSysPar->mRomPar.u16ParLeng != sizeof(tSysRomPar))
	{
		LibSysParDebugMsg("len Error");	
		u8Flag = 1;
	}
	else if(pmSysPar->mRomPar.u16DateCode != SYSPAR_DATE_CODE)
	{
		LibSysParDebugMsg("DateCode Error");	
		u8Flag = 1;
	}
	if(u8Flag == 0)
	{
		pu32Par =(uint32_t *)pmSysPar;
		while(1)
		{
			u32Checksum ^= (uint32_t)*pu32Par;
			pu32Par++;
			if(pu32Par >= &pmSysPar->mRomPar.u32Reserved)
				break;
		}
		if(u32Checksum != 0)
		{	
			u8Flag = 1;
			sprintf(c8Str,"Checksum Error =%.8X %.8X", u32Checksum,
					pmSysPar->mRomPar.u32Checksum	
						);
			LibSysParDebugMsg((uint8_t *)c8Str);
		}
	}
	if(u8Flag != 0)
	{
	}
	else
	{
		LibSysParDebugMsg("Load Par ok");		
		memcpy(&gmSystemParemater.mRomPar ,&pmSysPar->mRomPar, sizeof(tSysRomPar));
	}
	return 0;
}



static void LibSysParSaveSysPar(void)
{
	uint32_t	*u32Par;
	uint32_t	u32Checksum = 0;
	tHalEeProm	mHalEeProm;
	
	memcpy(&gmSystemParemater.mRomPar.u8HeadInfo, SYSPAR_HEAD_INFO, 8);
	gmSystemParemater.mRomPar.u16ParLeng = sizeof(tSysRomPar);
	gmSystemParemater.mRomPar.u16DateCode = SYSPAR_DATE_CODE;
	gmSystemParemater.mRomPar.u32Checksum = 0;
	gmSystemParemater.mRomPar.u32Reserved = 0;

	u32Par =(uint32_t *)&gmSystemParemater.mRomPar;
	while(1)
	{
		u32Checksum ^= (uint32_t)*u32Par;
		u32Par++;
		if(u32Par >= &gmSystemParemater.mRomPar.u32Reserved)
			break;
	}
	gmSystemParemater.mRomPar.u32Checksum = u32Checksum;
	
	mHalEeProm.u32StartAddress = PAR_ADDR;
	mHalEeProm.u16Length = gmSystemParemater.mRomPar.u16ParLeng;
	mHalEeProm.pu8DataBuffer = (uint8_t *)&gmSystemParemater.mRomPar;
	
	if(LIB_SYS_PAR_EEPROM_ERASE(&mHalEeProm) != 0)
			LibSysParDebugMsg("Save Sys Par ..erase error");
	
	LIB_SYS_PAR_EEPROM_WRITE(&mHalEeProm);
	
	LibSysParDebugMsg("Save Sys Par");
}

static void sysParSwTimerHandler(__far void *dest, uint16_t evt, void *vDataPtr)
{
	if(evt & kLIB_SW_TIMER_EVT_10_3_MS)
	{
		if(gu8SystemParIdleCount != 0)
		{
			gu8SystemParIdleCount--;
			if(gu8SystemParIdleCount == 0)
			{
				LibSysParSaveSysPar();
				LibSysParSetRamValue();
				LibSoftwareTimerHandlerClose(sysParSwTimerHandler, 0);
			}
		}
	}
}
static void resetSysParIdleCount(void)
{
	if(gu8SystemParIdleCount == 0)
	{
	  	LibSoftwareTimerHandlerOpen(sysParSwTimerHandler, 0);
	}
	gu8SystemParIdleCount  = 50;
}
static void setDefaultOvpValue(void)
{
	gmSystemParemater.mRomPar.mOvp[0].u16SetValue = 35800;
	gmSystemParemater.mRomPar.mOvp[0].u8SetTime = 10;
	gmSystemParemater.mRomPar.mOvp[0].u16ReleaseValue = 34500;
	gmSystemParemater.mRomPar.mOvp[0].u8ReleaseTime = 20;
         
	gmSystemParemater.mRomPar.mOvp[1].u16SetValue = 36000;
	gmSystemParemater.mRomPar.mOvp[1].u8SetTime = 10;
	gmSystemParemater.mRomPar.mOvp[1].u16ReleaseValue = 34500;
	gmSystemParemater.mRomPar.mOvp[1].u8ReleaseTime = 20;
         
	gmSystemParemater.mRomPar.mOvp[2].u16SetValue = 36200;
	gmSystemParemater.mRomPar.mOvp[2].u8SetTime = 6;
	gmSystemParemater.mRomPar.mOvp[2].u16ReleaseValue = 34500;
	gmSystemParemater.mRomPar.mOvp[2].u8ReleaseTime = 6;
}
static void setDefaultUvpValue(void)
{
	gmSystemParemater.mRomPar.mUvp[0].u16SetValue = 27500;
	gmSystemParemater.mRomPar.mUvp[0].u8SetTime = 10;
	gmSystemParemater.mRomPar.mUvp[0].u16ReleaseValue = 30000;
	gmSystemParemater.mRomPar.mUvp[0].u8ReleaseTime = 20;
	
	gmSystemParemater.mRomPar.mUvp[1].u16SetValue = 26500;
	gmSystemParemater.mRomPar.mUvp[1].u8SetTime = 10;
	gmSystemParemater.mRomPar.mUvp[1].u16ReleaseValue = 30000;
	gmSystemParemater.mRomPar.mUvp[1].u8ReleaseTime = 20;
                   
	gmSystemParemater.mRomPar.mUvp[2].u16SetValue = 25600;
	gmSystemParemater.mRomPar.mUvp[2].u8SetTime = 6;
	gmSystemParemater.mRomPar.mUvp[2].u16ReleaseValue = 30000;
	gmSystemParemater.mRomPar.mUvp[2].u8ReleaseTime = 6;	
}

static void setDefaultDvpValue(void)
{
	gmSystemParemater.mRomPar.mDvp[0].u16SetValue =3000;
	gmSystemParemater.mRomPar.mDvp[0].u8SetTime = 0;
	gmSystemParemater.mRomPar.mDvp[0].u16ReleaseValue = 2000;
	gmSystemParemater.mRomPar.mDvp[0].u8ReleaseTime = 0;
	
	gmSystemParemater.mRomPar.mDvp[1].u16SetValue = 5000;
	gmSystemParemater.mRomPar.mDvp[1].u8SetTime = 0;
	gmSystemParemater.mRomPar.mDvp[1].u16ReleaseValue = 4000;
	gmSystemParemater.mRomPar.mDvp[1].u8ReleaseTime = 0;
                   
	gmSystemParemater.mRomPar.mDvp[2].u16SetValue = 10000;
	gmSystemParemater.mRomPar.mDvp[2].u8SetTime = 0;
	gmSystemParemater.mRomPar.mDvp[2].u16ReleaseValue = 7000;
	gmSystemParemater.mRomPar.mDvp[2].u8ReleaseTime = 0;			
}

static void setDefaultDtpValue(void)
{
	gmSystemParemater.mRomPar.mDtp[0].u16SetValue = 8;
	gmSystemParemater.mRomPar.mDtp[0].u8SetTime = 0;
	gmSystemParemater.mRomPar.mDtp[0].u16ReleaseValue = 5;
	gmSystemParemater.mRomPar.mDtp[0].u8ReleaseTime = 0;
	
	gmSystemParemater.mRomPar.mDtp[1].u16SetValue = 13;
	gmSystemParemater.mRomPar.mDtp[1].u8SetTime = 0;
	gmSystemParemater.mRomPar.mDtp[1].u16ReleaseValue = 10;
	gmSystemParemater.mRomPar.mDtp[1].u8ReleaseTime = 0;
                   
	gmSystemParemater.mRomPar.mDtp[2].u16SetValue = 13;
	gmSystemParemater.mRomPar.mDtp[2].u8SetTime = 0;
	gmSystemParemater.mRomPar.mDtp[2].u16ReleaseValue = 10;
	gmSystemParemater.mRomPar.mDtp[2].u8ReleaseTime = 0;		
}

static void setDefaultCotpValue(void)
{
	gmSystemParemater.mRomPar.mCotp[0].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(45);
	gmSystemParemater.mRomPar.mCotp[0].u8SetTime = 20;
	gmSystemParemater.mRomPar.mCotp[0].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(40);
	gmSystemParemater.mRomPar.mCotp[0].u8ReleaseTime = 20;
                   
	gmSystemParemater.mRomPar.mCotp[1].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(50);
	gmSystemParemater.mRomPar.mCotp[1].u8SetTime = 20;
	gmSystemParemater.mRomPar.mCotp[1].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(45);
	gmSystemParemater.mRomPar.mCotp[1].u8ReleaseTime = 20;
                   
	gmSystemParemater.mRomPar.mCotp[2].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(52);
	gmSystemParemater.mRomPar.mCotp[2].u8SetTime = 6;
	gmSystemParemater.mRomPar.mCotp[2].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(47);
	gmSystemParemater.mRomPar.mCotp[2].u8ReleaseTime = 6;
}
static void setDefaultCutpValue(void)
{
	gmSystemParemater.mRomPar.mCutp[0].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(5);
	gmSystemParemater.mRomPar.mCutp[0].u8SetTime = 20;
	gmSystemParemater.mRomPar.mCutp[0].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(10);
	gmSystemParemater.mRomPar.mCutp[0].u8ReleaseTime = 20;
                    
	gmSystemParemater.mRomPar.mCutp[1].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(2);
	gmSystemParemater.mRomPar.mCutp[1].u8SetTime = 20;
	gmSystemParemater.mRomPar.mCutp[1].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(5);
	gmSystemParemater.mRomPar.mCutp[1].u8ReleaseTime = 20;
                    
	gmSystemParemater.mRomPar.mCutp[2].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(-3);
	gmSystemParemater.mRomPar.mCutp[2].u8SetTime = 6;
	gmSystemParemater.mRomPar.mCutp[2].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(2);
	gmSystemParemater.mRomPar.mCutp[2].u8ReleaseTime = 6;
}
static void setDefaultDotpValue(void)
{
	gmSystemParemater.mRomPar.mDotp[0].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(45);
	gmSystemParemater.mRomPar.mDotp[0].u8SetTime = 20;
	gmSystemParemater.mRomPar.mDotp[0].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(40);
	gmSystemParemater.mRomPar.mDotp[0].u8ReleaseTime = 20;
                    
	gmSystemParemater.mRomPar.mDotp[1].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(50);
	gmSystemParemater.mRomPar.mDotp[1].u8SetTime = 20;
	gmSystemParemater.mRomPar.mDotp[1].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(45);
	gmSystemParemater.mRomPar.mDotp[1].u8ReleaseTime = 20;
                    
	gmSystemParemater.mRomPar.mDotp[2].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(52);
	gmSystemParemater.mRomPar.mDotp[2].u8SetTime = 6;
	gmSystemParemater.mRomPar.mDotp[2].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(47);
	gmSystemParemater.mRomPar.mDotp[2].u8ReleaseTime = 6;
	
}
static void setDefaultDutpValue(void)
{
	gmSystemParemater.mRomPar.mDutp[0].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(5);
	gmSystemParemater.mRomPar.mDutp[0].u8SetTime = 20;
	gmSystemParemater.mRomPar.mDutp[0].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(10);
	gmSystemParemater.mRomPar.mDutp[0].u8ReleaseTime = 20;
                    
	gmSystemParemater.mRomPar.mDutp[1].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(2);
	gmSystemParemater.mRomPar.mDutp[1].u8SetTime = 20;
	gmSystemParemater.mRomPar.mDutp[1].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(5);
	gmSystemParemater.mRomPar.mDutp[1].u8ReleaseTime = 20;
                    
	gmSystemParemater.mRomPar.mDutp[2].u16SetValue = 
			LIB_SYS_TEMP_TO_ADC(-3);
	gmSystemParemater.mRomPar.mDutp[2].u8SetTime = 6;
	gmSystemParemater.mRomPar.mDutp[2].u16ReleaseValue = 
			LIB_SYS_TEMP_TO_ADC(2);
	gmSystemParemater.mRomPar.mDutp[2].u8ReleaseTime = 6;
}

static void setDefaultCocpValue(void)
{
	gmSystemParemater.mRomPar.mCocp[0].u16SetValue = 260;
	gmSystemParemater.mRomPar.mCocp[0].u8SetTime = 10;
	gmSystemParemater.mRomPar.mCocp[0].u16ReleaseValue = 250;
	gmSystemParemater.mRomPar.mCocp[0].u8ReleaseTime = 10;
                    
	gmSystemParemater.mRomPar.mCocp[1].u16SetValue = 270;
	gmSystemParemater.mRomPar.mCocp[1].u8SetTime = 10;
	gmSystemParemater.mRomPar.mCocp[1].u16ReleaseValue = 260;
	gmSystemParemater.mRomPar.mCocp[1].u8ReleaseTime = 10;
                    
	gmSystemParemater.mRomPar.mCocp[2].u16SetValue = 275;
	gmSystemParemater.mRomPar.mCocp[2].u8SetTime = 5;
	gmSystemParemater.mRomPar.mCocp[2].u16ReleaseValue = 270;
	gmSystemParemater.mRomPar.mCocp[2].u8ReleaseTime = 20;
}

static void setDefaultDocpValue(void)
{
	gmSystemParemater.mRomPar.mDocp[0].u16SetValue = 260;
	gmSystemParemater.mRomPar.mDocp[0].u8SetTime = 10;
	gmSystemParemater.mRomPar.mDocp[0].u16ReleaseValue = 250;
	gmSystemParemater.mRomPar.mDocp[0].u8ReleaseTime = 10;
                    
	gmSystemParemater.mRomPar.mDocp[1].u16SetValue = 270;
	gmSystemParemater.mRomPar.mDocp[1].u8SetTime = 10;
	gmSystemParemater.mRomPar.mDocp[1].u16ReleaseValue = 260;
	gmSystemParemater.mRomPar.mDocp[1].u8ReleaseTime = 10;
                    
	gmSystemParemater.mRomPar.mDocp[2].u16SetValue = 275;
	gmSystemParemater.mRomPar.mDocp[2].u8SetTime = 5;
	gmSystemParemater.mRomPar.mDocp[2].u16ReleaseValue = 270;
	gmSystemParemater.mRomPar.mDocp[2].u8ReleaseTime = 20;
}
static void setDefaultBalanceValue(void)
{
	gmSystemParemater.mRomPar.mBalance.u8DutySet = 10;
	gmSystemParemater.mRomPar.mBalance.u8DutyRest = 5;
	gmSystemParemater.mRomPar.mBalance.u8TempSet = 
				LIB_SYS_TEMP_TO_ADC(50);
	gmSystemParemater.mRomPar.mBalance.u8TempRelease = 
				LIB_SYS_TEMP_TO_ADC(53);
	gmSystemParemater.mRomPar.mBalance.u16ChgSet = 36000;
	gmSystemParemater.mRomPar.mBalance.u16ChgRelease = 36000;
	gmSystemParemater.mRomPar.mBalance.u16ChgDeltaSet = 200;
	gmSystemParemater.mRomPar.mBalance.u16ChgDeltaRelease = 100;
	gmSystemParemater.mRomPar.mBalance.u16DhgSet = 50000;
	gmSystemParemater.mRomPar.mBalance.u16DhgRelease = 50000;
	gmSystemParemater.mRomPar.mBalance.u16DhgDeltaSet = 50000;
	gmSystemParemater.mRomPar.mBalance.u16DhgDeltaRelease = 50000;

	gmSystemParemater.mRomPar.mBalance.u16RlxSet = 36000;
	gmSystemParemater.mRomPar.mBalance.u16RlxRelease = 36000;
	gmSystemParemater.mRomPar.mBalance.u16RlxDeltaSet = 100;
	gmSystemParemater.mRomPar.mBalance.u16RlxDeltaRelease = 50;
}
static void setDefaultOcvTable(void)
{
	uint8_t		i;
	tOcvRaTable	Ocv[]={
		{0,		26547},
		{3,		30493},
		{5,		31382},
		{7,		31929},
		{11,	32057},
		{13,	32085},
		{15,	32188},
		{19,	32381},
		{25,	32601},
		{30,	32743},
		{35,	32901},
		{40,	32918},
		{45,	32945},
		{50,	32950},
		{55,	32964},
		{60,	33002},
		{65,	33057},
		{71,	33358},
		{75,	33350},
		{80,	33353},
		{85,	33365},
		{90,	33371},
		{94,	33373},
		{98,	33384},
		{100,	33816},
		{255,	4000}};	
		
		
	
	for(i=0; i<API_SYS_PAR_MAX_OCV_TABLE_NUM; i++)
	{
		if(Ocv[i].u8Level == 255)
			break;
		gmSystemParemater.mRomPar.mOcvTable[i].u8Level = Ocv[i].u8Level;
		gmSystemParemater.mRomPar.mOcvTable[i].u16Value = Ocv[i].u16Value;
	}

}
static void setDefaultRaTable(void)
{
	uint8_t		i;
	tOcvRaTable	Ra[]={
		{0,		234},
		{4,		182},
		{6,		171},
		{8,		157},
		{10,	157},
		{12,	159},
		{15,	159},
		{20,	158},
		{25,	157},
		{30,	156},
		{35,	155},
		{40,	156},
		{45,	157},
		{50,	158},
		{55,	158},
		{60,	160},
		{65,	165},
		{70,	157},
		{75,	157},
		{80,	157},
		{85,	157},
		{90,	157},
		{94,	158},
		{98,	156},
		{100,	154},
		{255,	255}};
		
	for(i=0; i<API_SYS_PAR_MAX_RA_TABLE_NUM; i++)
	{
		if(Ra[i].u8Level == 255)
			break;
		gmSystemParemater.mRomPar.mRaTable[i].u8Level = Ra[i].u8Level;
		gmSystemParemater.mRomPar.mRaTable[i].u16Value = Ra[i].u16Value;
	}
}

static void setDefaultOvpPfValue(void)
{
	gmSystemParemater.mRomPar.mOvpPf.u16SetValue = 45000;
	gmSystemParemater.mRomPar.mOvpPf.u8SetTime = 40;
}

static void setDefaultUvpPfValue(void)
{
	gmSystemParemater.mRomPar.mUvpPf.u16SetValue = 15000;
	gmSystemParemater.mRomPar.mUvpPf.u8SetTime = 40;
}

static void setDefaultScuOtValue(void)
{
	gmSystemParemater.mRomPar.mScuOt[0][0].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(85);
	gmSystemParemater.mRomPar.mScuOt[0][0].u8SetTime = 20;
	gmSystemParemater.mRomPar.mScuOt[0][0].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[0][0].u8ReleaseTime = 20;

	gmSystemParemater.mRomPar.mScuOt[0][1].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[0][1].u8SetTime = 0;
	gmSystemParemater.mRomPar.mScuOt[0][1].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(77);
	gmSystemParemater.mRomPar.mScuOt[0][1].u8ReleaseTime = 0;
	
	gmSystemParemater.mRomPar.mScuOt[0][2].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(90);
	gmSystemParemater.mRomPar.mScuOt[0][2].u8SetTime = 20;
	gmSystemParemater.mRomPar.mScuOt[0][2].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(87);
	gmSystemParemater.mRomPar.mScuOt[0][2].u8ReleaseTime = 20;

	gmSystemParemater.mRomPar.mScuOt[1][0].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(85);
	gmSystemParemater.mRomPar.mScuOt[1][0].u8SetTime = 20;
	gmSystemParemater.mRomPar.mScuOt[1][0].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[1][0].u8ReleaseTime = 20;
	
	gmSystemParemater.mRomPar.mScuOt[1][1].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[1][1].u8SetTime = 0;
	gmSystemParemater.mRomPar.mScuOt[1][1].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(77);
	gmSystemParemater.mRomPar.mScuOt[1][1].u8ReleaseTime = 0;
	
	gmSystemParemater.mRomPar.mScuOt[1][2].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(90);
	gmSystemParemater.mRomPar.mScuOt[1][2].u8SetTime = 20;
	gmSystemParemater.mRomPar.mScuOt[1][2].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(87);
	gmSystemParemater.mRomPar.mScuOt[1][2].u8ReleaseTime = 20;
	//----------------------------
	//	Abmi
	gmSystemParemater.mRomPar.mScuOt[2][0].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(70);
	gmSystemParemater.mRomPar.mScuOt[2][0].u8SetTime = 0;
	gmSystemParemater.mRomPar.mScuOt[2][0].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(67);
	gmSystemParemater.mRomPar.mScuOt[2][0].u8ReleaseTime = 0;
	
	gmSystemParemater.mRomPar.mScuOt[2][1].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[2][1].u8SetTime = 0;
	gmSystemParemater.mRomPar.mScuOt[2][1].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(77);
	gmSystemParemater.mRomPar.mScuOt[2][1].u8ReleaseTime = 0;
	
	gmSystemParemater.mRomPar.mScuOt[2][2].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(90);
	gmSystemParemater.mRomPar.mScuOt[2][2].u8SetTime = 6;
	gmSystemParemater.mRomPar.mScuOt[2][2].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(87);
	gmSystemParemater.mRomPar.mScuOt[2][2].u8ReleaseTime = 6;
	//----------------------------------
	//	BusBar
	gmSystemParemater.mRomPar.mScuOt[3][0].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(85);
	gmSystemParemater.mRomPar.mScuOt[3][0].u8SetTime = 20;
	gmSystemParemater.mRomPar.mScuOt[3][0].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[3][0].u8ReleaseTime = 20;
	
	gmSystemParemater.mRomPar.mScuOt[3][1].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[3][1].u8SetTime = 0;
	gmSystemParemater.mRomPar.mScuOt[3][1].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(77);
	gmSystemParemater.mRomPar.mScuOt[3][1].u8ReleaseTime = 0;
	
	gmSystemParemater.mRomPar.mScuOt[3][2].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(90);
	gmSystemParemater.mRomPar.mScuOt[3][2].u8SetTime = 20;
	gmSystemParemater.mRomPar.mScuOt[3][2].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(87);
	gmSystemParemater.mRomPar.mScuOt[3][2].u8ReleaseTime = 20;
	
	gmSystemParemater.mRomPar.mScuOt[4][0].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(85);
	gmSystemParemater.mRomPar.mScuOt[4][0].u8SetTime = 20;
	gmSystemParemater.mRomPar.mScuOt[4][0].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[4][0].u8ReleaseTime = 20;
	
	gmSystemParemater.mRomPar.mScuOt[4][1].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(80);
	gmSystemParemater.mRomPar.mScuOt[4][1].u8SetTime = 0;
	gmSystemParemater.mRomPar.mScuOt[4][1].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(77);
	gmSystemParemater.mRomPar.mScuOt[4][1].u8ReleaseTime = 0;
	
	gmSystemParemater.mRomPar.mScuOt[4][2].u16SetValue = 
				LIB_SYS_TEMP_TO_ADC(90);
	gmSystemParemater.mRomPar.mScuOt[4][2].u8SetTime = 20;
	gmSystemParemater.mRomPar.mScuOt[4][2].u16ReleaseValue = 
				LIB_SYS_TEMP_TO_ADC(87);
	gmSystemParemater.mRomPar.mScuOt[4][2].u8ReleaseTime = 20;
}


static void setDefaultSysSpecValue(void)
{
	gmSystemParemater.mRomPar.mMaxCurrent.u16ChgCurrent = 280;
	gmSystemParemater.mRomPar.mMaxCurrent.u16DsgCurrent = 280;
	gmSystemParemater.mRomPar.mMaxCurrent.u16ChgPeakCurrent = 300;
	gmSystemParemater.mRomPar.mMaxCurrent.u16DsgPeakCurrent = 300;
	gmSystemParemater.mRomPar.mMaxCurrent.u8ChgPeakSecond = 5;
	gmSystemParemater.mRomPar.mMaxCurrent.u8DsgPeakSecond = 5;
	
	gmSystemParemater.mRomPar.mRate.u16Voltage = 1000;
	gmSystemParemater.mRomPar.mRate.u16MaxVoltage = 800;
	gmSystemParemater.mRomPar.mRate.u16MinVoltage = 1300;
}

static void setDefaultSysBMUPassiveBalRVal(void){
	gmSystemParemater.mRomPar.u16BmuPassiveBalRVal = 15000;
}


static void LibSysParSetDefaultRomValue(void)
{
	uint8_t	i;
	
	gmSystemParemater.mRomPar.u32HwVersion = 0x0204;
	gmSystemParemater.mRomPar.u16ZeroCurrent = 300;
	gmSystemParemater.mRomPar.u16MinChargeDischargeCurrent = 500;
	gmSystemParemater.mRomPar.u16MinFlatVoltage = 32420;
	gmSystemParemater.mRomPar.u16MaxFlatVoltage = 33300;
	gmSystemParemater.mRomPar.u16TerminateVoltage = 26000;
	gmSystemParemater.mRomPar.mOtHwSetValue.u16SetValue = (40+60);
	gmSystemParemater.mRomPar.mOtHwSetValue.u8SetTime = 0;
	gmSystemParemater.mRomPar.mUtHwSetValue.u16SetValue = (40-10);
	gmSystemParemater.mRomPar.mUtHwSetValue.u8SetTime = 0;
	gmSystemParemater.mRomPar.mOvpHwSetValue.u16SetValue = 3800;
	gmSystemParemater.mRomPar.mOvpHwSetValue.u8SetTime = 0;
	gmSystemParemater.mRomPar.mUvpHwSetValue.u16SetValue = 2500;
	gmSystemParemater.mRomPar.mUvpHwSetValue.u8SetTime = 0;
	gmSystemParemater.mRomPar.mFullCharge.u16Current = 14000;
	gmSystemParemater.mRomPar.mFullCharge.u16Voltage = 35500;
	gmSystemParemater.mRomPar.mFullCharge.u8Time = 20;
	
	gmSystemParemater.mRomPar.u16RelayOnThreshold = 100;
	gmSystemParemater.mRomPar.u16PreDischargeTime = 30;
	gmSystemParemater.mRomPar.u16PreDischargeThreshold = 0;
	
	gmSystemParemater.mRomPar.mAfeComm.u8L1Time = 60;
	gmSystemParemater.mRomPar.mAfeComm.u8L2Time = 90;
	
	gmSystemParemater.mRomPar.u32DesignedCapacity = 280000L;
	gmSystemParemater.mRomPar.u8TotalAfeNumber = 8;
	gmSystemParemater.mRomPar.u32SystemActiveFlag = 7;
	
	for(i=0; i<MAX_BMU_NUM; i++)
	{
		gmSystemParemater.mRomPar.u32CellFlag[i] = 0x03ffff;
		gmSystemParemater.mRomPar.u32NtcFlag[i] = 0x00000f;	
		gmSystemParemater.mRomPar.u32CellBusBarFlag[i] = 0;
		gmSystemParemater.mRomPar.u32NtcAmbientFlag[i] = 0;
		gmSystemParemater.mRomPar.u32NtcBusBarFlag[i] = 0;
		gmSystemParemater.mRomPar.u32NtcOtherFlag[i] = 0;
	}
	
	gmSystemParemater.mRomPar.mLineLoss[0].u16Channel = 0x0107;
	gmSystemParemater.mRomPar.mLineLoss[0].u32RValue = 92;
	gmSystemParemater.mRomPar.mLineLoss[1].u16Channel = 0x010D;
	gmSystemParemater.mRomPar.mLineLoss[1].u32RValue = 92;
	
	setDefaultOvpValue();
	setDefaultOvpPfValue();

	setDefaultUvpValue();
	setDefaultUvpPfValue();
	
	setDefaultDvpValue();
	setDefaultDtpValue();
	setDefaultCotpValue();
    setDefaultCutpValue();
	setDefaultDotpValue();
	setDefaultDutpValue();
	setDefaultCocpValue();
	setDefaultDocpValue();
	setDefaultBalanceValue();
	setDefaultOcvTable();
	setDefaultRaTable();	
	
	setDefaultScuOtValue();
	setDefaultSysSpecValue();
	setDefaultSysBMUPassiveBalRVal();
}

static void updateCellNumber(void)
{
	uint32_t	flag;
	uint32_t	BusBarFlag;
	uint8_t		bmuindex,b;
	
	gmSystemParemater.mRamPar.u16CellNumber = 0;
	gmSystemParemater.mRamPar.u8CellBusbarNumber = 0;
	
	for(bmuindex=0; bmuindex < gmSystemParemater.mRomPar.u8TotalAfeNumber;  bmuindex++)
	{
		flag = gmSystemParemater.mRomPar.u32CellFlag[bmuindex];
		BusBarFlag = LibSysParGetCellBusBarFlag(bmuindex);

		for(b=0; b<32; b++)
		{
			if(flag & 0x01)
				gmSystemParemater.mRamPar.u16CellNumber++;
			else if(BusBarFlag & 0x01)
				gmSystemParemater.mRamPar.u8CellBusbarNumber++;
			flag >>= 1;
			BusBarFlag >>= 1;
		}
	}	
	if(gmSystemParemater.mRamPar.u16CellNumber >= MAX_CELL_NUMBER)
		gmSystemParemater.mRamPar.u16CellNumber = MAX_CELL_NUMBER;
	if(gmSystemParemater.mRamPar.u8CellBusbarNumber >= MAX_CELL_BUSBAR_NUMBER)
		gmSystemParemater.mRamPar.u8CellBusbarNumber = MAX_CELL_BUSBAR_NUMBER;
		
}
static void updateNtcNumber(void)
{
	uint32_t	flag;
	uint32_t	ambientFlag;
	uint32_t	busBarFlag;
	uint32_t	otherFlag;

	uint8_t		bmuindex,b;
	
	gmSystemParemater.mRamPar.u16NtcNumber = 0;
	gmSystemParemater.mRamPar.u8NtcBusbarNumber = 0;
	gmSystemParemater.mRamPar.u8NtcAmbientNumber = 0;
	gmSystemParemater.mRamPar.u8NtcOtherNumber = 0;

	for(bmuindex=0; bmuindex<gmSystemParemater.mRomPar.u8TotalAfeNumber;  bmuindex++)
	{
		flag = gmSystemParemater.mRomPar.u32NtcFlag[bmuindex];
		ambientFlag = LibSysParGetNtcAmbientFlag(bmuindex);
		busBarFlag = LibSysParGetNtcBusBarFlag(bmuindex);
		otherFlag = LibSysParGetNtcOtherFlag(bmuindex);
		for(b=0; b<32; b++)
		{
			if(flag & 0x01)			
				gmSystemParemater.mRamPar.u16NtcNumber++;	
			else if(ambientFlag & 0x01)
				gmSystemParemater.mRamPar.u8NtcAmbientNumber++;
			else if(busBarFlag & 0x01)
				gmSystemParemater.mRamPar.u8NtcBusbarNumber++;				
			else if(otherFlag & 0x01)
				gmSystemParemater.mRamPar.u8NtcOtherNumber++;

			flag >>= 1;
			ambientFlag >>= 1;
			busBarFlag >>= 1;
			otherFlag >>= 1;
		}
	}
	if(gmSystemParemater.mRamPar.u16NtcNumber >= MAX_NTC_NUMBER)
		gmSystemParemater.mRamPar.u16NtcNumber = MAX_NTC_NUMBER;
	if(gmSystemParemater.mRamPar.u8NtcBusbarNumber >= MAX_NTC_BUSBAR_NUMBER)
		gmSystemParemater.mRamPar.u8NtcBusbarNumber = MAX_NTC_BUSBAR_NUMBER;
	if(gmSystemParemater.mRamPar.u8NtcAmbientNumber >= MAX_NTC_AMBIENT_NUMBER)
		gmSystemParemater.mRamPar.u8NtcAmbientNumber = MAX_NTC_AMBIENT_NUMBER;
	if(gmSystemParemater.mRamPar.u8NtcOtherNumber >= MAX_NTC_OTHER_NUMBER)
		gmSystemParemater.mRamPar.u8NtcOtherNumber = MAX_NTC_OTHER_NUMBER;
}

static void updateCellLogicIndexOfModule(void){
	uint8_t afe_index  = 0;
//	uint16_t cellCnt = 0 ;
	
	gmSystemParemater.mRamPar.u16CellLogicIndexOffset[0] = 0;
	
	for(afe_index = 1; afe_index <= gmSystemParemater.mRomPar.u8TotalAfeNumber; afe_index++){
		gmSystemParemater.mRamPar.u16CellLogicIndexOffset[afe_index]
			= gmSystemParemater.mRamPar.u16CellLogicIndexOffset[afe_index-1] + 
			  gmSystemParemater.mRamPar.u8CellNumberOfAfe[afe_index - 1];
	}

}

static void LibSysParCheckValidRomValue(void){
	
	if(gmSystemParemater.mRomPar.u8TotalAfeNumber > MAX_BMU_NUM){
		gmSystemParemater.mRomPar.u8TotalAfeNumber = MAX_BMU_NUM;
	}

}

static void LibSysParSetRamValue(void)
{
    char    str[100];
    sprintf(str,"Cotp Temp %d %d %d", 
            gmSystemParemater.mRomPar.mCotp[0].u16SetValue,
            gmSystemParemater.mRomPar.mCotp[1].u16SetValue,
            gmSystemParemater.mRomPar.mCotp[2].u16SetValue);
    AppSerialUartSendMessage(str);
    
	gmSystemParemater.mRamPar.mCotp[0].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCotp[0].u16SetValue);
	gmSystemParemater.mRamPar.mCotp[0].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCotp[0].u16ReleaseValue);
	gmSystemParemater.mRamPar.mCotp[1].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCotp[1].u16SetValue);
	gmSystemParemater.mRamPar.mCotp[1].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCotp[1].u16ReleaseValue);
	gmSystemParemater.mRamPar.mCotp[2].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCotp[2].u16SetValue);
	gmSystemParemater.mRamPar.mCotp[2].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCotp[2].u16ReleaseValue);

    sprintf(str,"Cotp TempAdc %d %d %d", 
            gmSystemParemater.mRamPar.mCotp[0].u16SetAdcValue,
            gmSystemParemater.mRamPar.mCotp[1].u16SetAdcValue,
            gmSystemParemater.mRamPar.mCotp[2].u16SetAdcValue);
    AppSerialUartSendMessage(str);



	gmSystemParemater.mRamPar.mCutp[0].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCutp[0].u16SetValue);
	gmSystemParemater.mRamPar.mCutp[0].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCutp[0].u16ReleaseValue);
	gmSystemParemater.mRamPar.mCutp[1].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCutp[1].u16SetValue);
	gmSystemParemater.mRamPar.mCutp[1].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCutp[1].u16ReleaseValue);
	gmSystemParemater.mRamPar.mCutp[2].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCutp[2].u16SetValue);
	gmSystemParemater.mRamPar.mCutp[2].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mCutp[2].u16ReleaseValue);

	gmSystemParemater.mRamPar.mDotp[0].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDotp[0].u16SetValue);
	gmSystemParemater.mRamPar.mDotp[0].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDotp[0].u16ReleaseValue);
	gmSystemParemater.mRamPar.mDotp[1].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDotp[1].u16SetValue);
	gmSystemParemater.mRamPar.mDotp[1].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDotp[1].u16ReleaseValue);
	gmSystemParemater.mRamPar.mDotp[2].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDotp[2].u16SetValue);
	gmSystemParemater.mRamPar.mDotp[2].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDotp[2].u16ReleaseValue);

	gmSystemParemater.mRamPar.mDutp[0].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDutp[0].u16SetValue);
	gmSystemParemater.mRamPar.mDutp[0].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDutp[0].u16ReleaseValue);
	gmSystemParemater.mRamPar.mDutp[1].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDutp[1].u16SetValue);
	gmSystemParemater.mRamPar.mDutp[1].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDutp[1].u16ReleaseValue);
	gmSystemParemater.mRamPar.mDutp[2].u16SetAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDutp[2].u16SetValue);
	gmSystemParemater.mRamPar.mDutp[2].u16ReleaseAdcValue = 
				LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mDutp[2].u16ReleaseValue);

	gmSystemParemater.mRamPar.mBalance.u16TempSetAdcValue = 
					LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mBalance.u8TempSet);
	gmSystemParemater.mRamPar.mBalance.u16TempReleaseAdcValue = 
					LIB_SYS_TEMP_TO_VOL(gmSystemParemater.mRomPar.mBalance.u8TempRelease);
	
	updateCellNumber();
	updateNtcNumber();
	updateCellLogicIndexOfModule();
}

/* Public function prototypes -----------------------------------------------*/
void LibSysParOvpPfClean(void)
{
	gmBatteryInfo.u16PfFlag &= ~BATINFO_OVP_PF_FLAG;
	resetBatteryInfoIdleCount();
}
void LibSysParOvpPfSet(void)
{
	gmBatteryInfo.u16PfFlag |= BATINFO_OVP_PF_FLAG;
	resetBatteryInfoIdleCount();
}
void LibSysParUvpPfClean(void)
{
	gmBatteryInfo.u16PfFlag &= ~BATINFO_UVP_PF_FLAG;
	resetBatteryInfoIdleCount();
}
void LibSysParUvpPfSet(void)
{
	gmBatteryInfo.u16PfFlag |= BATINFO_UVP_PF_FLAG;
	resetBatteryInfoIdleCount();
}

void LibSysParCDvpPfClean(void)
{
	gmBatteryInfo.u16PfFlag &= ~BATINFO_CDVP_PF_FLAG;
	resetBatteryInfoIdleCount();
}
void LibSysParCDvpPfSet(void)
{
	gmBatteryInfo.u16PfFlag |= BATINFO_CDVP_PF_FLAG;
	resetBatteryInfoIdleCount();
}

uint8_t LibSysParIsOvpPfSet(void)
{
	if(gmBatteryInfo.u16PfFlag & BATINFO_OVP_PF_FLAG)
		return 1;
	return 0;
}

uint8_t LibSysParIsUvpPfSet(void)
{
	if(gmBatteryInfo.u16PfFlag & BATINFO_UVP_PF_FLAG)
		return 1;
	return 0;
}

uint8_t LibSysParIsCDvpPfSet(void)
{
	if(gmBatteryInfo.u16PfFlag & BATINFO_CDVP_PF_FLAG)
		return 1;
	return 0;
}

uint32_t LibSysParGetQmax(void)
{
	return gmBatteryInfo.u32Qmax;
}

void LibSysParSetQmax(uint32_t Qmax)
{
	char	str[100];
	
	sprintf(str,"LibSysParSetQmax %d", Qmax);
	LibSysParDebugMsg(str);
	
	gmBatteryInfo.u32Qmax = Qmax;
	resetBatteryInfoIdleCount();
}

uint16_t LibSysParGetQmaxUpdateTimes(void)
{
	return gmBatteryInfo.u16QmaxUpdateTimes;
}

void LibSysParSetQmaxUpdateTimes(uint16_t times)
{
	LibSysParDebugMsg("LibSysParSetQmaxUpdateTimes");
	gmBatteryInfo.u16QmaxUpdateTimes = times;
	resetBatteryInfoIdleCount();
}
uint16_t LibSysParGetCycleCount(void)
{
	return gmBatteryInfo.u16CycleCount;
}

void LibSysParSetCycleCount(uint16_t count)
{
	LibSysParDebugMsg("LibSysParSetCycleCount");
	gmBatteryInfo.u16CycleCount = count;
	resetBatteryInfoIdleCount();
}

uint16_t LibSysParGetPfFlag(void)
{
	return gmBatteryInfo.u16PfFlag;
}

void LibSysParSetPfFlag(uint16_t flag)
{
	LibSysParDebugMsg("LibSysParSetPfFlag");

	gmBatteryInfo.u16PfFlag = flag;
	resetBatteryInfoIdleCount();
}

int32_t appCurrDebug(uint8_t CurrentIndex, int32_t adc)
{
	return 0;
}
int32_t appVbatDebug(uint8_t VbatIndex, int32_t adc)
{
	return 0;
}

void apiCaliParSetCurrentValue(uint8_t CurrentIndex, uint8_t PointIndex,int32_t Value, int32_t Adc)
{
	
}
void apiCaliParGetCurrentValue(uint8_t CurrentIndex, uint8_t PointIndex,int32_t *Value, int32_t *Adc)
{
	
}

void apiCaliParSetVbatValue(uint8_t VbatIndex, uint8_t PointIndex, int32_t Value, int32_t Adc)
{
	
}
void apiCaliParGetVbatValue(uint8_t VbatIndex, uint8_t PointIndex,int32_t *Value, int32_t *Adc)
{
	
}

uint32_t apiCaliParGetChecksum(void)
{
	return 0;
}

//System parameter

//-----------------------------------------------------------
uint32_t LibSysParGetHwVersion(void)
{
	return gmSystemParemater.mRomPar.u32HwVersion;
}
void LibSysParSetHwVersion(uint32_t u32Version)
{
	gmSystemParemater.mRomPar.u32HwVersion = u32Version;
	resetSysParIdleCount();
}

uint8_t LibSysParGetTotalAfeNumber(void)
{
	return gmSystemParemater.mRomPar.u8TotalAfeNumber;	
}
void LibSysParSetAfeNumber(uint8_t u8TotalAfes)
{
	gmSystemParemater.mRomPar.u8TotalAfeNumber = u8TotalAfes;
	resetSysParIdleCount();
}

uint32_t LibSysParGetCellFlag(uint8_t u8BmuIndex)
{
	if(u8BmuIndex >= MAX_BMU_NUM)
		return 0;
	return gmSystemParemater.mRomPar.u32CellFlag[u8BmuIndex];	
}
void LibSysParSetCellFlag(uint8_t BmuIndex,uint32_t CellFlag)
{
	if(BmuIndex >= MAX_BMU_NUM)
		return;
	gmSystemParemater.mRomPar.u32CellFlag[BmuIndex] = CellFlag;
	resetSysParIdleCount();
}

uint32_t LibSysParGetNtcFlag(uint8_t BmuIndex)
{
	if(BmuIndex >= MAX_BMU_NUM)
		return 0;
	return gmSystemParemater.mRomPar.u32NtcFlag[BmuIndex];	
}

void LibSysParSetNtcFlag(uint8_t BmuIndex, uint32_t NtcFlag)
{
	if(BmuIndex >= MAX_BMU_NUM)
		return;
	gmSystemParemater.mRomPar.u32NtcFlag[BmuIndex] = NtcFlag;
	resetSysParIdleCount();
}

uint16_t LibSysParGetZeroCurrentValue(void)
{
	return gmSystemParemater.mRomPar.u16ZeroCurrent;	
}
void LibSysParSetZeroCurrentValue(uint16_t current)
{
	gmSystemParemater.mRomPar.u16ZeroCurrent = current;
	resetSysParIdleCount();
}

uint16_t LibSysParGetMinChargeCurrentValue(void)
{
	return gmSystemParemater.mRomPar.u16MinChargeDischargeCurrent;	
}

void LibSysParSetMinChargeCurrentValue(uint16_t current)
{
	gmSystemParemater.mRomPar.u16MinChargeDischargeCurrent = current;	
	resetSysParIdleCount();
}

uint32_t LibSysParGetDesignedCapacity(void)
{
	return gmSystemParemater.mRomPar.u32DesignedCapacity;
}

void LibSysParSetDesignedCapacity(uint32_t dc)
{
	gmSystemParemater.mRomPar.u32DesignedCapacity = dc;
	resetSysParIdleCount();
}

uint32_t LibSysParGetSystemActiveFlag(void)
{
	return gmSystemParemater.mRomPar.u32SystemActiveFlag;
}
void LibSysParSetSystemActiveFlag(uint32_t u32Flag)
{
	gmSystemParemater.mRomPar.u32SystemActiveFlag = u32Flag;
	resetSysParIdleCount();
}

void LibSysParGetMaxCurrentValue(tSysCurrentPar *pmPar)
{
	pmPar->mChgCurrent.u32Value = gmSystemParemater.mRomPar.mMaxCurrent.u16ChgCurrent;
	pmPar->mDsgCurrent.u32Value = gmSystemParemater.mRomPar.mMaxCurrent.u16DsgCurrent;
}
void LibSysParSetMaxCurrentValue(tSysCurrentPar *pmPar)
{
	gmSystemParemater.mRomPar.mMaxCurrent.u16ChgCurrent = pmPar->mChgCurrent.u32Value;
	gmSystemParemater.mRomPar.mMaxCurrent.u16DsgCurrent = pmPar->mDsgCurrent.u32Value;
	resetSysParIdleCount();
}

void LibSysParGetMaxPeakCurrentValue(tSysPeakCurrentPar *pmPar)
{																																																
	pmPar->mChgPeakCurrent.u32Value = gmSystemParemater.mRomPar.mMaxCurrent.u16ChgPeakCurrent;
	pmPar->mChgPeakSecond.u32Value = gmSystemParemater.mRomPar.mMaxCurrent.u8ChgPeakSecond;
	pmPar->mDsgPeakCurrent.u32Value = gmSystemParemater.mRomPar.mMaxCurrent.u16DsgPeakCurrent;
	pmPar->mDsgPeakSecond.u32Value = gmSystemParemater.mRomPar.mMaxCurrent.u8DsgPeakSecond;
}
void LibSysParSetMaxPeakCurrentValue(tSysPeakCurrentPar *pmPar)
{
	gmSystemParemater.mRomPar.mMaxCurrent.u16ChgPeakCurrent = pmPar->mChgPeakCurrent.u32Value;
	gmSystemParemater.mRomPar.mMaxCurrent.u8ChgPeakSecond = pmPar->mChgPeakSecond.u32Value;
	gmSystemParemater.mRomPar.mMaxCurrent.u16DsgPeakCurrent = pmPar->mDsgPeakCurrent.u32Value;
	gmSystemParemater.mRomPar.mMaxCurrent.u8DsgPeakSecond = pmPar->mDsgPeakSecond.u32Value;
	resetSysParIdleCount();
}

void LibSysParGetRateVoltage(tSysRateVoltagePar *pmPar)
{
	pmPar->mVoltage.u32Value = gmSystemParemater.mRomPar.mRate.u16Voltage;
	pmPar->mMaxVoltage.u32Value = gmSystemParemater.mRomPar.mRate.u16MaxVoltage;
	pmPar->mMinVoltage.u32Value = gmSystemParemater.mRomPar.mRate.u16MinVoltage;
}
void LibSysParSetRateVoltage(tSysRateVoltagePar *pmPar)
{
	gmSystemParemater.mRomPar.mRate.u16Voltage = pmPar->mVoltage.u32Value;
	gmSystemParemater.mRomPar.mRate.u16MaxVoltage = pmPar->mMaxVoltage.u32Value;
	gmSystemParemater.mRomPar.mRate.u16MinVoltage = pmPar->mMinVoltage.u32Value;
	resetSysParIdleCount();
}
uint16_t LibSysParGetMinFlatVoltage(void)
{
	return gmSystemParemater.mRomPar.u16MinFlatVoltage;
}

uint16_t LibSysParGetMaxFlatVoltage(void)
{
	return gmSystemParemater.mRomPar.u16MaxFlatVoltage;
}
void LibSysParGetFlatVoltage(tSysFloatVoltagePar *pmPar)
{
	pmPar->mMinFloatVoltage.u32Value = gmSystemParemater.mRomPar.u16MinFlatVoltage;
	pmPar->mMaxFloatVoltage.u32Value = gmSystemParemater.mRomPar.u16MaxFlatVoltage;
}

void LibSysParSetFlatVoltage(tSysFloatVoltagePar *pmPar)
{
	gmSystemParemater.mRomPar.u16MinFlatVoltage = pmPar->mMinFloatVoltage.u32Value;
	gmSystemParemater.mRomPar.u16MaxFlatVoltage = pmPar->mMaxFloatVoltage.u32Value;
	resetSysParIdleCount();
}

void LibSysParGetFullChargeCondition(tSysFullChargePar *pmPar)
{
	pmPar->mCurrent.u32Value = gmSystemParemater.mRomPar.mFullCharge.u16Current;
	pmPar->mVoltage.u32Value = gmSystemParemater.mRomPar.mFullCharge.u16Voltage;
	pmPar->mTime.u32Value = gmSystemParemater.mRomPar.mFullCharge.u8Time;
}
void LibSysParSetFullChargeCondition(tSysFullChargePar *pmPar)
{
	gmSystemParemater.mRomPar.mFullCharge.u16Current = pmPar->mCurrent.u32Value;
	gmSystemParemater.mRomPar.mFullCharge.u16Voltage = pmPar->mVoltage.u32Value;
	gmSystemParemater.mRomPar.mFullCharge.u8Time = pmPar->mTime.u32Value;
	resetSysParIdleCount();
}	
uint16_t LibSysParGetTerminateVoltage(void)
{
	return gmSystemParemater.mRomPar.u16TerminateVoltage;
}
void LibSysParSetTerminateVoltage(uint16_t u16Voltage)
{
	gmSystemParemater.mRomPar.u16TerminateVoltage = u16Voltage;
	resetSysParIdleCount();
}

void LibSysParGetAfeCommTime(tSysAfeCommTimePar *pmPar)
{
	pmPar->mL1Time.u32Value = gmSystemParemater.mRomPar.mAfeComm.u8L1Time;
	pmPar->mL2Time.u32Value = gmSystemParemater.mRomPar.mAfeComm.u8L2Time;
}

uint8_t LibSysParGetAfeCommL1Time(void)
{
	return gmSystemParemater.mRomPar.mAfeComm.u8L1Time;
}
uint8_t LibSysParGetAfeCommL2Time(void)
{
	return gmSystemParemater.mRomPar.mAfeComm.u8L2Time;
}

void LibSysParSetAfeCommTime(tSysAfeCommTimePar *pmPar)
{
	gmSystemParemater.mRomPar.mAfeComm.u8L1Time = pmPar->mL1Time.u32Value;
	gmSystemParemater.mRomPar.mAfeComm.u8L2Time = pmPar->mL2Time.u32Value;
	resetSysParIdleCount();
}

uint16_t LibSysParGetPreDischargeTime(void)
{
	return gmSystemParemater.mRomPar.u16PreDischargeTime;
}

void LibSysParSetPreDischargeTime(uint16_t u16Time)
{
	gmSystemParemater.mRomPar.u16PreDischargeTime = u16Time;
	resetSysParIdleCount();
}

uint16_t LibSysParGetPreDischargeThreshold(void)
{
	return gmSystemParemater.mRomPar.u16PreDischargeThreshold;
}

void LibSysParSetPreDischargeThreshold(uint16_t u16Voltage)
{
	gmSystemParemater.mRomPar.u16PreDischargeThreshold = u16Voltage;
	resetSysParIdleCount();
}


uint16_t LibSysParGetRelayOnDiffVoltage(void)
{
	return gmSystemParemater.mRomPar.u16RelayOnThreshold;
}

void LibSysParSetRelayOnDiffVoltage(uint16_t u16Voltage)
{
	gmSystemParemater.mRomPar.u16RelayOnThreshold = u16Voltage;
	resetSysParIdleCount();
}


uint16_t LibSysParGetCellNumber(void)
{
	return gmSystemParemater.mRamPar.u16CellNumber;
}
uint8_t LibSysParGetCellBusbarNumber(void)
{
	return gmSystemParemater.mRamPar.u8CellBusbarNumber;
}

uint16_t LibSysParGetNtcNumber(void)
{
	return gmSystemParemater.mRamPar.u16NtcNumber;
}

uint8_t LibSysParGetNtcBusbarNumber(void)
{
	return gmSystemParemater.mRamPar.u8NtcBusbarNumber;
}

uint8_t LibSysParGetNtcAmbientNumber(void)
{
	return gmSystemParemater.mRamPar.u8NtcAmbientNumber;
}

uint8_t LibSysParGetNtcOtherNumber(void)
{
	return gmSystemParemater.mRamPar.u8NtcOtherNumber;
}

uint16_t LibSysParGetLogicOffset(uint8_t u8AfeIndex){

	return  gmSystemParemater.mRamPar.u16CellLogicIndexOffset[u8AfeIndex];
}

//---------------------------------------
void LibSysParGetOcvTable(uint8_t u8Index ,tOcvRaTable *pmOcvTable)
{
	if(u8Index >= API_SYS_PAR_MAX_OCV_TABLE_NUM)
		return;
	pmOcvTable->u8Level =  gmSystemParemater.mRomPar.mOcvTable[u8Index].u8Level;
	pmOcvTable->u16Value =  gmSystemParemater.mRomPar.mOcvTable[u8Index].u16Value;
}

void LibSysParGetRaTable(uint8_t u8Index ,tOcvRaTable *pmOcvTable)
{
	if(u8Index >= API_SYS_PAR_MAX_RA_TABLE_NUM)
		return;
	pmOcvTable->u8Level =  gmSystemParemater.mRomPar.mRaTable[u8Index].u8Level;
	pmOcvTable->u16Value =  gmSystemParemater.mRomPar.mRaTable[u8Index].u16Value;
}

void LibSysParSetOcvTable(uint8_t u8Index ,tOcvRaTable *pmOcvTable)
{
	if(u8Index >= API_SYS_PAR_MAX_OCV_TABLE_NUM)
		return;
	gmSystemParemater.mRomPar.mOcvTable[u8Index].u8Level = pmOcvTable->u8Level;
	gmSystemParemater.mRomPar.mOcvTable[u8Index].u16Value = pmOcvTable->u16Value;
	resetSysParIdleCount();
}

void LibSysParSetRaTable(uint8_t u8Index ,tOcvRaTable *pmOcvTable)
{
	if(u8Index >= API_SYS_PAR_MAX_RA_TABLE_NUM)
		return;		
	gmSystemParemater.mRomPar.mRaTable[u8Index].u8Level = pmOcvTable->u8Level;
	gmSystemParemater.mRomPar.mRaTable[u8Index].u16Value = pmOcvTable->u16Value;
	resetSysParIdleCount();
}

//---------------------------------------
//Protect parameter
//OVP
void LibSysParGetOvpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		if(u8Level == 0x10)
		{
			pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mOvpHwSetValue.u16SetValue;
			pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mOvpHwSetValue.u8SetTime;
		}
		else
		{
			pmPar->mSetValue.u32Value = 0;
			pmPar->mSTime.u32Value = 0;
			pmPar->mRelValue.u32Value = 0;
			pmPar->mRTime.u32Value = 0;
		}
		return;
	}
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mOvp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mOvp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mOvp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mOvp[u8Level].u8ReleaseTime;
}

void LibSysParSetOvpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{	
	if(u8Level >= 3)
	{
		if(u8Level == 0x10)
		{
			gmSystemParemater.mRomPar.mOvpHwSetValue.u16SetValue = pmPar->mSetValue.u32Value;
			gmSystemParemater.mRomPar.mOvpHwSetValue.u8SetTime = pmPar->mSTime.u32Value;
		}
		return;
	}
	gmSystemParemater.mRomPar.mOvp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mOvp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mOvp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mOvp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;
	resetSysParIdleCount();
}

// UVP

void LibSysParGetUvpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		if(u8Level == 0x10)
		{
			pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mUvpHwSetValue.u16SetValue;		
			pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mUvpHwSetValue.u8SetTime;		
		}
		else
		{
			pmPar->mSetValue.u32Value = 0;
			pmPar->mSTime.u32Value = 0;
			pmPar->mRelValue.u32Value = 0;
			pmPar->mRTime.u32Value = 0;
		}
		return;
	}
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mUvp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mUvp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mUvp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mUvp[u8Level].u8ReleaseTime;
}

void LibSysParSetUvpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{	
	if(u8Level >= 3)
	{
		if(u8Level == 0x10)
		{
			gmSystemParemater.mRomPar.mUvpHwSetValue.u16SetValue = pmPar->mSetValue.u32Value;
			gmSystemParemater.mRomPar.mUvpHwSetValue.u8SetTime = pmPar->mSTime.u32Value;
		}
		return;
	}
	gmSystemParemater.mRomPar.mUvp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mUvp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mUvp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mUvp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;
	resetSysParIdleCount();
}
//---------------------------------------------------------------
//	DVP
void LibSysParGetDvpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		pmPar->mSetValue.u32Value = 0;
		pmPar->mSTime.u32Value = 0;
		pmPar->mRelValue.u32Value = 0;
		pmPar->mRTime.u32Value = 0;
		return;
	}
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mDvp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mDvp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mDvp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mDvp[u8Level].u8ReleaseTime;
}

void LibSysParSetDvpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{	
	if(u8Level >= 3)
	{
		return;
	}
	gmSystemParemater.mRomPar.mDvp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mDvp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mDvp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mDvp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;
	;
	resetSysParIdleCount();
}

//---------------------------------------------------------------
void LibSysParGet2ndOtProtectPar(tScuProtectPar *pmPar)
{
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mOtHwSetValue.u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mOtHwSetValue.u8SetTime;
}
void LibSysParSet2ndOtProtectPar(tScuProtectPar *pmPar)
{
	gmSystemParemater.mRomPar.mOtHwSetValue.u16SetValue = pmPar->mSetValue.u32Value; 
	gmSystemParemater.mRomPar.mOtHwSetValue.u8SetTime = pmPar->mSTime.u32Value;
	resetSysParIdleCount();
}
void LibSysParGet2ndUtProtectPar(tScuProtectPar *pmPar)
{
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mUtHwSetValue.u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mUtHwSetValue.u8SetTime;
}
void LibSysParSet2ndUtProtectPar(tScuProtectPar *pmPar)
{
	gmSystemParemater.mRomPar.mUtHwSetValue.u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mUtHwSetValue.u8SetTime = pmPar->mSTime.u32Value;
	resetSysParIdleCount();
}

//---------------------------------------------------
//	Dotp
void LibSysParGetDotpProtectPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	pmPar->mSetValue.u32Value = gmSystemParemater.mRamPar.mDotp[u8Level].u16SetAdcValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mDotp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRamPar.mDotp[u8Level].u16ReleaseAdcValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mDotp[u8Level].u8ReleaseTime;
}


void LibSysParGetDotpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mDotp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mDotp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mDotp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mDotp[u8Level].u8ReleaseTime;
}
void LibSysParSetDotpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	gmSystemParemater.mRomPar.mDotp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mDotp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mDotp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mDotp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;	
	resetSysParIdleCount();
}
//----------- DUTP ---------------------------
void LibSysParGetDutpProtectPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	pmPar->mSetValue.u32Value = gmSystemParemater.mRamPar.mDutp[u8Level].u16SetAdcValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mDutp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRamPar.mDutp[u8Level].u16ReleaseAdcValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mDutp[u8Level].u8ReleaseTime;
}

void LibSysParGetDutpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mDutp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mDutp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mDutp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mDutp[u8Level].u8ReleaseTime;
}
void LibSysParSetDutpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	gmSystemParemater.mRomPar.mDutp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mDutp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mDutp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mDutp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;	
	resetSysParIdleCount();
}
//----------- DTP ---------------------------
void LibSysParGetDtpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mDtp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mDtp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mDtp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mDtp[u8Level].u8ReleaseTime;
}
void LibSysParSetDtpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	gmSystemParemater.mRomPar.mDtp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mDtp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mDtp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mDtp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;
	resetSysParIdleCount();
}

//------------------------------------------------------
//	Cotp
void LibSysParGetCotpProtectPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	pmPar->mSetValue.u32Value = gmSystemParemater.mRamPar.mCotp[u8Level].u16SetAdcValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mCotp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRamPar.mCotp[u8Level].u16ReleaseAdcValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mCotp[u8Level].u8ReleaseTime;
}

void LibSysParGetCotpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		if(u8Level == 0x10)
		{
			 pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mOtHwSetValue.u16SetValue;
			 pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mOtHwSetValue.u8SetTime;			
		}
		return;
	}
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mCotp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mCotp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mCotp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mCotp[u8Level].u8ReleaseTime;
}
void LibSysParSetCotpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		if(u8Level == 0x10)
		{
			gmSystemParemater.mRomPar.mOtHwSetValue.u16SetValue = pmPar->mSetValue.u32Value;
			gmSystemParemater.mRomPar.mOtHwSetValue.u8SetTime = pmPar->mSTime.u32Value;
		}
		return;
	}
	gmSystemParemater.mRomPar.mCotp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mCotp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mCotp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mCotp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;
	resetSysParIdleCount();
}
	
//-----------------------------------------------	
//	Cutp
void LibSysParGetCutpProtectPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		return;
	}
	pmPar->mSetValue.u32Value = gmSystemParemater.mRamPar.mCutp[u8Level].u16SetAdcValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mCutp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRamPar.mCutp[u8Level].u16ReleaseAdcValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mCutp[u8Level].u8ReleaseTime;
}
void LibSysParGetCutpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		if(u8Level == 0x10)
		{
			pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mUtHwSetValue.u16SetValue;
			pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mUtHwSetValue.u8SetTime;
		}
		return;
	}
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mCutp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mCutp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mCutp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mCutp[u8Level].u8ReleaseTime;
}
void LibSysParSetCutpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		if(u8Level == 0x10)
		{
			gmSystemParemater.mRomPar.mUtHwSetValue.u16SetValue = pmPar->mSetValue.u32Value;
			gmSystemParemater.mRomPar.mUtHwSetValue.u8SetTime = pmPar->mSTime.u32Value;			
		}
		return;
	}
	gmSystemParemater.mRomPar.mCutp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mCutp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mCutp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mCutp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;	
	resetSysParIdleCount();
}
//-----------------------------------------------------
void LibSysParSetScuOtPar(uint8_t u8Index, uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Index >= 5 || u8Level >= 3)
	{
		return;
	}
	gmSystemParemater.mRomPar.mScuOt[u8Index][u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mScuOt[u8Index][u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mScuOt[u8Index][u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mScuOt[u8Index][u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;	
	resetSysParIdleCount();
}

void LibSysParGetScuOtPar(uint8_t u8Index, uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Index >=5 || u8Level >= 3)
	{
		return;
	}
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mScuOt[u8Index][u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mScuOt[u8Index][u8Level].u8SetTime;
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mScuOt[u8Index][u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mScuOt[u8Index][u8Level].u8ReleaseTime;
	
}
//-----------------------------------------------------
//	Docp
void LibSysParGetDocpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mDocp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mDocp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mDocp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mDocp[u8Level].u8ReleaseTime;

}
void LibSysParSetDocpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	gmSystemParemater.mRomPar.mDocp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mDocp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mDocp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mDocp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;	
	resetSysParIdleCount();
}

//---------------------------------------------------
//Cocp
void LibSysParGetCocpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mCocp[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mCocp[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mCocp[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mCocp[u8Level].u8ReleaseTime;

}
void LibSysParSetCocpPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
		return;
	gmSystemParemater.mRomPar.mCocp[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mCocp[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mCocp[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mCocp[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;	
	resetSysParIdleCount();
}
//-----------------------------------------
//	PF
void LibSysParGetOvpPfPar(tScuProtectPar *pmPar)
{
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mOvpPf.u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mOvpPf.u8SetTime; 
}
void LibSysParSetOvpPfPar(tScuProtectPar *pmPar)
{
	gmSystemParemater.mRomPar.mOvpPf.u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mOvpPf.u8SetTime = pmPar->mSTime.u32Value;
	resetSysParIdleCount();
}

void LibSysParGetUvpPfPar(tScuProtectPar *pmPar)
{
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mUvpPf.u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mUvpPf.u8SetTime; 
}
void LibSysParSetUvpPfPar(tScuProtectPar *pmPar)
{
	gmSystemParemater.mRomPar.mUvpPf.u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mUvpPf.u8SetTime = pmPar->mSTime.u32Value;
	resetSysParIdleCount();
}
//------------------------------------------
void LibSysParGetBalanceDuty(tSysBalanceDutyPar *pmPar)
{
	pmPar->mSetDuty.u32Value = gmSystemParemater.mRomPar.mBalance.u8DutySet;
	pmPar->mSetDutyRest.u32Value = gmSystemParemater.mRomPar.mBalance.u8DutyRest;
	pmPar->mSetTemp.u32Value = gmSystemParemater.mRomPar.mBalance.u8TempSet;
	pmPar->mSetTempRealse.u32Value = gmSystemParemater.mRomPar.mBalance.u8TempRelease;
}
void LibSysParSetBalanceDuty(tSysBalanceDutyPar *pmPar)
{
	gmSystemParemater.mRomPar.mBalance.u8DutySet = pmPar->mSetDuty.u32Value;
	gmSystemParemater.mRomPar.mBalance.u8DutyRest = pmPar->mSetDutyRest.u32Value;
	gmSystemParemater.mRomPar.mBalance.u8TempSet = pmPar->mSetTemp.u32Value;
	gmSystemParemater.mRomPar.mBalance.u8TempRelease = pmPar->mSetTempRealse.u32Value;
	resetSysParIdleCount();
}
void LibSysParGetBalanceChg(tSysBalancePar *pmPar)
{
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mBalance.u16ChgSet;
	pmPar->mSetRealse.u32Value = gmSystemParemater.mRomPar.mBalance.u16ChgRelease;
	pmPar->mDeltaValue.u32Value = gmSystemParemater.mRomPar.mBalance.u16ChgDeltaSet;
	pmPar->mDeltaRealse.u32Value = gmSystemParemater.mRomPar.mBalance.u16ChgDeltaRelease;
}
void LibSysParSetBalanceChg(tSysBalancePar *pmPar)
{
	gmSystemParemater.mRomPar.mBalance.u16ChgSet = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16ChgRelease = pmPar->mSetRealse.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16ChgDeltaSet = pmPar->mDeltaValue.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16ChgDeltaRelease = pmPar->mDeltaRealse.u32Value;
	resetSysParIdleCount();
}

void LibSysParGetBalanceDhg(tSysBalancePar *pmPar)
{
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mBalance.u16DhgSet;
	pmPar->mSetRealse.u32Value = gmSystemParemater.mRomPar.mBalance.u16DhgRelease;
	pmPar->mDeltaValue.u32Value = gmSystemParemater.mRomPar.mBalance.u16DhgDeltaSet;
	pmPar->mDeltaRealse.u32Value = gmSystemParemater.mRomPar.mBalance.u16DhgDeltaRelease;
}
void LibSysParSetBalanceDhg(tSysBalancePar *pmPar)
{
	gmSystemParemater.mRomPar.mBalance.u16DhgSet = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16DhgRelease = pmPar->mSetRealse.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16DhgDeltaSet = pmPar->mDeltaValue.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16DhgDeltaRelease = pmPar->mDeltaRealse.u32Value;
	resetSysParIdleCount();
}
void LibSysParGetBalanceRlx(tSysBalancePar *pmPar)
{
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mBalance.u16RlxSet;
	pmPar->mSetRealse.u32Value = gmSystemParemater.mRomPar.mBalance.u16RlxRelease;
	pmPar->mDeltaValue.u32Value = gmSystemParemater.mRomPar.mBalance.u16RlxDeltaSet;
	pmPar->mDeltaRealse.u32Value = gmSystemParemater.mRomPar.mBalance.u16RlxDeltaRelease;
}
void LibSysParSetBalanceRlx(tSysBalancePar *pmPar)
{
	gmSystemParemater.mRomPar.mBalance.u16RlxSet = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16RlxRelease = pmPar->mSetRealse.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16RlxDeltaSet = pmPar->mDeltaValue.u32Value;
	gmSystemParemater.mRomPar.mBalance.u16RlxDeltaRelease = pmPar->mDeltaRealse.u32Value;
	resetSysParIdleCount();
}
//----------------------------------------------------------
//	Note Message
void LibSysParGetNotwMessageString(uint8_t *pu8Msg)
{
	uint8_t	i;
	for(i=0; i<MAX_NOTE_MESSAGE_STRING_ITEM; i++)
	{
		pu8Msg[i] = gmSystemParemater.mRomPar.u8NoteMessage[i];
		if(pu8Msg[i] == 0)
			break;
	}
}
void LibSysParSetNotwMessageString(uint8_t *pu8Msg)
{
	uint8_t	i;
	for(i=0; i<MAX_NOTE_MESSAGE_STRING_ITEM; i++)
	{
		gmSystemParemater.mRomPar.u8NoteMessage[i] = pu8Msg[i];
		if(pu8Msg[i] == 0)
			break;
	}
	resetSysParIdleCount();
}
uint32_t LibSysParGetChecksum(void)
{
	return gmSystemParemater.mRomPar.u32Checksum;
}


//----------------------------------------------------------------
void LibSysParGetLineLossPar(uint8_t u8BufIndex, uint16_t *pu16Channel, uint32_t *pu32RValue)
{
	if(u8BufIndex >= 4) 
		return;
	*pu16Channel = gmSystemParemater.mRomPar.mLineLoss[u8BufIndex].u16Channel;
	*pu32RValue = gmSystemParemater.mRomPar.mLineLoss[u8BufIndex].u32RValue;
}
void LibSysParSetLineLossPar(uint8_t u8BufIndex, uint16_t u16Channel, uint32_t u32RValue)
{
	if(u8BufIndex >= 4)
		return;
	gmSystemParemater.mRomPar.mLineLoss[u8BufIndex].u16Channel = u16Channel;
	gmSystemParemater.mRomPar.mLineLoss[u8BufIndex].u32RValue = u32RValue;
	resetSysParIdleCount();
}

void LibSysParGetDipPar(uint8_t u8Level, tScuProtectPar *pmPar)
{
	if(u8Level >= 3)
	{
		pmPar->mSetValue.u32Value = 0;
		pmPar->mSTime.u32Value = 0;
		pmPar->mRelValue.u32Value = 0;
		pmPar->mRTime.u32Value = 0;
		return;
	}
	pmPar->mSetValue.u32Value = gmSystemParemater.mRomPar.mDip[u8Level].u16SetValue;
	pmPar->mSTime.u32Value = gmSystemParemater.mRomPar.mDip[u8Level].u8SetTime; 
	pmPar->mRelValue.u32Value = gmSystemParemater.mRomPar.mDip[u8Level].u16ReleaseValue;
	pmPar->mRTime.u32Value = gmSystemParemater.mRomPar.mDip[u8Level].u8ReleaseTime;
}

void LibSysParSetDipPar(uint8_t u8Level, tScuProtectPar *pmPar)
{	
	if(u8Level >= 3)
	{
		return;
	}
	gmSystemParemater.mRomPar.mDip[u8Level].u16SetValue = pmPar->mSetValue.u32Value;
	gmSystemParemater.mRomPar.mDip[u8Level].u8SetTime = pmPar->mSTime.u32Value;
	gmSystemParemater.mRomPar.mDip[u8Level].u16ReleaseValue = pmPar->mRelValue.u32Value;
	gmSystemParemater.mRomPar.mDip[u8Level].u8ReleaseTime = pmPar->mRTime.u32Value;
	resetSysParIdleCount();
}

//----------------------------------------------------------------
uint32_t LibSysParGetCellBusBarFlag(uint8_t u8BmuIndex)
{
	if(u8BmuIndex >= MAX_BMU_NUM)
		return 0;
	return gmSystemParemater.mRomPar.u32CellBusBarFlag[u8BmuIndex];	
}
void LibSysParSetCellBusBarFlag(uint8_t u8BmuIndex,uint32_t u32Flag)
{
	if(u8BmuIndex >= MAX_BMU_NUM)
		return;
	gmSystemParemater.mRomPar.u32CellBusBarFlag[u8BmuIndex] = u32Flag;
	resetSysParIdleCount();
}

uint32_t LibSysParGetNtcAmbientFlag(uint8_t u8BmuIndex)
{
	if(u8BmuIndex >= MAX_BMU_NUM)
		return 0;
	return gmSystemParemater.mRomPar.u32NtcAmbientFlag[u8BmuIndex];	
}
void LibSysParSetNtcAmbientFlag(uint8_t u8BmuIndex,uint32_t u32Flag)
{
	if(u8BmuIndex >= MAX_BMU_NUM)
		return;
	gmSystemParemater.mRomPar.u32NtcAmbientFlag[u8BmuIndex] = u32Flag;
	resetSysParIdleCount();
}

uint32_t LibSysParGetNtcBusBarFlag(uint8_t u8BmuIndex)
{
	if(u8BmuIndex >= MAX_BMU_NUM)
		return 0;
	return gmSystemParemater.mRomPar.u32NtcBusBarFlag[u8BmuIndex];	
}
void LibSysParSetNtcBusBarFlag(uint8_t u8BmuIndex,uint32_t u32Flag)
{
	if(u8BmuIndex >= MAX_BMU_NUM)
		return;
	gmSystemParemater.mRomPar.u32NtcBusBarFlag[u8BmuIndex] = u32Flag;
	resetSysParIdleCount();
}

uint32_t LibSysParGetNtcOtherFlag(uint8_t BmuIndex)
{
	if(BmuIndex >= MAX_BMU_NUM)
		return 0;
	return gmSystemParemater.mRomPar.u32NtcOtherFlag[BmuIndex];	
}
void LibSysParSetNtcOtherFlag(uint8_t u8BmuIndex,uint32_t u32Flag)
{
	if(u8BmuIndex >= MAX_BMU_NUM)
		return;
	gmSystemParemater.mRomPar.u32NtcOtherFlag[u8BmuIndex] = u32Flag;
}

uint16_t LibSysParGetBmuPassiveBalR(void){
	return gmSystemParemater.mRomPar.u16BmuPassiveBalRVal;
}

void LibSysParSetBmuPassiveBalR(uint16_t u16Val){
	if(u16Val == 0){
		return; 
	}
	
	gmSystemParemater.mRomPar.u16BmuPassiveBalRVal = u16Val;
	resetSysParIdleCount();
}

//----------------------------------------------------------------
uint16_t LibSysParOpen(void)
{
	LibSysParSetBatteryInfoDefaultValue();
	//LibSysParLoadBatteryInfoPar();
	
	gu8SystemParIdleCount = 0;
	
	LibSysParSetDefaultRomValue();
	//LibSysParLoadSysPar();
	
	LibSysParCheckValidRomValue();
	LibSysParSetRamValue();
	if(LibSysParGetSystemActiveFlag() & SYS_ACTIVE_FLAG_CANCEL_IT_ALGORITHM){
		LibSysParDebugMsg("Cancel IT Algorithm");
	}
	return sizeof(tSysRomPar);	
}


/************************ (C) COPYRIGHT *****END OF FILE****/    

