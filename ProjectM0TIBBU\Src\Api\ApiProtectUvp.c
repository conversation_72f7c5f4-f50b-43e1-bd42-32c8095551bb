/*
******************************************************************************
* @file     ApiProtectUvp.c
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include "Define.h"
#include "HalAfe.h"
#include "ApiProtect.h"
#include "ApiProtectUvp.h"
#include "LibSysPar.h"

void AppSerialUartSendMessage(char *str);

/* Private define ------------------------------------------------------------*/
#define	API_PROTECT_UVP_DEBUG_MSG(c8Msg)	   AppSerialUartSendMessage(c8Msg)
#define	API_PROTECT_UVP_CHECK_NUM_PER_TIME	(20)
#define	API_PROTECT_UVP_GET_CELL_NUMBER		LibSysParGetCellNumber
#define	API_PROTECT_UVP_GET_MIN_CELL_VOLTAGE(u8Bmu, u8Posi, u16Cells)	HalAfeGetMinCellVoltage(u8Bmu, u8Posi, u16Cells)
#define	API_PROTECT_UVP_GET_UVP_PAR(u8ProtectLevel, mProtectPar) 		LibSysParGetUvpPar(u8ProtectLevel, mProtectPar)
#define	API_PROTECT_UVP_GET_UVP_PF_PAR(mProtectPar) 	LibSysParGetUvpPfPar(mProtectPar)
#define	API_PROTECT_UVP_GET_LEVEL_MASK(u8ProtectLevel, mProtectFlagValue)	ApiProtectGetLevelMask(u8ProtectLevel, mProtectFlagValue)
#define API_PROTECT_UVP_IS_OVP_PF_SET()		LibSysParIsUvpPfSet()
/* Private macro -------------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
typedef struct{
	uint8_t	u8Flag[MAX_CELL_NUMBER];
	uint8_t	u8SetCount[API_PROTECT_LEVEL][MAX_CELL_NUMBER];
	uint8_t	u8ReleaseCount[API_PROTECT_LEVEL][MAX_CELL_NUMBER];
	uint8_t	u8PfSetCount;
	uint8_t	u8PfFlag;
	tfpApiProtectEvtHandler  fpEvtHandler;
}tUvpProtect;

static tUvpProtect	gmUvpProtect={0};
static uint16_t	gu16UvpCellIndex;
static bool gbUvpEnable = 0;
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
static void ApiProjectUvpProtectIni(void)
{
	gu16UvpCellIndex = 0;
	if(API_PROTECT_UVP_IS_OVP_PF_SET() != 0)
		gmUvpProtect.u8PfFlag = 1;
}

/* Public function prototypes -----------------------------------------------*/
void ApiProtectUvpPfClean(void)
{
	gmUvpProtect.u8PfFlag = 0;
}

uint8_t	ApiProtectUvpPfGetFlag(void)
{
	return gmUvpProtect.u8PfFlag;
}

uint8_t	ApiProtectUvpGetFlag(uint16_t CellIndex)
{
	return gmUvpProtect.u8Flag[CellIndex];
}

uint8_t ApiProtectUvpHandler(uint8_t u8ProtectLevel)
{
	uint8_t				u8Checkcount = 0;
	uint16_t			u16CellVoltage;
	tProtectFlagValue	mProtectFlagValue;
	tScuProtectPar		mProtectPar;
	
	if (gbUvpEnable == 0)
	{
		return 1;
	}
	
	API_PROTECT_UVP_GET_UVP_PAR(u8ProtectLevel, &mProtectPar);
	API_PROTECT_UVP_GET_LEVEL_MASK(u8ProtectLevel, &mProtectFlagValue);

	while(1)
	{			
		u16CellVoltage = HalAfeGetCellVoltage(gu16UvpCellIndex);

		if(u16CellVoltage < mProtectPar.mSetValue.u32Value && mProtectPar.mSTime.u32Value != 0)
		{
			#if 0
			if(apiSystemFlagGetFlag1() & SYSTEM_FLAG1_SYSTEM_READY){
				halAfeStartOpenWireTest();
			}
			
			if(!halAfeIsAdcValid(gu16UvpCellIndex)){
				gmUvpProtect.u8Flag[gu16UvpCellIndex] &= mProtectFlagValue.u8ClearMask;
				gmUvpProtect.u8ReleaseCount[u8ProtectLevel][gu16UvpCellIndex] = 0;
				gmUvpProtect.u8SetCount[u8ProtectLevel][gu16UvpCellIndex] = 1;
				goto compareNext;
			}
			#endif
			if((gmUvpProtect.u8Flag[gu16UvpCellIndex] & mProtectFlagValue.u8Mask) == 0)
			{
				gmUvpProtect.u8Flag[gu16UvpCellIndex] &= mProtectFlagValue.u8ClearMask;
				gmUvpProtect.u8Flag[gu16UvpCellIndex] |= mProtectFlagValue.u8Setting;
				gmUvpProtect.u8SetCount[u8ProtectLevel][gu16UvpCellIndex] = 1;
			}
			else if((gmUvpProtect.u8Flag[gu16UvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
			{
				gmUvpProtect.u8SetCount[u8ProtectLevel][gu16UvpCellIndex]++;
				if(gmUvpProtect.u8SetCount[u8ProtectLevel][gu16UvpCellIndex] >= mProtectPar.mSTime.u32Value)
				{
					if(gmUvpProtect.fpEvtHandler)
					{
						gmUvpProtect.fpEvtHandler(0, kAPI_PROTECT_UVP_L1_SET + u8ProtectLevel, &gu16UvpCellIndex);	
					}
					gmUvpProtect.u8Flag[gu16UvpCellIndex] &= mProtectFlagValue.u8ClearMask;
					gmUvpProtect.u8Flag[gu16UvpCellIndex] |= mProtectFlagValue.u8Setted;
					gmUvpProtect.u8SetCount[u8ProtectLevel][gu16UvpCellIndex] = 0;
				}
			}
		}
		else  if((gmUvpProtect.u8Flag[gu16UvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setting)
		{
			gmUvpProtect.u8Flag[gu16UvpCellIndex] &= mProtectFlagValue.u8ClearMask;
		}
		//-------------------------------------------
		//release 
		if(u16CellVoltage > mProtectPar.mRelValue.u32Value && mProtectPar.mRTime.u32Value)
		{
			if((gmUvpProtect.u8Flag[gu16UvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Setted)
			{
				gmUvpProtect.u8Flag[gu16UvpCellIndex] &= mProtectFlagValue.u8ClearMask;
				gmUvpProtect.u8Flag[gu16UvpCellIndex] |= mProtectFlagValue.u8Releasing;
				gmUvpProtect.u8ReleaseCount[u8ProtectLevel][gu16UvpCellIndex] = 1;
			}
			else if((gmUvpProtect.u8Flag[gu16UvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
			{
				gmUvpProtect.u8ReleaseCount[u8ProtectLevel][gu16UvpCellIndex]++;
				if(gmUvpProtect.u8ReleaseCount[u8ProtectLevel][gu16UvpCellIndex] >= mProtectPar.mRTime.u32Value)
				{
					gmUvpProtect.u8Flag[gu16UvpCellIndex] &= mProtectFlagValue.u8ClearMask;
					gmUvpProtect.u8ReleaseCount[u8ProtectLevel][gu16UvpCellIndex] = 0;
					if(gmUvpProtect.fpEvtHandler)
					{
						gmUvpProtect.fpEvtHandler(0, kAPI_PROTECT_UVP_L1_RELEASE + u8ProtectLevel, &gu16UvpCellIndex);	
					}
				}
			}
		}
		else if((gmUvpProtect.u8Flag[gu16UvpCellIndex] & mProtectFlagValue.u8Mask) == mProtectFlagValue.u8Releasing)
		{
			gmUvpProtect.u8Flag[gu16UvpCellIndex] &= mProtectFlagValue.u8ClearMask;
			gmUvpProtect.u8Flag[gu16UvpCellIndex] |= mProtectFlagValue.u8Setted;
		}
compareNext:
		gu16UvpCellIndex++;
		if(gu16UvpCellIndex >= API_PROTECT_UVP_GET_CELL_NUMBER())
		{
			gu16UvpCellIndex = 0;
			return 1;
		}
		u8Checkcount++;
		if(u8Checkcount >= API_PROTECT_UVP_CHECK_NUM_PER_TIME)
			break;
	}
	return 0;
}


void ApiProtectUvpPfHandler(void)
{
	uint8_t		u8Bmu,u8Posi;
	uint16_t	u16Cells;
	uint16_t	u16Voltage;
	tScuProtectPar	mProtectPar;
	
	if (gbUvpEnable == 0)
	{
		return;
	}
	
	if(gmUvpProtect.u8PfFlag)
		return;
	API_PROTECT_UVP_GET_UVP_PF_PAR(&mProtectPar);
	u16Voltage = API_PROTECT_UVP_GET_MIN_CELL_VOLTAGE(&u8Bmu, &u8Posi, &u16Cells);
	if(u16Voltage < mProtectPar.mSetValue.u32Value && mProtectPar.mSTime.u32Value)
	{
		gmUvpProtect.u8PfSetCount++;
		if(gmUvpProtect.u8PfSetCount >= mProtectPar.mSTime.u32Value)
		{
			gmUvpProtect.u8PfFlag = 1;	
			if(gmUvpProtect.fpEvtHandler)
			{
				gmUvpProtect.fpEvtHandler(0, kAPI_PROTECT_UVP_PF, &u16Cells);
			}
		}
	}
	else
		gmUvpProtect.u8PfSetCount = 0;
}

void ApiProtectUvpOpen(tfpApiProtectEvtHandler fpEvtHandler)
{
	ApiProjectUvpProtectIni();
	
	gbUvpEnable = 1;
	
	gmUvpProtect.fpEvtHandler = fpEvtHandler;
}

/************************ (C) COPYRIGHT *****END OF FILE****/    

