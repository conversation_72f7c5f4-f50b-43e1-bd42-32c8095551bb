/**
  ******************************************************************************
  * @file        AppSerialCanDavinciDebug.c
  * <AUTHOR>
  * @version     v0.0.1
  * @date        2021/10/28
  * @brief       
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2021 Johnny</center></h2>
  *
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include "Define.h"
#include "Main.h"
#include "LibFunctionReturnValueDefine.h"

#include "HalAfe.h"

#include "HalCan.h"

#include "LibSoftwareTimerHandler.h"
#include "AppSerialCanDavinci.h"
//#include "ApiSysPar.h"
//#include "LibCalibration.h"
//#include "HalAfeADS7946.h"
//#include "HalBsp.h"
//#include "AppProject.h"
//#include "AppAlg.h"
//#include "ApiIRMonitoring.h"
//#include "ApiScuTemp.h"
#include "LibNtc.h"
//#include "ApiRelayControl.h"
//#include "smp_w5500_DMA.h"
//#include "HalAfeLtc68xx.h"
//#include "ApiModbusTCPIP.h"


void appSerialCanDavinciSendTextMessage(char *str);
#define	appSerialCanDavinciDebugMsg(str)	appSerialCanDavinciSendTextMessage(str)
/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define		canDbgScuId()		appProjectGetScuId()

/* Private macro -------------------------------------------------------------*/
/* Public variables ---------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/

/* Public function prototypes -----------------------------------------------*/
int32_t appCurrDebug(uint8_t CurrentIndex, int32_t adc);
int32_t appVbatDebug(uint8_t VbatIndex, int32_t adc);

static void DavinciCanDebugModeChange(tHalCanFrame *pCanPkg)
{
	tHalCanFrame	CanPkg;
	uint16_t	subindex;
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	if(subindex == SMP_SIMU_MODE_SUB_INDEX)
	{
		if(memcmp(pCanPkg->tUnionData.u8Data, "EnSimuM", 7) == 0)
		{
			#if MAO_DISSABLE
			appProjectEnableSimuMode();
			#endif
		}
		else if(memcmp(pCanPkg->tUnionData.u8Data, "ExSimuM", 7) == 0)
		{
			#if MAO_DISSABLE
			appProjectDisableSimuMode();
			#endif
		}
		else
			return;
	}
	else if(subindex == SMP_ENG_MODE_SUB_INDEX)
	{
		if(memcmp(pCanPkg->tUnionData.u8Data, "EnterEng", 8) == 0)
		{
			#if MAO_DISSABLE
			appProjectEnableEngMode();
			#endif
			appSerialCanDavinciDebugMsg("Enter Eng. Mode");
		}
		else if(memcmp(pCanPkg->tUnionData.u8Data, "ExitEngM", 8) == 0)
		{
			#if MAO_DISSABLE
			appProjectDisableEngMode();
			#endif
			appSerialCanDavinciDebugMsg("Exit Eng. Mode");
		}
		else
			return;
	}
	else
		return;
	CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_TX, canDbgScuId(),
									SMP_DEBUG_MODE_CONTROL_OBJ_INDEX,
									subindex);
	CanPkg.u8Dlc = 0;								
	appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
}

static void DavinciCanDebugCurrentAdc(tHalCanFrame *pCanPkg)
{
	int32_t	adc;
	char	str[100];
	
	adc = (int32_t)(GET_WORD(&pCanPkg->tUnionData.u8Data[0]));

	HalAfeSetCurrentAdcValue(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id), adc);

	sprintf(str,"adc:%d I = %d", adc, appCurrDebug(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id), adc));
	appSerialCanDavinciDebugMsg(str);
}
static void DavinciCanDebugVbatAdc(tHalCanFrame *pCanPkg)
{
	int32_t	adc;
	char	str[100];
	
	adc = (int32_t)(GET_WORD(&pCanPkg->tUnionData.u8Data[0]));

	HalAfeSetVBatAdcValue(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id), adc);

	sprintf(str,"adc:%d V = %d", adc, appVbatDebug(SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id), adc));
	appSerialCanDavinciDebugMsg(str);
}

/// [CH] : 用CAN設定電壓
static void DavinciCanDebugCellVSimu(tHalCanFrame *pCanPkg)
{
	uint8_t		i;
	uint16_t	subindex, voltage;
//	char	str[100];
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	for(i=0; i<4 ; i++)
	{
		voltage = GET_WORD(&pCanPkg->tUnionData.u8Data[i*2]);
		HalAfeSetCellVoltage(subindex++, voltage);
	}
}


static void DavinciCanDebugNtcVSimu(tHalCanFrame *pCanPkg)
{
	uint8_t		i;
	uint16_t	subindex, voltage;
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	for(i=0; i<4 ; i++)
	{
		voltage = GET_WORD(&pCanPkg->tUnionData.u8Data[i*2]);
		HalAfeSetNtcVoltage(subindex++, voltage);
	}

}
static void DavinciCanDebugNtcTSimu(tHalCanFrame *pCanPkg)
{
	uint8_t		i;
	uint16_t	subindex, voltage;
	int16_t		temp;
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	for(i=0; i<8 ; i++)
	{
		temp = (int16_t)pCanPkg->tUnionData.u8Data[i];		
		voltage = LibTemperatureToVoltage(temp);
		
		HalAfeSetNtcVoltage(subindex++, voltage);
	}
}

static void DavinciCanDebugCurrentSimu(tHalCanFrame *pCanPkg)
{
	uint16_t	subindex;
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	if(subindex == 0)
	{
		HalAfeSetCurrentValue(0, (int32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]));
	}
	else if(subindex == 1)
	{
		HalAfeSetCurrentValue(1, (int32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]));
	}
}

static void DavinciCanDebugGpio(tHalCanFrame *pCanPkg)
{
	uint32_t	mask;
	uint32_t	dat;
	uint16_t	subindex;
	char		*ptr;
	tHalCanFrame	CanPkg;

	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	#if MAO_DISSABLE
	if(subindex & 0x200)	//get gpio name
	{
		mask = 1 << ((subindex&0x1f0)/ 0x10);
		ptr = halBspGetGpioControlMsg(subindex&0x0f, mask);
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_TX, canDbgScuId(),
							SMP_DEBUG_GPIO_OBJ_INDEX,
							subindex);
		CanPkg.u8Dlc = 8;
		LibFifoMemcpy(CanPkg.tUnionData.u8Data, ptr, 8);
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);	
	}
	else if(appProjectIsInEngMode() == true)
	{
		mask = (uint32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]);
		dat = (uint32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[4]);		
		halBspGpioControl(subindex, mask, dat);
		
		CanPkg.u8Dlc = 0;
		CanPkg.u32Id = MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_TX, canDbgScuId(),
							SMP_DEBUG_GPIO_OBJ_INDEX,
							subindex);
		appSerialCanDavinciPutPkgToCanFifo(&CanPkg);
	}
	#endif
}


static void DavinciCanDebugVBatSimu(tHalCanFrame *pCanPkg)
{
	uint16_t	subindex;
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	if(subindex == 0)
	{
		HalAfeSetVBatVoltage(kAFE_VBAT_INDEX, (uint32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]));
	}
	else if(subindex == 1)
	{
		HalAfeSetVBatVoltage(kAFE_VPACK_INDEX, (uint32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]));
	}
	else if(subindex == 2)
	{
		HalAfeSetVBatVoltage(kAFE_VBCAL_INDEX, (uint32_t)GET_DWORD(&pCanPkg->tUnionData.u8Data[0]));
	}
}
static void DavinciCanDebugRelayControl(tHalCanFrame *pCanPkg)
{
	uint16_t	subindex;
	#if MAO_DISSABLE
	if(appProjectIsInEngMode() == 0)
		return;
	#endif
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);

	switch(subindex)
	{
	case SMP_PS_RELAY_SUB_INDEX:
		break;
	case SMP_P_MAIN_RELAY_SUB_INDEX:
		break;
	case SMP_N_MAIN_RELAY_SUB_INDEX:

		break;
	case SMP_PRE_RELAY_SUB_INDEX:
		break;
	case SMP_FAN_RELAY_SUB_INDEX:
		break;
	}
}

void halAfeClearTestCount(void);



void apiEkfParSaveEkfRunningStatusForTest(void);
void apiEkfParLoadEkfRunningStatusForTest(void);



static void forTestDebugButton(tHalCanFrame *pCanPkg)
{
//	uint8_t		i;
	uint16_t	subindex;
//	char	str[100];
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	
	switch(subindex)
	{
	case 0:
		apiEkfParSaveEkfRunningStatusForTest();
		appSerialCanDavinciDebugMsg("Debug 0");
		break;
	case 1:
		apiEkfParLoadEkfRunningStatusForTest();
		appSerialCanDavinciDebugMsg("Debug 1");
		break;
	case 2:
		appSerialCanDavinciDebugMsg("Debug 2");
		break;
	case 3:
		appSerialCanDavinciDebugMsg("Debug 3");
		break;
	case 4:
		#if MAO_DISSABLE
		SetSaveEkfStatus();
		#endif
		appSerialCanDavinciDebugMsg("Debug 4");
		break;
	case 5:
		appSerialCanDavinciDebugMsg("Debug 5");
		break;
	case 6:
		appSerialCanDavinciDebugMsg("Debug 6");
		break;
	case 7:
		appSerialCanDavinciDebugMsg("Debug 7");
		break;
	}
}

static void DavinciCanDebugBroadcastMsgTimerHandle(tHalCanFrame *pCanPkg){
	uint16_t	subindex = 0;
	tUnion16Bits		sec = {0};
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);
	#if MAO_DISSABLE
	sec.u16 = GET_WORD(&pCanPkg->tUnionData.u8Data[0]);
	switch(subindex){
		case SMP_W5500_MSGBROADCAST_CTRL_SUB_INDEX:
			smp_W5500_SetBroadcastMsgTimer(sec.u16);
			break;
		case SMP_LTCAFE_MSGBROADCAST_CTRL_SUB_INDEX:
			halAfeSetBroadcasrMsgTimer(sec.u16);
			break;
		case SMP_TCPIPREMOTEIP_MSGBROADCAST_CTRL_SUB_INDEX:
			appTcpipSmpSetBroadcasrMsgTimer(sec.u16);
			break;
	}
	#endif

}

// =============================================================================	

static void DavinciCanDebugReceiveCellVISimu(tHalCanFrame *pCanPkg)
{
	uint8_t		i;
	uint16_t	subindex, voltage;
	
	
	subindex = SMP_CAN_GET_SUB_INDEX(pCanPkg -> u32Id);


	for (i = 0; i < 4; i++)
	{
		voltage = GET_WORD(&pCanPkg->tUnionData.u8Data[i*2]);
		gu16CellVoltageBuffer[subindex++] = voltage;
	}
}

// =============================================================================	

void appAlgDebug(uint8_t *pBuf, uint8_t len);

static void DavinciCanDebugRx(tHalCanFrame *pCanPkg)
{
	//appSerialCanDavinciDebugMsg("DavinciCanDebugRx");
//	appAlgDebug(pCanPkg->tUnionData.u8Data, pCanPkg -> u8Dlc);
}

SMP_CAN_DECODE_CMD_START(mDavinciDebugCanDecodeTab)
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_MODE_CONTROL_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugModeChange)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_SIMU_CURR_ADC_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugCurrentAdc)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_SIMU_VBAT_ADC_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugVbatAdc)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_PKG_RX_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugRx)



	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_CELLV_SIMU_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugCellVSimu)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_NTCV_SIMU_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugNtcVSimu)
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_NTCT_SIMU_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugNtcTSimu)


	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_GPIO_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugGpio)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_SIMU_CURRENT_VALUE_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugCurrentSimu)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_SIMU_VBAT_VALUE_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugVBatSimu)

	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_RELAY_CONTROL_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugRelayControl)
								
	                                /// [CH] : 0x80                        						
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_CLEAR_TEST_COUNT_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugReceiveCellVISimu)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_FOR_TEST_BUTTON_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								forTestDebugButton)
								
	SMP_CAN_DECODE_CMD_CONTENT(	MAKE_SMP_CAN_ID(SMP_CAN_FUN_DEBUG_RX, 0,
									SMP_DEBUG_BROADCASTMSG_OBJ_INDEX,
									0),
								CHECK_SMP_CAN_OBJ,
								DavinciCanDebugBroadcastMsgTimerHandle)

SMP_CAN_DECODE_CMD_END();


void DavinciCanFunDebugRx(tHalCanFrame *pCanPkg)
{
//	uint8_t	i;
	uint8_t cmdIndex;
	
//	char	str[100];
//	char	str1[100];
 	cmdIndex = 0;
	//appSerialCanDavinciDebugMsg("Decode Debug Fun");

	for(cmdIndex = 0; mDavinciDebugCanDecodeTab[cmdIndex].fun != 0; cmdIndex++)
	{
		if((mDavinciDebugCanDecodeTab[cmdIndex].canid & mDavinciDebugCanDecodeTab[cmdIndex].mask) == 
		   (mDavinciDebugCanDecodeTab[cmdIndex].mask & pCanPkg -> u32Id)  &&
		   appSerialCanDavinciIsCorrectScuId(pCanPkg))
		{
		//	sprintf(str,"Debug ID= %d",SMP_CAN_GET_SCU_ID(pCanPkg -> u32Id));
		//	appSerialCanDavinciDebugMsg(str);
			mDavinciDebugCanDecodeTab[cmdIndex].fun(pCanPkg);
			break; 		
 		}
	}
}

/************************ (C) COPYRIGHT Johnny Wang *****END OF FILE****/    




