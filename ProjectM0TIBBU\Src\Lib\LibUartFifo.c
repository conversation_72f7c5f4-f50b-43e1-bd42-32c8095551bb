/*
******************************************************************************
* @file     LibUartFifo.c
* <AUTHOR>
* @brief    This file include MSPM0G3519 UART FIFO Library Function.

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
/* Includes -----------------------------------------------------------------*/
#include "LibUartFifo.h"
#include "LibFunctionReturnValueDefine.h"
#include "HalUart.h" 

/* Local typedef ------------------------------------------------------------*/
/* Local define -------------------------------------------------------------*/
/* Local macro --------------------------------------------------------------*/
/* Local function declare ---------------------------------------------------*/
/* Global variables ---------------------------------------------------------*/
/* Local function prototypes ------------------------------------------------*/

/* Global function prototypes -----------------------------------------------*/
int8_t LibUartFifoPush(tLibUartFifoStatus* pmUartFifo, uint8_t u8Value)
{
	uint16_t u16NextPosi;


	//HalGpioTogglePin(GPIOB, DL_GPIO_PIN_29);
	u16NextPosi = (pmUartFifo->u16FifoPushInPosi + 1);

	if (u16NextPosi >= pmUartFifo->u16FifoSize)
	{
		u16NextPosi = 0;
	}

	if (u16NextPosi == pmUartFifo->u16FifoPopOutPosi)
	{
		return RES_ERROR_NULL; //Buf full
	}
	else 
	{
		pmUartFifo->pu8FifoStartAddr[pmUartFifo->u16FifoPushInPosi] = u8Value;
		pmUartFifo->u16FifoPushInPosi = u16NextPosi;
	}
	//HalGpioTogglePin(GPIOB, DL_GPIO_PIN_29);
	return RES_SUCCESS;
}

int8_t LibUartFifoPop(tLibUartFifoStatus* pmUartFifo, uint8_t* pu8Value)
{
	uint16_t u16NextPosi;

	
	u16NextPosi = (pmUartFifo->u16FifoPopOutPosi + 1);
	
	if (u16NextPosi >= pmUartFifo->u16FifoSize)
	{
		u16NextPosi = 0;
	}

	if (pmUartFifo->u16FifoPushInPosi == pmUartFifo->u16FifoPopOutPosi)
    {
		return RES_ERROR_RESOURCES; //Buf empty
	}
    else
    {
		//HalGpioTogglePin(GPIOB, DL_GPIO_PIN_30);
		*pu8Value = pmUartFifo->pu8FifoStartAddr[pmUartFifo->u16FifoPopOutPosi];
		pmUartFifo->u16FifoPopOutPosi = u16NextPosi;
	}		
	//HalGpioTogglePin(GPIOB, DL_GPIO_PIN_30);
	return RES_SUCCESS;
}

int8_t LibUartFifoGetUsedSize(tLibUartFifoStatus* pmUartFifo, uint16_t *pu16UsedSize)
{
    if (pmUartFifo->u16FifoPushInPosi == pmUartFifo->u16FifoPopOutPosi)
    {
		return RES_ERROR_RESOURCES; //Buf empty
	}
    else if (pmUartFifo->u16FifoPushInPosi > pmUartFifo->u16FifoPopOutPosi)
    {
		*pu16UsedSize = (pmUartFifo->u16FifoPushInPosi - pmUartFifo->u16FifoPopOutPosi);
	}
    else
    {
		*pu16UsedSize = ( (pmUartFifo->u16FifoSize - pmUartFifo->u16FifoPopOutPosi) + (pmUartFifo->u16FifoPushInPosi) );
	}	
	return RES_SUCCESS;
}