/*
******************************************************************************
* @file     ApiProtectEvent.h
* <AUTHOR>
* @brief    

******************************************************************************
* @attention
*
* COPYRIGHT(c) 2025 FW Team</center>
******************************************************************************
*/
  
#ifndef _API_PROTECT_EVENT_H_
#define _API_PROTECT_EVENT_H_

typedef enum{
	kAPI_PROTECT_OVP_L1_SET = 1,
	kAPI_PROTECT_OVP_L2_SET,
	kAPI_PROTECT_OVP_L3_SET,
	kAPI_PROTECT_OVP_L1_RELEASE,
	kAPI_PROTECT_OVP_L2_RELEASE,
	kAPI_PROTECT_OVP_L3_RELEASE,

	kAPI_PROTECT_UVP_L1_SET,
	kAPI_PROTECT_UVP_L2_SET,
	kAPI_PROTECT_UVP_L3_SET,
	kAPI_PROTECT_UVP_L1_RELEASE,
	kAPI_PROTECT_UVP_L2_RELEASE,
	kAPI_PROTECT_UVP_L3_RELEASE,
	
	kAPI_PROTECT_CDVP_L1_SET,
	kAPI_PROTECT_CDVP_L2_SET,
	kAPI_PROTECT_CDVP_L3_SET,
	kAPI_PROTECT_CDVP_L1_RELEASE,
	kAPI_PROTECT_CDVP_L2_RELEASE,
	kAPI_PROTECT_CDVP_L3_RELEASE,
	kAPI_PROTECT_CDVP_PF,
	
	kAPI_PROTECT_MDVP_L1_SET,
	kAPI_PROTECT_MDVP_L2_SET,
	kAPI_PROTECT_MDVP_L3_SET,
	kAPI_PROTECT_MDVP_L1_RELEASE,
	kAPI_PROTECT_MDVP_L2_RELEASE,
	kAPI_PROTECT_MDVP_L3_RELEASE,
	kAPI_PROTECT_MDVP_PF,

	kAPI_PROTECT_CDTP_L1_SET,
	kAPI_PROTECT_CDTP_L2_SET,
	kAPI_PROTECT_CDTP_L3_SET,
	kAPI_PROTECT_CDTP_L1_RELEASE,
	kAPI_PROTECT_CDTP_L2_RELEASE,
	kAPI_PROTECT_CDTP_L3_RELEASE,
	
	kAPI_PROTECT_MDTP_L1_SET,
	kAPI_PROTECT_MDTP_L2_SET,
	kAPI_PROTECT_MDTP_L3_SET,
	kAPI_PROTECT_MDTP_L1_RELEASE,
	kAPI_PROTECT_MDTP_L2_RELEASE,
	kAPI_PROTECT_MDTP_L3_RELEASE,

	kAPI_PROTECT_COTP_L1_SET,
	kAPI_PROTECT_COTP_L2_SET,
	kAPI_PROTECT_COTP_L3_SET,
	kAPI_PROTECT_COTP_L4_SET,
	kAPI_PROTECT_COTP_L1_RELEASE,
	kAPI_PROTECT_COTP_L2_RELEASE,
	kAPI_PROTECT_COTP_L3_RELEASE,
	kAPI_PROTECT_COTP_L4_RELEASE,		

	kAPI_PROTECT_CUTP_L1_SET,
	kAPI_PROTECT_CUTP_L2_SET,
	kAPI_PROTECT_CUTP_L3_SET,
	kAPI_PROTECT_CUTP_L4_SET,
	kAPI_PROTECT_CUTP_L1_RELEASE,		
	kAPI_PROTECT_CUTP_L2_RELEASE,
	kAPI_PROTECT_CUTP_L3_RELEASE,
	kAPI_PROTECT_CUTP_L4_RELEASE,
	
	kAPI_PROTECT_DOTP_L1_SET,
	kAPI_PROTECT_DOTP_L2_SET,		
	kAPI_PROTECT_DOTP_L3_SET,
	kAPI_PROTECT_DOTP_L1_RELEASE,
	kAPI_PROTECT_DOTP_L2_RELEASE,
	kAPI_PROTECT_DOTP_L3_RELEASE,

	kAPI_PROTECT_DUTP_L1_SET,
	kAPI_PROTECT_DUTP_L2_SET,		
	kAPI_PROTECT_DUTP_L3_SET,
	kAPI_PROTECT_DUTP_L1_RELEASE,
	kAPI_PROTECT_DUTP_L2_RELEASE,
	kAPI_PROTECT_DUTP_L3_RELEASE,

	kAPI_PROTECT_DOCP_L1_SET,		
	kAPI_PROTECT_DOCP_L2_SET,
	kAPI_PROTECT_DOCP_L3_SET,
	kAPI_PROTECT_DOCP_L4_SET,
	kAPI_PROTECT_DOCP_L1_RELEASE,
	kAPI_PROTECT_DOCP_L2_RELEASE,
	kAPI_PROTECT_DOCP_L3_RELEASE,
	kAPI_PROTECT_DOCP_L4_RELEASE,
	
	kAPI_PROTECT_COCP_L1_SET,
	kAPI_PROTECT_COCP_L2_SET,	
	kAPI_PROTECT_COCP_L3_SET,
	kAPI_PROTECT_COCP_L4_SET,
	kAPI_PROTECT_COCP_L1_RELEASE,
	kAPI_PROTECT_COCP_L2_RELEASE,
	kAPI_PROTECT_COCP_L3_RELEASE,
	kAPI_PROTECT_COCP_L4_RELEASE,

	kAPI_PROTECT_OVP_PF,
	kAPI_PROTECT_UVP_PF,
	
	kAPI_PROTECT_RLY1_OT_L1_SET,
	kAPI_PROTECT_RLY1_OT_L2_SET,		
	kAPI_PROTECT_RLY1_OT_L3_SET,
	kAPI_PROTECT_RLY1_OT_L1_RELEASE,
	kAPI_PROTECT_RLY1_OT_L2_RELEASE,
	kAPI_PROTECT_RLY1_OT_L3_RELEASE,

	kAPI_PROTECT_RLY2_OT_L1_SET,
	kAPI_PROTECT_RLY2_OT_L2_SET,
	kAPI_PROTECT_RLY2_OT_L3_SET,
	kAPI_PROTECT_RLY2_OT_L1_RELEASE,
	kAPI_PROTECT_RLY2_OT_L2_RELEASE,
	kAPI_PROTECT_RLY2_OT_L3_RELEASE,

	kAPI_PROTECT_AMBI_OT_L1_SET,
	kAPI_PROTECT_AMBI_OT_L2_SET,
	kAPI_PROTECT_AMBI_OT_L3_SET,
	kAPI_PROTECT_AMBI_OT_L1_RELEASE,
	kAPI_PROTECT_AMBI_OT_L2_RELEASE,
	kAPI_PROTECT_AMBI_OT_L3_RELEASE,

	kAPI_PROTECT_BUSBAR_P_OT_L1_SET,
	kAPI_PROTECT_BUSBAR_P_OT_L2_SET,
	kAPI_PROTECT_BUSBAR_P_OT_L3_SET,
	kAPI_PROTECT_BUSBAR_P_OT_L1_RELEASE,
	kAPI_PROTECT_BUSBAR_P_OT_L2_RELEASE,
	kAPI_PROTECT_BUSBAR_P_OT_L3_RELEASE,

	kAPI_PROTECT_BUSBAR_N_OT_L1_SET,
	kAPI_PROTECT_BUSBAR_N_OT_L2_SET,
	kAPI_PROTECT_BUSBAR_N_OT_L3_SET,
	kAPI_PROTECT_BUSBAR_N_OT_L1_RELEASE,
	kAPI_PROTECT_BUSBAR_N_OT_L2_RELEASE,
	kAPI_PROTECT_BUSBAR_N_OT_L3_RELEASE,

	kAPI_PROTECT_RP_URP_L1_SET,
	kAPI_PROTECT_RP_URP_L2_SET,
	kAPI_PROTECT_RP_URP_L3_SET,
	kAPI_PROTECT_RP_URP_L1_RELEASE,
	kAPI_PROTECT_RP_URP_L2_RELEASE,
	kAPI_PROTECT_RP_URP_L3_RELEASE,

	kAPI_PROTECT_RN_URP_L1_SET,
	kAPI_PROTECT_RN_URP_L2_SET,
	kAPI_PROTECT_RN_URP_L3_SET,
	kAPI_PROTECT_RN_URP_L1_RELEASE,
	kAPI_PROTECT_RN_URP_L2_RELEASE,
	kAPI_PROTECT_RN_URP_L3_RELEASE,

	kAPI_PROTECT_DIP_L1_SET,
	kAPI_PROTECT_DIP_L2_SET,
	kAPI_PROTECT_DIP_L3_SET,
	kAPI_PROTECT_DIP_L1_RELEASE,
	kAPI_PROTECT_DIP_L2_RELEASE,
	kAPI_PROTECT_DIP_L3_RELEASE,

	kAPI_PROTECT_VB_DVP_L1_SET,
	kAPI_PROTECT_VB_DVP_L2_SET,
	kAPI_PROTECT_VB_DVP_L3_SET,
	kAPI_PROTECT_VB_DVP_L1_RELEASE,
	kAPI_PROTECT_VB_DVP_L2_RELEASE,
	kAPI_PROTECT_VB_DVP_L3_RELEASE,

	kAPI_PROTECT_CELL_OWP_SET,
	kAPI_PROTECT_CELL_OWP_RELEASE,
	
	kAPI_PROTECT_EVENT_END
}eTypeApiProtectEvent;


#endif /* _API_PROTECT_EVENT_H_ */


/************************ (C) COPYRIGHT *****END OF FILE****/    
